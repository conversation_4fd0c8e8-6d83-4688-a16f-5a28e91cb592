import numpy as np
import matplotlib.pyplot as plt
from itertools import combinations
from scipy.spatial.distance import pdist, squareform

# --- 1. 数据输入 ---
# 点 0=厂区, 1–30=收集点
coords = np.array([
    [ 0,  0],[12,  8],[ 5, 15],[20, 30],[25, 10],[35, 22],[18,  5],[30, 35],
    [10, 25],[22, 18],[38, 15],[ 5,  8],[15, 32],[28,  5],[30, 12],[10, 10],
    [20, 20],[35, 30],[ 8, 22],[25, 25],[32,  8],[15,  5],[28, 20],[38, 25],
    [10, 30],[20, 10],[30, 18],[ 5, 25],[18, 30],[35, 10],[22, 35]
])
w_all = np.array([
    [0,0,0,0],
    [0.72,0.12,0.06,0.30],[1.38,0.23,0.05,0.64],[1.08,0.18,0.04,0.50],
    [1.55,0.31,0.06,1.18],[1.62,0.27,0.05,0.76],[1.76,0.384,0.096,0.96],
    [0.77,0.168,0.042,0.42],[1.02,0.238,0.068,0.374],[1.32,0.176,0.044,0.66],
    [1.45,0.30,0.075,0.675],[1.35,0.27,0.108,0.972],[1.87,0.51,0.068,0.952],
    [2.58,0.516,0.129,1.075],[1.134,0.21,0.063,0.693],[0.78,0.13,0.065,0.325],
    [0.768,0.192,0.080,0.56],[0.72,0.27,0.090,0.72],[1.595,0.348,0.087,0.87],
    [1.50,0.36,0.090,1.05],[1.08,0.18,0.090,0.45],[0.912,0.19,0.038,0.76],
    [0.90,0.195,0.075,0.33],[0.99,0.27,0.072,0.468],[1.44,0.24,0.048,0.672],
    [1.74,0.319,0.116,0.725],[1.17,0.39,0.130,0.91],[1.70,0.34,0.170,1.19],
    [2.64,0.66,0.044,1.056],[0.864,0.216,0.072,0.648],[0.986,0.204,0.085,0.425]
])#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
# 中转站 31–35 坐标
st_coords = np.array([[12,5],[7,28],[20,8],[30,15],[25,10]])
Tj = 10000  # 建设成本 (元/天)
# 车辆参数
Q = [8,6,3,10]
C = [2.5,2.0,5.0,1.8]

# --- 2. 构建距离矩阵 ---
all_coords = np.vstack((coords, st_coords))
D = squareform(pdist(all_coords))

n = 30
m = 5

best_cost = np.inf
best_y = None#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
best_assign = None
best_routes = None

# --- 3. 枚举所有非空站点子集 ---
for r in range(1, m+1):
    for subset in combinations(range(m), r):
        y = np.zeros(m, bool)
        y[list(subset)] = True
        build_cost = y.sum() * Tj

        # 分配到最近启用站
        assign = np.zeros((n,4), int)
        for i in range(n):
            for k in range(4):
                dists = D[i+1, 31:31+m]
                # 只考虑启用站
                idx = np.where(y)[0]#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
                j = idx[np.argmin(dists[y])]
                assign[i,k] = j

        # 路径生成与运输成本
        trans_cost = 0
        routes = [[[] for _ in range(4)] for __ in range(m)]
        for s in range(m):
            j = 31 + s
            for k in range(4):
                pts = np.where(assign[:,k]==s)[0]
                unserved = list(pts)
                while unserved:
                    curr = j; load = 0; route = [j]
                    while True:
                        feasible = [i for i in unserved if load + w_all[i+1,k] <= Q[k]]
                        if not feasible: break
                        next_i = min(feasible, key=lambda ii: D[curr, ii+1])
                        route.append(next_i)
                        load += w_all[next_i+1,k]
                        curr = next_i
                        unserved.remove(next_i)
                    route.append(j)
                    for t in range(len(route)-1):
                        trans_cost += C[k]*D[route[t], route[t+1]]
                    routes[s][k].append(route)

        total_cost = build_cost + trans_cost
        if total_cost < best_cost:
            best_cost = total_cost
            best_y = y.copy()
            best_assign = assign.copy()
            best_routes = routes

# --- 4. 输出结果 ---
print("最优中转站:", [31+s for s in np.where(best_y)[0]])
print(f"最小总成本: {best_cost:.2f} 元")
for s in np.where(best_y)[0]:
    print(f"\n--- 中转站 {31+s} 路线 ---")
    for k in range(4):
        for vi, rt in enumerate(best_routes[s][k],1):
            print(f" 类型{k+1} 车{vi}: {rt}")

# --- 5. 精美可视化 ---
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# 5.1 建设成本 vs 站点数
plt.figure(figsize=(6,4))
plt.bar([sum(best_y)], [best_cost-best_y.sum()*Tj], color='coral', label='运输成本')
plt.bar([sum(best_y)], [best_y.sum()*Tj], bottom=[best_cost-best_y.sum()*Tj],
        color='skyblue', label='建设成本')
plt.xticks([sum(best_y)], [f"{sum(best_y)}站"], fontsize=12)
plt.ylabel('成本 (元)'), plt.title('最优子集成本拆分')
plt.legend(), plt.grid(axis='y')

# 5.2 全局路线图
plt.figure(figsize=(8,6))
plt.scatter(coords[:,0], coords[:,1], c='gray', s=40, label='收集点')
plt.scatter(coords[0,0], coords[0,1], c='k', s=100, marker='s', label='厂区')
plt.scatter(st_coords[:,0], st_coords[:,1], c='r', s=100, marker='^', label='中转站')
linestyles = ['-','--',':','-.']
for s in np.where(best_y)[0]:
    for k in range(4):
        for rt in best_routes[s][k]:
            xs, ys = zip(*[all_coords[i] for i in rt])
            plt.plot(xs, ys, linestyle=linestyles[k], linewidth=1.5,
                     label=f"S{31+s}-T{k+1}")
plt.title('全局中转站运输路线')
plt.xlabel('X (km)'), plt.ylabel('Y (km)')
plt.legend(bbox_to_anchor=(1.05,1), loc='upper left'), plt.grid(True)
plt.tight_layout()

# 5.3 各站点服务量柱状
plt.figure(figsize=(6,4))
served = [np.sum(best_assign[:,k]==s) for s in np.where(best_y)[0] for k in range(4)]
plt.bar(range(len(served)), served, color='seagreen')
plt.xticks(range(len(served)),
           [f"S{31+s}-T{k+1}" for s in np.where(best_y)[0] for k in range(4)],
           rotation=45, ha='right')
plt.ylabel('服务点数'), plt.title('各站点各类型服务量'), plt.grid(axis='y')

plt.show()
