%% 非对称距离矩阵构建函数
function [dist_matrix, time_matrix] = build_asymmetric_distance_matrix(...
    all_coords, constraints, total_nodes)
    
    % 初始化对称距离矩阵
    dist_matrix = zeros(total_nodes, total_nodes);
    time_matrix = -ones(total_nodes, total_nodes); % -1表示无时间限制
    
    % 计算基础欧几里得距离
    for i = 1:total_nodes
        for j = 1:total_nodes
            if i ~= j
                dist_matrix(i,j) = sqrt((all_coords(i,1) - all_coords(j,1))^2 + ...
                                      (all_coords(i,2) - all_coords(j,2))^2);
            end
        end
    end
    
    % 应用非对称约束
    fprintf('  应用非对称约束:\n');
    for k = 1:size(constraints, 1)
        from_node = constraints(k, 1);
        to_node = constraints(k, 2);
        distance = constraints(k, 3);
        time_start = constraints(k, 4);
        time_end = constraints(k, 5);
        
        % 转换节点编号为矩阵索引
        if from_node == 0
            from_idx = 1; % 处理厂
        elseif from_node <= 30
            from_idx = from_node + 1; % 收集点
        else
            from_idx = from_node - 30 + 31; % 中转站，31是第一个中转站的编号
        end
        
        if to_node == 0
            to_idx = 1;
        elseif to_node <= 30
            to_idx = to_node + 1;
        else
            to_idx = to_node - 30 + 31;
        end
        
        % 检查索引有效性
        if from_idx > 0 && from_idx <= total_nodes && to_idx > 0 && to_idx <= total_nodes
            dist_matrix(from_idx, to_idx) = distance;
            time_matrix(from_idx, to_idx) = time_start; % 存储禁行开始时间
            
            fprintf('    约束 %d->%d: 距离%.1fkm', from_node, to_node, distance);
            if time_start > 0
                fprintf(', 禁行时间%d:00-%d:00', time_start, time_end);
            end
            fprintf('\n');
        end
    end
end

