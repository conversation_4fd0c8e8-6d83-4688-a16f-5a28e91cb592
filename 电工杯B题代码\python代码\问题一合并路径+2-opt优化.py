import numpy as np
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt
import itertools

# Input data: 0 is depot, 1..n are customers
# Columns: x, y, demand
data = np.array([
    [0,   0,  0],
    [12,  8,  1.2],
    [5,   15, 2.3],
    [20,  30, 1.8],
    [25,  10, 3.1],
    [35,  22, 2.7],
    [18,   5, 1.5],
    [30,  35, 2.9],
    [10,  25, 1.1],
    [22,  18, 2.4],#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    [38,  15, 3.0],
    [5,    8, 1.7],
    [15,  32, 2.1],
    [28,   5, 3.2],
    [30,  12, 2.6],
    [10,  10, 1.9],
    [20,  20, 2.5],
    [35,  30, 3.3],
    [8,   22, 1.3],
    [25,  25, 2.8],
    [32,   8, 3.4],
    [15,   5, 1.6],
    [28,  20, 2.2],
    [38,  25, 3.5],
    [10,  30, 1.4],
    [20,  10, 2.0],
    [30,  18, 3.6],
    [5,   25, 1.0],
    [18,  30, 2.3],
    [35,  10, 3.7],
    [22,  35, 1.9]
])
coords = data[:, :2]
demand = data[:, 2]
Q = 5.0
n = coords.shape[0] - 1

# Compute distance matrix
dist_matrix = squareform(pdist(coords))

# Initialize individual routes and loads
routes = [[0, i, 0] for i in range(1, n+1)]
loads = demand[1:].tolist()

# Compute savings matrix
savings = np.zeros((n, n))
for i, j in itertools.combinations(range(1, n+1), 2):
    saving = dist_matrix[0, i] + dist_matrix[0, j] - dist_matrix[i, j]
    savings[i-1, j-1] = savings[j-1, i-1] = saving
pairs = sorted(
    [(i, j, savings[i-1, j-1]) for i in range(1, n+1) for j in range(i+1, n+1)],
    key=lambda x: x[2], reverse=True
)

# Helper to find route containing a customer
def find_route(customer):
    for idx, route in enumerate(routes):
        if customer in route:
            return idx, route.index(customer)
    return None, None

# Merge routes based on savings
for i, j, _ in pairs:
    ri, pos_i = find_route(i)
    rj, pos_j = find_route(j)
    if ri is None or rj is None or ri == rj:
        continue
    # check endpoints and capacity
    load_i = sum(demand[k] for k in routes[ri] if k != 0)
    load_j = sum(demand[k] for k in routes[rj] if k != 0)
    if load_i + load_j > Q:
        continue
    # if i at end of ri and j at start of rj
    if routes[ri][-2] == i and routes[rj][1] == j:
        routes[ri] = routes[ri][:-1] + routes[rj][1:]
        routes[rj] = []
    # or j at end of rj and i at start of ri
    elif routes[rj][-2] == j and routes[ri][1] == i:
        routes[rj] = routes[rj][:-1] + routes[ri][1:]
        routes[ri] = []
routes = [r for r in routes if r]

# 2-opt local improvement
def route_distance(route):
    return sum(dist_matrix[route[k], route[k+1]] for k in range(len(route)-1))

for idx, route in enumerate(routes):
    improved = True
    while improved:
        improved = False
        best_dist = route_distance(route)
        for a in range(1, len(route)-2):
            for b in range(a+1, len(route)-1):
                new_route = route[:a] + route[a:b+1][::-1] + route[b+1:]#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
                new_dist = route_distance(new_route)
                if new_dist < best_dist:
                    route = new_route
                    best_dist = new_dist
                    improved = True
                    break
            if improved:
                break
    routes[idx] = route

# Compute statistics
num_vehicles = len(routes)
dists = [route_distance(r) for r in routes]
loads = [sum(demand[k] for k in r if k != 0) for r in routes]
total_dist = sum(dists)

# Output results
print(f"Number of vehicles: {num_vehicles}, Total distance: {total_dist:.2f} km")
for v, (r, ld, d) in enumerate(zip(routes, loads, dists), start=1):
    print(f"Vehicle {v}: route {r}, load {ld:.2f}, distance {d:.2f} km")

# Visualization (optional)
plt.figure(figsize=(8,6))
plt.scatter(coords[0,0], coords[0,1], marker='s', s=100, label='Depot')
plt.scatter(coords[1:,0], coords[1:,1], s=50, label='Customer')
for v, route in enumerate(routes, start=1):
    xs, ys = zip(*(coords[k] for k in route))
    plt.plot(xs, ys, '-o', label=f'V{v}')
plt.legend(); plt.title('Routes'); plt.xlabel('X'); plt.ylabel('Y'); plt.grid()#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
plt.show()
