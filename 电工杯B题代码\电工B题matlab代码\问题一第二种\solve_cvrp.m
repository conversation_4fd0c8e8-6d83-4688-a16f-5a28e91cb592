%% 主求解函数
function [best_routes, best_distance, vehicle_count] = solve_cvrp(coords, demands, Q, dist_matrix)
    n = size(coords, 1) - 1;
    
    % 第一阶段: 使用扫描算法进行聚类分组
    fprintf('\n第一阶段: 扫描算法聚类分组\n');
    clusters = sweep_algorithm(coords, demands, Q);
    
    % 第二阶段: 对每个聚类进行路径优化
    fprintf('第二阶段: 路径优化\n');
    best_routes = cell(length(clusters), 1);
    total_distance = 0;
    
    for i = 1:length(clusters)
        cluster = clusters{i};
        fprintf('  优化车辆%d的路径 (包含%d个收集点)...', i, length(cluster));
        
        % 使用最近邻算法 + 2-opt改进
        optimized_route = optimize_route(cluster, dist_matrix);
        best_routes{i} = optimized_route;
        
        route_distance = calculate_route_distance(optimized_route, dist_matrix);
        total_distance = total_distance + route_distance;
        
        fprintf(' 完成，距离: %.2f公里\n', route_distance);
    end
    
    best_distance = total_distance;
    vehicle_count = length(clusters);
end
