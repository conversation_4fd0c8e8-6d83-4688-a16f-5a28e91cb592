import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
from itertools import chain, combinations

# --- 1. 数据输入 ---
coords = np.array([
    [ 0,  0],[12,  8],[ 5, 15],[20, 30],[25, 10],[35, 22],[18,  5],[30, 35],
    [10, 25],[22, 18],[38, 15],[ 5,  8],[15, 32],[28,  5],[30, 12],[10, 10],
    [20, 20],[35, 30],[ 8, 22],[25, 25],[32,  8],[15,  5],[28, 20],[38, 25],
    [10, 30],[20, 10],[30, 18],[ 5, 25],[18, 30],[35, 10],[22, 35]
])#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
w_all = np.array([
    [0,0,0,0],
    [0.72,0.12,0.06,0.30],[1.38,0.23,0.05,0.64],[1.08,0.18,0.04,0.50],
    [1.55,0.31,0.06,1.18],[1.62,0.27,0.05,0.76],[1.76,0.384,0.096,0.96],
    [0.77,0.168,0.042,0.42],[1.02,0.238,0.068,0.374],[1.32,0.176,0.044,0.66],
    [1.45,0.30,0.075,0.675],[1.35,0.27,0.108,0.972],[1.87,0.51,0.068,0.952],
    [2.58,0.516,0.129,1.075],[1.134,0.21,0.063,0.693],[0.78,0.13,0.065,0.325],
    [0.768,0.192,0.080,0.56],[0.72,0.27,0.090,0.72],[1.595,0.348,0.087,0.87],
    [1.50,0.36,0.090,1.05],[1.08,0.18,0.090,0.45],[0.912,0.19,0.038,0.76],
    [0.90,0.195,0.075,0.33],[0.99,0.27,0.072,0.468],[1.44,0.24,0.048,0.672],
    [1.74,0.319,0.116,0.725],[1.17,0.39,0.130,0.91],[1.70,0.34,0.170,1.19],
    [2.64,0.66,0.044,1.056],[0.864,0.216,0.072,0.648],[0.986,0.204,0.085,0.425]
])
st_coords = np.array([[12,5],[7,28],[20,8],[30,15],[25,10]])  # 31–35
Tj = 10000
Q = [8,6,3,10]
C = [2.5,2.0,5.0,1.8]#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16

# 全局坐标和距离
all_coords = np.vstack((coords, st_coords))
D = squareform(pdist(all_coords))
n, m = 30, 5

# 最近邻 CVRP 子例程
def solve_cvrp(pts, demands, Qk):
    unserved = pts.copy()
    routes = []
    while unserved:
        route, load, curr = [0], 0, 0
        while True:
            cand = [i for i in unserved if load+demands[i]>Qk]
            # oops inverted: want those with load+demands[i] <= Qk
            cand = [i for i in unserved if load+demands[i] <= Qk]
            if not cand: break
            i = min(cand, key=lambda x: D[curr,x])
            route.append(i)
            load += demands[i]
            curr = i
            unserved.remove(i)
        route.append(0)
        routes.append(route)
    return routes

# 枚举所有非空站点子集
best = {'cost': np.inf}#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
for r in chain.from_iterable(combinations(range(m), k) for k in range(1, m+1)):
    y = set(r)
    build_cost = len(y)*Tj
    # 分配到最近站
    assign = np.zeros((n,4), dtype=int)
    for i in range(n):
        for k in range(4):
            dists = [D[i+1, 31+s] for s in y]
            assign[i,k] = list(y)[np.argmin(dists)]
    # 路径成本
    trans_cost = 0
    routes = {}
    for s in y:
        j = 31+s
        for k in range(4):
            pts = list(assign[:,k]==s)
            demands = {i+1:w_all[i+1,k] for i,b in enumerate(pts) if b}
            if not demands: continue
            local = list(demands.keys())
            rs = solve_cvrp(local, demands, Q[k])
            for route in rs:
                for u,v in zip(route, route[1:]):
                    trans_cost += C[k]*D[u,v]
            routes[(s,k)] = rs
    total = build_cost + trans_cost
    if total < best['cost']:
        best.update({'cost': total, 'y': y, 'routes': routes})

# 输出#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
print("选址中转站:", sorted([31+s for s in best['y']]))
print(f"最小总成本: {best['cost']:.2f}元")
for (s,k), rs in best['routes'].items():
    for idx, route in enumerate(rs,1):
        print(f"站{31+s} 类{k+1} 车{idx}: {route}")
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# 可视化1: 全局概览
plt.figure(figsize=(8,6))
plt.scatter(all_coords[0,0],all_coords[0,1],c='k',s=120,marker='s',label='厂区')
plt.scatter(all_coords[1:31,0],all_coords[1:31,1],c='gray',label='客户')
plt.scatter(all_coords[31:36,0],all_coords[31:36,1],c='r',marker='s',label='中转站')
linestyles = ['-','--',':','-.']
colors = plt.cm.tab10(np.arange(m))
for (s,k), rs in best['routes'].items():
    for route in rs:
        xs, ys = zip(*[all_coords[u] for u in route])
        plt.plot(xs, ys, linestyle=linestyles[k], color=colors[s], linewidth=1.5)
plt.legend(loc='center left', bbox_to_anchor=(1,0.5))
plt.grid(True); plt.title('全局运输路线概览')
plt.xlabel('X (km)'); plt.ylabel('Y (km)')

# 可视化2: 每站详细
for s in best['y']:
    plt.figure(figsize=(6,6))
    j = 31+s
    plt.scatter(all_coords[j,0],all_coords[j,1],c='r',s=120,marker='s',label=f'中转站{j}')
    for k in range(4):
        pts = [i+1 for i in range(n) if assign[i,k]==s]
        plt.scatter(all_coords[pts,0],all_coords[pts,1],c='b',label=f'类型{k+1}')
        for route in best['routes'].get((s,k),[]):
            xs, ys = zip(*[all_coords[u] for u in route])
            plt.plot(xs, ys, '-o')
    plt.title(f'中转站{j} 运输详情')
    plt.legend(); plt.grid()
    plt.xlabel('X'); plt.ylabel('Y')

plt.show()
