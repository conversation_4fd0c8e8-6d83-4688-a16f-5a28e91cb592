import numpy as np
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt

# Vehicle Routing Problem (CVRP) - Greedy Nearest Neighbor
# Single vehicle type, capacity Q = 5 tons

# 1. Input Data
# Format: [x, y, demand]
data = np.array([
    [0,   0,  0],   # 0: Depot
    [12,  8,  1.2],
    [5,   15, 2.3],
    [20,  30, 1.8],
    [25,  10, 3.1],
    [35,  22, 2.7],
    [18,   5, 1.5],
    [30,  35, 2.9],
    [10,  25, 1.1],
    [22,  18, 2.4],
    [38,  15, 3.0],
    [5,    8, 1.7],
    [15,  32, 2.1],
    [28,   5, 3.2],
    [30,  12, 2.6],
    [10,  10, 1.9],
    [20,  20, 2.5],
    [35,  30, 3.3],
    [8,   22, 1.3],
    [25,  25, 2.8],
    [32,   8, 3.4],
    [15,   5, 1.6],
    [28,  20, 2.2],
    [38,  25, 3.5],
    [10,  30, 1.4],
    [20,  10, 2.0],
    [30,  18, 3.6],
    [5,   25, 1.0],
    [18,  30, 2.3],
    [35,  10, 3.7],
    [22,  35, 1.9]
])
coords = data[:, :2]
weights = data[:, 2]
Q = 5  # vehicle capacity
num_customers = len(coords) - 1

# 2. Preprocessing: Distance matrix
dist_matrix = squareform(pdist(coords))

# 3. Greedy nearest neighbor construction
unserved = list(range(1, num_customers + 1))  # 1..n
routes = []

while unserved:
    curr_load = 0.0
    route = [0]  # start from depot index 0
    curr_node = 0

    while True:
        # find nearest feasible customer
        best_dist = np.inf
        next_cust = None
        for i in unserved:
            if curr_load + weights[i] <= Q:
                d = dist_matrix[curr_node, i]
                if d < best_dist:
                    best_dist = d
                    next_cust = i
        if next_cust is None:
            break
        # assign customer
        route.append(next_cust)
        curr_load += weights[next_cust]
        curr_node = next_cust
        unserved.remove(next_cust)

    route.append(0)  # return to depot
    routes.append(route)

# 4. Compute distances and loads
num_vehicles = len(routes)
distances = np.zeros(num_vehicles)
loads = np.zeros(num_vehicles)

for k, route in enumerate(routes):
    d = 0.0
    for i in range(len(route) - 1):
        d += dist_matrix[route[i], route[i+1]]
    distances[k] = d
    loads[k] = sum(weights[c] for c in route if c != 0)

total_distance = distances.sum()

# 5. Output results
print(f"Greedy Nearest Neighbor CVRP: Vehicles used = {num_vehicles}, Total distance = {total_distance:.2f} km")
for k, route in enumerate(routes, 1):
    print(f"  Vehicle {k}: Route {route}, Load {loads[k-1]:.2f} t, Distance {distances[k-1]:.2f} km")

# 6. Visualization
# 6.1 Route plot
plt.figure(figsize=(8, 6))
plt.scatter(coords[0,0], coords[0,1], marker='s', s=100, label='Depot')
plt.scatter(coords[1:,0], coords[1:,1], s=50, label='Customers')
for i in range(1, num_customers+1):
    plt.text(coords[i,0], coords[i,1], str(i))

colors = plt.cm.tab10.colors
for k, route in enumerate(routes):
    pts = np.array([coords[i] for i in route])
    plt.plot(pts[:,0], pts[:,1], '-o', label=f'Vehicle {k+1}', linewidth=1.5, color=colors[k % len(colors)])

plt.title('Greedy Nearest Neighbor Routes')
plt.xlabel('X (km)')
plt.ylabel('Y (km)')
plt.legend(loc='best')
plt.grid(True)
plt.show()

# 6.2 Load & Distance bar charts
fig, ax = plt.subplots(2, 1, figsize=(8, 6))
ax[0].bar(range(1, num_vehicles+1), loads)
ax[0].set_title('Load per Vehicle')
ax[0].set_ylabel('Tons')

ax[1].bar(range(1, num_vehicles+1), distances)
ax[1].set_title('Distance per Vehicle')
ax[1].set_ylabel('Km')
ax[1].set_xlabel('Vehicle')

plt.tight_layout()
plt.show()

# 6.3 Distance share pie chart
plt.figure(figsize=(6, 6))
plt.pie(distances, labels=[f'V{i+1}' for i in range(num_vehicles)], autopct='%1.1f%%')
plt.title('Distance Share per Vehicle')
plt.show()
