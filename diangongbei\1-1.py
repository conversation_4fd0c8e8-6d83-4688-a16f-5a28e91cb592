import pandas as pd
import numpy as np
import random
import math

# 读取数据，路径请根据实际修改
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
data = pd.read_excel(r'B题\附件1.xlsx', skiprows=2, names=col_names)

points = data[['x', 'y']].values
weights = data['w'].values
n = len(points)

Q = 5.0  # 车辆最大载重，吨
VEHICLE_SPEED = 40  # km/h
depot = np.array([0, 0])  # 垃圾处理厂坐标

# 计算距离矩阵（包括垃圾厂）
all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 初始化：每个点一个单独路径，形如 [[1], [2], [3], ...]
routes = [[i] for i in range(1, n+1)]
route_loads = [weights[i-1] for i in range(1, n+1)]

# 计算节约值 S(i,j) = d(i,0)+d(j,0)-d(i,j)
savings = []
for i in range(1, n+1):
    for j in range(i+1, n+1):
        s = dist_matrix[i, 0] + dist_matrix[j, 0] - dist_matrix[i, j]
        savings.append((i, j, s))
# 按节约值降序排序
savings.sort(key=lambda x: x[2], reverse=True)

# 定义辅助函数找到点所属路径索引
def find_route(routes, node):
    for idx, route in enumerate(routes):
        if node in route:
            return idx
    return None

# 合并路径
for i, j, s in savings:
    ri = find_route(routes, i)
    rj = find_route(routes, j)
    if ri is not None and rj is not None and ri != rj:
        # 检查路径端点是否是 i, j 使得合并路径顺序合理
        route_i = routes[ri]
        route_j = routes[rj]
        # 判断i是否是route_i的端点，j是否是route_j的端点
        can_merge = False
        if (route_i[-1] == i and route_j[0] == j):
            merged_load = route_loads[ri] + route_loads[rj]
            if merged_load <= Q:
                # 合并route_i + route_j
                routes[ri] = route_i + route_j
                route_loads[ri] = merged_load
                del routes[rj]
                del route_loads[rj]
                can_merge = True
        elif (route_i[0] == i and route_j[-1] == j):
            merged_load = route_loads[ri] + route_loads[rj]
            if merged_load <= Q:
                # 合并route_j + route_i
                routes[ri] = route_j + route_i
                route_loads[ri] = merged_load
                del routes[rj]
                del route_loads[rj]
                can_merge = True
        elif (route_i[0] == i and route_j[0] == j):
            merged_load = route_loads[ri] + route_loads[rj]
            if merged_load <= Q:
                # 合并route_j[::-1] + route_i
                routes[ri] = route_j[::-1] + route_i
                route_loads[ri] = merged_load
                del routes[rj]
                del route_loads[rj]
                can_merge = True
        elif (route_i[-1] == i and route_j[-1] == j):
            merged_load = route_loads[ri] + route_loads[rj]
            if merged_load <= Q:
                # 合并route_i + route_j[::-1]
                routes[ri] = route_i + route_j[::-1]
                route_loads[ri] = merged_load
                del routes[rj]
                del route_loads[rj]
                can_merge = True

# 计算路径距离函数
def calc_route_distance(route):
    dist = 0
    prev = 0
    for node in route:
        dist += dist_matrix[prev][node]
        prev = node
    dist += dist_matrix[prev][0]
    return dist

# 输出结果
total_dist = 0
print(f"共生成 {len(routes)} 条运输路径（车辆多次往返）")
for idx, route in enumerate(routes):
    dist = calc_route_distance(route)
    total_dist += dist
    time_h = dist / VEHICLE_SPEED
    route_str = ' -> '.join(str(r) for r in route)
    print(f"路径{idx+1}: 0 -> {route_str} -> 0，距离：{dist:.2f} km，耗时：{time_h:.2f} 小时")

print(f"\n总行驶距离: {total_dist:.2f} km")
print(f"总运输时间: {total_dist / VEHICLE_SPEED:.2f} 小时")

# 模拟退火算法
def simulated_annealing(routes, route_loads, dist_matrix, Q, initial_temp=1000, cooling_rate=0.99, max_iterations=1000):
    current_routes = routes
    current_loads = route_loads
    current_temp = initial_temp

    best_routes = current_routes
    best_distance = calc_total_distance(current_routes, dist_matrix)

    print(f"初始路径: {current_routes}, 初始总距离: {best_distance:.2f}")

    while current_temp > 1:
        for _ in range(max_iterations):
            # 生成新解
            new_routes = perturb_solution(current_routes)
            new_distance = calc_total_distance(new_routes, dist_matrix)

            # 计算成本差
            delta_distance = new_distance - best_distance

            # 接受新解
            if delta_distance < 0 or random.random() < math.exp(-delta_distance / current_temp):
                current_routes = new_routes
                current_loads = route_loads  # 需要更新新解的负载
                if new_distance < best_distance:
                    best_routes = new_routes
                    best_distance = new_distance

        # 降低温度
        current_temp *= cooling_rate
        print(f"当前温度: {current_temp:.2f}")

    return best_routes, best_distance

# 计算总距离的辅助函数
def calc_total_distance(routes, dist_matrix):
    total_dist = 0
    for route in routes:
        total_dist += calc_route_distance(route)
    return total_dist

# 生成新解的辅助函数
def perturb_solution(routes):
    new_routes = routes.copy()
    # 随机选择两个路径并交换其中的一个点
    if len(new_routes) > 1:
        idx1, idx2 = random.sample(range(len(new_routes)), 2)
        if new_routes[idx1] and new_routes[idx2]:
            point1 = random.choice(new_routes[idx1])
            point2 = random.choice(new_routes[idx2])
            new_routes[idx1].remove(point1)
            new_routes[idx2].append(point1)
            new_routes[idx2].remove(point2)
            new_routes[idx1].append(point2)
    return new_routes

# 在合并路径的代码后调用模拟退火
best_routes, best_distance = simulated_annealing(routes, route_loads, dist_matrix, Q)

print(f"最佳路径: {best_routes}, 最佳总距离: {best_distance:.2f}")
