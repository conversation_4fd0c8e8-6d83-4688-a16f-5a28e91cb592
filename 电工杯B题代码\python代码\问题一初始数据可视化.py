import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata

# 数据准备
data = np.array([
    [0,  0,  0,   np.nan],
    [1, 12,  8,   1.2],
    [2,  5, 15,   2.3],
    [3, 20, 30,   1.8],
    [4, 25, 10,   3.1],
    [5, 35, 22,   2.7],
    [6, 18,  5,   1.5],
    [7, 30, 35,   2.9],
    [8, 10, 25,   1.1],
    [9, 22, 18,   2.4],
    [10, 38, 15,  3.0],
    [11,  5,  8,  1.7],
    [12, 15, 32,  2.1],
    [13, 28,  5,  3.2],
    [14, 30, 12,  2.6],
    [15, 10, 10,  1.9],
    [16, 20, 20,  2.5],
    [17, 35, 30,  3.3],
    [18,  8, 22,  1.3],
    [19, 25, 25,  2.8],
    [20, 32,  8,  3.4],
    [21, 15,  5,  1.6],
    [22, 28, 20,  2.2],
    [23, 38, 25,  3.5],
    [24, 10, 30,  1.4],
    [25, 20, 10,  2.0],
    [26, 30, 18,  3.6],
    [27,  5, 25,  1.0],
    [28, 18, 30,  2.3],
    [29, 35, 10,  3.7],
    [30, 22, 35,  1.9]
])

idx = data[:,0].astype(int)
X = data[:,1]
Y = data[:,2]
W = data[:,3]

# 标记处理厂
depot_mask = idx == 0
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# 1. 平面散点图：点大小映射垃圾量，颜色映射垃圾量
plt.figure(figsize=(8,6))
plt.scatter(X[depot_mask], Y[depot_mask], s=200, marker='s', label='处理厂')
sc = plt.scatter(X[~depot_mask], Y[~depot_mask], s=W[~depot_mask]*100, c=W[~depot_mask], cmap='viridis', label='收集点')
plt.colorbar(sc, label='垃圾量 (吨)')
plt.title('垃圾量分布散点图')
plt.xlabel('X (km)')
plt.ylabel('Y (km)')
plt.legend()
plt.axis('equal')
plt.grid(True)

# 2. 垃圾量热力图：基于插值的二维场
grid_x, grid_y = np.mgrid[X.min():X.max():100j, Y.min():Y.max():100j]
points = np.column_stack((X[~depot_mask], Y[~depot_mask]))
values = W[~depot_mask]
grid_z = griddata(points, values, (grid_x, grid_y), method='cubic')

plt.figure(figsize=(8,6))
plt.imshow(grid_z.T, origin='lower', extent=(X.min(), X.max(), Y.min(), Y.max()))
plt.scatter(X[~depot_mask], Y[~depot_mask], s=20, c='black')
plt.scatter(X[depot_mask], Y[depot_mask], s=200, marker='s', c='black')
plt.title('垃圾量热力图')
plt.xlabel('X (km)')
plt.ylabel('Y (km)')
plt.colorbar(label='垃圾量 (吨)')
plt.grid(False)

plt.show()
