function route = twoOptImprove(route, D)
improved = true;
while improved
    improved = false;
    bestDist = pathDistance(route, D);
    for i = 2:length(route)-2
        for j = i+1:length(route)-1
            newRoute = [route(1:i-1), fliplr(route(i:j)), route(j+1:end)];
            newDist = pathDistance(newRoute, D);
            if newDist < bestDist
                route = newRoute;
                bestDist = newDist;
                improved = true;
            end
        end
        if improved, break; end
    end
end%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
end