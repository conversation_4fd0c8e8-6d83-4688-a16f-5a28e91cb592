
# vrp_lns_multi_type.py
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
import random
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
# --- 1. 数据输入 ---
data = np.array([
    [0,    0,   0.00, 0.00, 0.00, 0.00],
    [12,   8,   0.72, 0.12, 0.06, 0.30],
    [5,   15,   1.38, 0.23, 0.05, 0.64],
    [20,  30,   1.08, 0.18, 0.04, 0.50],
    [25,  10,   1.55, 0.31, 0.06, 1.18],
    [35,  22,   1.62, 0.27, 0.05, 0.76],
    [18,   5,   1.76, 0.384,0.096,0.96],
    [30,  35,   0.77, 0.168,0.042,0.42],
    [10,  25,   1.02, 0.238,0.068,0.374],
    [22,  18,   1.32, 0.176,0.044,0.66],
    [38,  15,   1.45, 0.30, 0.075,0.675],
    [5,    8,   1.35, 0.27, 0.108,0.972],
    [15,  32,   1.87, 0.51, 0.068,0.952],
    [28,   5,   2.58, 0.516,0.129,1.075],
    [30,  12,   1.134,0.21, 0.063,0.693],
    [10,  10,   0.78, 0.13, 0.065,0.325],
    [20,  20,   0.768,0.192,0.080,0.56],
    [35,  30,   0.72, 0.27, 0.090,0.72],
    [8,   22,   1.595,0.348,0.087,0.87],
    [25,  25,   1.50, 0.36, 0.090,1.05],
    [32,   8,   1.08, 0.18, 0.090,0.45],
    [15,   5,   0.912,0.19, 0.038,0.76],
    [28,  20,   0.90, 0.195,0.075,0.33],
    [38,  25,   0.99, 0.27, 0.072,0.468],
    [10,  30,   1.44, 0.24, 0.048,0.672],
    [20,  10,   1.74, 0.319,0.116,0.725],
    [30,  18,   1.17, 0.39, 0.130,0.91],
    [5,   25,   1.70, 0.34, 0.170,1.19],
    [18,  30,   2.64, 0.66, 0.044,1.056],
    [35,  10,   0.864,0.216,0.072,0.648],
    [22,  35,   0.986,0.204,0.085,0.425]
])
coords = data[:, :2]
w_all  = data[:, 2:]
N      = coords.shape[0]
D      = squareform(pdist(coords))
Q      = [8,6,3,10]
C      = [2.5,2.0,5.0,1.8]
max_iter = 300
remove_ratio = 0.2

all_routes = []
all_cost = []

# --- 2. 对每类垃圾使用 LNS 优化 ---
for k in range(4):
    print(f"\n=== 垃圾类型 {k+1} 优化 ===")
    customers = [i for i in range(1, N) if w_all[i, k] > 0]
    # 初始最近邻解
    routes = []
    loads = []
    dists = []
    unserved = customers.copy()
    while unserved:
        rt = [0]
        load = 0
        curr = 0
        while True:
            cand = [i for i in unserved if load + w_all[i, k] <= Q[k]]
            if not cand:
                break
            next_i = min(cand, key=lambda i: D[curr, i])
            rt.append(next_i)
            load += w_all[next_i, k]
            curr = next_i
            unserved.remove(next_i)
        rt.append(0)
        routes.append(rt)
        loads.append(load)
        dists.append(sum(D[rt[i], rt[i+1]] for i in range(len(rt)-1)))
    curr_routes = routes.copy()
    best_routes = routes.copy()
    best_cost = sum(dists) * C[k]

    # LNS 迭代
    for _ in range(max_iter):
        # 破坏
        new_routes = []
        removed = set()
        for rt in curr_routes:
            inner = rt[1:-1]
            if not inner:
                new_routes.append(rt)
                continue
            m = max(1, int(remove_ratio * len(inner)))
            sel = random.sample(inner, m)
            removed.update(sel)
            reduced = [i for i in inner if i not in sel]
            new_routes.append([0] + reduced + [0])
        # 修复
        for idx_rt, rt in enumerate(new_routes):
            ins = list(removed)
            while ins:
                ii = ins.pop(0)
                best_inc = float('inf')
                best_pos = 1
                for pos in range(1, len(rt)):
                    inc = D[rt[pos-1], ii] + D[ii, rt[pos]] - D[rt[pos-1], rt[pos]]
                    if inc < best_inc:
                        best_inc = inc
                        best_pos = pos
                rt = rt[:best_pos] + [ii] + rt[best_pos:]
            new_routes[idx_rt] = rt
        # 评估
        total_dist = sum(sum(D[rt[i], rt[i+1]] for i in range(len(rt)-1)) for rt in new_routes)
        total_cost = total_dist * C[k]
        if total_cost < best_cost:
            best_cost = total_cost
            best_routes = [rt.copy() for rt in new_routes]
        curr_routes = [rt.copy() for rt in new_routes]

    all_routes.append(best_routes)
    all_cost.append(best_cost)
    print(f"类型 {k+1} 最优成本: {best_cost:.2f} 元")

print(f"\n== 全部类别 总成本: {sum(all_cost):.2f} 元 ==")

# --- 3. 各类型具体路径输出 ---
for k, routes in enumerate(all_routes):
    print(f"\n--- 垃圾类型 {k+1} 最终路径 ---")
    for vid, rt in enumerate(routes,1):
        print(f"类型{k+1} 车{vid}: {rt}")
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# --- 4. 精美可视化 ---
plt.figure(figsize=(8,6))
plt.scatter(coords[0,0], coords[0,1], c='k', s=120, marker='s', label='厂区')
plt.scatter(coords[1:,0], coords[1:,1], c='gray', s=50, label='客户')
linestyles = ['-','--',':','-.']
colors = plt.cm.tab10.colors
for k, routes in enumerate(all_routes):
    for rt in routes:
        xs, ys = zip(*[coords[i] for i in rt])
        plt.plot(xs, ys, linestyle=linestyles[k], color=colors[k], linewidth=1.5)
plt.title('四类垃圾运输路径总览')
plt.legend(loc='upper right'); plt.grid(True)

plt.figure(figsize=(10,8))
for k, routes in enumerate(all_routes):#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    plt.subplot(2,2,k+1); plt.title(f'类型{k+1} 运输路径'); plt.grid(True)
    plt.scatter(coords[0,0], coords[0,1], c='k', s=100, marker='s')
    idx = [i for i in range(1,N) if w_all[i,k]>0]
    plt.scatter(coords[idx,0], coords[idx,1], c='blue', s=40)
    for rt in routes:
        xs, ys = zip(*[coords[i] for i in rt])
        plt.plot(xs, ys, '-o', linewidth=1.2)
    plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.tight_layout()

plt.figure(figsize=(6,4))
plt.bar(['厨余','可回收','有害','其他'], all_cost)
plt.title('各类运输总成本'); plt.ylabel('成本 (元)'); plt.grid(axis='y')
plt.show()
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16