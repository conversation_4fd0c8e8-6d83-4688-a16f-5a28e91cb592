%% 计算非对称路径距离
function distance = calculate_asymmetric_route_distance(route, dist_matrix)
    distance = 0;
    for i = 1:length(route)-1
        from_idx = route(i) + 1;
        to_idx = route(i+1) + 1;
        
        if from_idx > 0 && from_idx <= size(dist_matrix, 1) && ...
           to_idx > 0 && to_idx <= size(dist_matrix, 2)
            distance = distance + dist_matrix(from_idx, to_idx);
        end
    end
end