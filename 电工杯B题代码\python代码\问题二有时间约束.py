#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题二：含时间约束的多车辆协同运输优化求解代码
多车型多商品车辆路径问题（MC-HFVRP with Time Constraints）
考虑每日最大行驶时间约束
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
import time
import math
import copy
from typing import List, Tuple, Dict, Optional
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class TimeConstrainedMCHFVRP:
    """含时间约束的多车型多商品车辆路径问题求解器"""

    def __init__(self):
        """初始化求解器"""
        # 收集点坐标和各类垃圾产生量数据
        self.data = np.array([
            [0, 0, 0, 0, 0, 0, 0],  # 处理厂
            [1, 12, 8, 0.72, 0.12, 0.06, 0.3],  # 收集点1
            [2, 5, 15, 1.38, 0.23, 0.05, 0.64],  # 收集点2
            [3, 20, 30, 1.08, 0.18, 0.04, 0.5],  # 收集点3
            [4, 25, 10, 1.55, 0.31, 0.06, 1.18],  # 收集点4#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
            [5, 35, 22, 1.62, 0.27, 0.05, 0.76],  # 收集点5
            [6, 18, 5, 1.76, 0.384, 0.096, 0.96],  # 收集点6
            [7, 30, 35, 0.77, 0.168, 0.042, 0.42],  # 收集点7
            [8, 10, 25, 1.02, 0.238, 0.068, 0.374],  # 收集点8
            [9, 22, 18, 1.32, 0.176, 0.044, 0.66],  # 收集点9
            [10, 38, 15, 1.45, 0.3, 0.075, 0.675],  # 收集点10
            [11, 5, 8, 1.35, 0.27, 0.108, 0.972],  # 收集点11
            [12, 15, 32, 1.87, 0.51, 0.068, 0.952],  # 收集点12
            [13, 28, 5, 2.58, 0.516, 0.129, 1.075],  # 收集点13
            [14, 30, 12, 1.134, 0.21, 0.063, 0.693],  # 收集点14
            [15, 10, 10, 0.78, 0.13, 0.065, 0.325],  # 收集点15
            [16, 20, 20, 0.768, 0.192, 0.08, 0.56],  # 收集点16
            [17, 35, 30, 0.72, 0.27, 0.09, 0.72],  # 收集点17
            [18, 8, 22, 1.595, 0.348, 0.087, 0.87],  # 收集点18
            [19, 25, 25, 1.5, 0.36, 0.09, 1.05],  # 收集点19
            [20, 32, 8, 1.08, 0.18, 0.09, 0.45],  # 收集点20
            [21, 15, 5, 0.912, 0.19, 0.038, 0.76],  # 收集点21
            [22, 28, 20, 0.9, 0.195, 0.075, 0.33],  # 收集点22
            [23, 38, 25, 0.99, 0.27, 0.072, 0.468],  # 收集点23
            [24, 10, 30, 1.44, 0.24, 0.048, 0.672],  # 收集点24
            [25, 20, 10, 1.74, 0.319, 0.116, 0.725],  # 收集点25#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
            [26, 30, 18, 1.17, 0.39, 0.13, 0.91],  # 收集点26
            [27, 5, 25, 1.7, 0.34, 0.17, 1.19],  # 收集点27
            [28, 18, 30, 2.64, 0.66, 0.044, 1.056],  # 收集点28
            [29, 35, 10, 0.864, 0.216, 0.072, 0.648],  # 收集点29
            [30, 22, 35, 0.986, 0.204, 0.085, 0.425],  # 收集点30
        ])

        # 提取基础数据
        self.coords = self.data[:, 1:3]  # 坐标矩阵
        self.demands = self.data[:, 3:7]  # 各类垃圾需求矩阵
        self.n = len(self.coords) - 1  # 收集点数量

        # 车辆参数 [载重限制, 容积限制, 单位成本, 最大工作时间]
        self.vehicle_params = np.array([
            [8, 20, 2.5, 8],  # 厨余垃圾车 (k=0) - 8小时工作时间
            [6, 25, 2.0, 8],  # 可回收物车 (k=1) - 8小时工作时间
            [3, 10, 5.0, 6],  # 有害垃圾车 (k=2) - 6小时工作时间（特殊车辆）
            [10, 18, 1.8, 10],  # 其他垃圾车 (k=3) - 10小时工作时间
        ])

        self.Q = self.vehicle_params[:, 0]  # 载重限制
        self.V = self.vehicle_params[:, 1]  # 容积限制
        self.C = self.vehicle_params[:, 2]  # 单位距离成本#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        self.T_max = self.vehicle_params[:, 3]  # 最大工作时间（小时）

        # 时间相关参数
        self.vehicle_speed = 40  # 车辆行驶速度 (km/h)
        self.service_time = 0.25  # 每个收集点服务时间 (小时)
        self.loading_time = 0.1  # 处理厂装卸时间 (小时)

        # 垃圾类型名称
        self.garbage_types = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

        # 计算距离矩阵
        self.dist_matrix = self._calculate_distance_matrix()

        print("=== 问题二：含时间约束的多车型垃圾运输路径优化 ===")
        print(f"收集点数量: {self.n}个")
        print(f"车辆类型数: {len(self.garbage_types)}种")
        print(f"车辆行驶速度: {self.vehicle_speed} km/h")
        print(f"单点服务时间: {self.service_time:.2f}小时")

    def _calculate_distance_matrix(self) -> np.ndarray:
        """计算欧几里得距离矩阵"""
        n = len(self.coords)
        dist_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i != j:
                    dist_matrix[i, j] = np.sqrt(
                        (self.coords[i, 0] - self.coords[j, 0]) ** 2 +
                        (self.coords[i, 1] - self.coords[j, 1]) ** 2
                    )

        return dist_matrix

    def solve(self) -> Dict:
        """求解含时间约束的多车型CVRP问题"""
        print("\n=== 垃圾产生量与时间约束统计 ===")

        # 统计各类垃圾总量
        total_demands = np.sum(self.demands[1:, :], axis=0)

        for k in range(4):
            print(f"{self.garbage_types[k]}:")
            print(f"  总量: {total_demands[k]:.3f}吨, 载重下界: {math.ceil(total_demands[k] / self.Q[k])}辆")
            print(f"  最大工作时间: {self.T_max[k]:.1f}小时")

            # 估算时间约束下的理论车辆数
            valid_points = np.sum(self.demands[1:, k] > 0)
            if valid_points > 0:
                avg_points_per_vehicle = max(1, self.Q[k] / (total_demands[k] / valid_points))
                est_time_per_vehicle = avg_points_per_vehicle * self.service_time + 3  # 3小时行驶时间估算
                time_constrained_vehicles = math.ceil(est_time_per_vehicle / self.T_max[k] *
                                                      math.ceil(total_demands[k] / self.Q[k]))
                print(f"  时间约束估算车辆数: {time_constrained_vehicles}辆")
            print()

        print("=== 开始含时间约束的多车型协同求解 ===")
        start_time = time.time()

        # 存储结果
        results = {
            'routes': [None] * 4,
            'costs': np.zeros(4),
            'distances': np.zeros(4),
            'vehicle_counts': np.zeros(4, dtype=int),
            'time_usage': [None] * 4,
            'violations': np.zeros(4, dtype=int),
            'total_demands': total_demands
        }

        # 分别求解每种车型的时间约束CVRP子问题
        for k in range(4):
            print(f"\n--- 求解{self.garbage_types[k]}时间约束运输子问题 ---")

            # 提取第k类垃圾的需求数据
            k_demands = self.demands[:, k].copy()

            # 检查是否有需求
            if np.sum(k_demands[1:]) == 0:
                print(f"警告: {self.garbage_types[k]}总需求为0，跳过求解")
                continue

            # 求解第k类车型的时间约束CVRP
            k_result = self._solve_single_type_cvrp(k_demands, k)

            # 存储结果
            results['routes'][k] = k_result['routes']
            results['costs'][k] = k_result['distance'] * self.C[k]
            results['distances'][k] = k_result['distance']
            results['vehicle_counts'][k] = k_result['vehicle_count']
            results['time_usage'][k] = k_result['time_usage']
            results['violations'][k] = k_result['violations']

            print(f"{self.garbage_types[k]}求解完成: {k_result['vehicle_count']}辆车, "
                  f"{k_result['distance']:.2f}公里, {results['costs'][k]:.2f}元")
            print(f"时间违约次数: {k_result['violations']}")

        results['solve_time'] = time.time() - start_time

        # 输出结果汇总
        self._print_results(results)

        # 分析时间约束影响
        self._analyze_time_constraint_impact(results)

        # 可视化结果
        self._visualize_results(results)

        return results

    def _solve_single_type_cvrp(self, demands: np.ndarray, vehicle_type: int) -> Dict:
        """求解单车型的时间约束CVRP"""
        # 过滤出有需求的收集点
        valid_points = np.where(demands[1:] > 0)[0]  # 排除处理厂

        if len(valid_points) == 0:
            return {
                'routes': [], 'distance': 0, 'vehicle_count': 0,
                'time_usage': [], 'violations': 0
            }

        print(f"  有效收集点数: {len(valid_points)}个")

        # 提取有效点的数据
        valid_coords = np.vstack([self.coords[0:1], self.coords[valid_points + 1]])
        valid_demands = np.concatenate([[0], demands[valid_points + 1]])

        # 计算有效点的距离矩阵
        m = len(valid_points)
        valid_dist_matrix = np.zeros((m + 1, m + 1))

        for i in range(m + 1):
            for j in range(m + 1):
                if i != j:
                    orig_i = 0 if i == 0 else valid_points[i - 1] + 1
                    orig_j = 0 if j == 0 else valid_points[j - 1] + 1
                    valid_dist_matrix[i, j] = self.dist_matrix[orig_i, orig_j]

        # 第一阶段：时间约束聚类
        print("  第一阶段：时间约束聚类...")
        clusters = self._time_constrained_clustering(
            valid_coords, valid_demands, self.Q[vehicle_type],
            self.T_max[vehicle_type], valid_dist_matrix
        )

        # 第二阶段：路径优化
        print("  第二阶段：时间约束路径优化...")
        routes = []
        time_usage = []
        total_distance = 0
        violations = 0

        for i, cluster in enumerate(clusters):
            # 路径优化
            optimized_route = self._optimize_route_with_time_check(
                cluster, valid_dist_matrix, self.Q[vehicle_type],
                self.T_max[vehicle_type]
            )

            # 转换为原始索引
            original_route = self._convert_to_original_indices(optimized_route, valid_points)
            routes.append(original_route)

            # 计算距离和时间
            route_distance = self._calculate_route_distance(original_route)
            route_time = self._calculate_route_time(original_route)

            total_distance += route_distance
            time_usage.append(route_time)

            # 检查时间违约
            if route_time > self.T_max[vehicle_type]:
                violations += 1

        return {
            'routes': routes,
            'distance': total_distance,
            'vehicle_count': len(clusters),
            'time_usage': np.array(time_usage),
            'violations': violations#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        }

    def _time_constrained_clustering(self, coords: np.ndarray, demands: np.ndarray,
                                     capacity: float, max_time: float,
                                     dist_matrix: np.ndarray) -> List[List[int]]:
        """时间约束下的聚类算法"""
        n = len(coords) - 1

        if n == 0:
            return []

        # 计算极角并排序
        depot = coords[0]
        angles = []

        for i in range(1, n + 1):
            dx = coords[i, 0] - depot[0]
            dy = coords[i, 1] - depot[1]
            angle = math.atan2(dy, dx)
            angles.append((angle, i - 1))  # (角度, 点索引)

        angles.sort()
        sorted_indices = [idx for _, idx in angles]

        # 时间约束下的贪心分组
        clusters = []
        current_cluster = []
        current_load = 0

        for idx in sorted_indices:
            point_demand = demands[idx + 1]

            # 估算添加该点后的时间消耗
            temp_cluster = current_cluster + [idx]
            estimated_time = self._estimate_cluster_time(temp_cluster, dist_matrix)

            # 检查载重和时间约束
            if (current_load + point_demand <= capacity and
                    estimated_time <= max_time):
                current_cluster.append(idx)
                current_load += point_demand
            else:
                # 当前聚类已满，开始新聚类
                if current_cluster:
                    clusters.append(current_cluster)
                current_cluster = [idx]
                current_load = point_demand

        # 添加最后一个聚类
        if current_cluster:#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
            clusters.append(current_cluster)

        # 后处理：检查时间违约并拆分
        return self._post_process_time_violations(clusters, dist_matrix, capacity, max_time, demands)

    def _estimate_cluster_time(self, cluster: List[int], dist_matrix: np.ndarray) -> float:
        """估算聚类的时间消耗"""
        if not cluster:
            return 0

        # 使用最近邻估算路径长度
        route = [0] + cluster + [0]
        estimated_distance = 0

        for i in range(len(route) - 1):
            from_idx = route[i]
            to_idx = route[i + 1]
            estimated_distance += dist_matrix[from_idx, to_idx]

        # 计算总时间
        driving_time = estimated_distance / self.vehicle_speed
        total_service_time = len(cluster) * self.service_time + self.loading_time

        return driving_time + total_service_time

    def _post_process_time_violations(self, clusters: List[List[int]],
                                      dist_matrix: np.ndarray, capacity: float,
                                      max_time: float, demands: np.ndarray) -> List[List[int]]:
        """后处理时间违约的聚类"""
        processed_clusters = []

        for cluster in clusters:
            cluster_time = self._estimate_cluster_time(cluster, dist_matrix)

            if cluster_time <= max_time:
                processed_clusters.append(cluster)
            else:
                # 时间违约，需要拆分
                print(f"    拆分超时聚类 ({cluster_time:.2f} > {max_time:.2f} 小时)")
                split_clusters = self._split_cluster_by_time(
                    cluster, dist_matrix, capacity, max_time, demands
                )
                processed_clusters.extend(split_clusters)

        return processed_clusters

    def _split_cluster_by_time(self, cluster: List[int], dist_matrix: np.ndarray,
                               capacity: float, max_time: float,
                               demands: np.ndarray) -> List[List[int]]:
        """按时间拆分聚类"""
        split_clusters = []
        remaining_points = cluster.copy()

        while remaining_points:
            current_split = []
            current_load = 0

            # 贪心选择点直到时间或载重约束
            for point in remaining_points[:]:
                point_demand = demands[point + 1]
                temp_split = current_split + [point]
                temp_time = self._estimate_cluster_time(temp_split, dist_matrix)

                if (current_load + point_demand <= capacity and
                        temp_time <= max_time):
                    current_split.append(point)
                    current_load += point_demand
                    remaining_points.remove(point)
                else:
                    break

            if not current_split:
                # 即使单点也超时，强制添加
                current_split = [remaining_points[0]]
                remaining_points.remove(remaining_points[0])
                print(f"      警告：单点 {current_split[0]} 仍超时")

            split_clusters.append(current_split)

        print(f"    聚类拆分为 {len(split_clusters)} 个子聚类")
        return split_clusters

    def _optimize_route_with_time_check(self, cluster: List[int], dist_matrix: np.ndarray,
                                        capacity: float, max_time: float) -> List[int]:
        """带时间检查的路径优化"""
        if not cluster:
            return [0, 0]

        if len(cluster) == 1:
            return [0, cluster[0], 0]

        # 最近邻算法构造初始解
        initial_route = self._nearest_neighbor_tsp(cluster, dist_matrix)

        # 检查时间约束
        initial_time = self._calculate_route_time_from_matrix(initial_route, dist_matrix)
        if initial_time > max_time:
            print(f"      警告：初始路径超时 {initial_time:.2f} > {max_time:.2f} 小时")

        # 2-opt改进（考虑时间约束）
        optimized_route = self._two_opt_with_time_constraint(
            initial_route, dist_matrix, max_time
        )

        return optimized_route

    def _nearest_neighbor_tsp(self, points: List[int], dist_matrix: np.ndarray) -> List[int]:
        """最近邻TSP算法"""
        if not points:
            return [0, 0]

        unvisited = points.copy()
        route = [0]
        current = 0

        while unvisited:
            min_dist = float('inf')
            next_point = -1

            for point in unvisited:
                dist = dist_matrix[current, point]
                if dist < min_dist:
                    min_dist = dist
                    next_point = point

            route.append(next_point)
            current = next_point
            unvisited.remove(next_point)

        route.append(0)#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        return route

    def _two_opt_with_time_constraint(self, route: List[int], dist_matrix: np.ndarray,
                                      max_time: float) -> List[int]:
        """带时间约束的2-opt算法"""
        improved_route = route.copy()
        n = len(route)

        if n <= 3:
            return improved_route

        improved = True
        max_iterations = 30
        iteration = 0

        while improved and iteration < max_iterations:
            improved = False
            iteration += 1

            for i in range(1, n - 2):
                for j in range(i + 1, n - 1):
                    if j - i == 1:
                        continue

                    # 执行2-opt交换
                    new_route = improved_route.copy()
                    new_route[i:j + 1] = new_route[i:j + 1][::-1]

                    # 检查改进和时间约束
                    old_distance = self._calculate_route_distance_from_matrix(improved_route, dist_matrix)
                    new_distance = self._calculate_route_distance_from_matrix(new_route, dist_matrix)
                    new_time = self._calculate_route_time_from_matrix(new_route, dist_matrix)

                    if new_distance < old_distance and new_time <= max_time:
                        improved_route = new_route
                        improved = True

        return improved_route

    def _convert_to_original_indices(self, local_route: List[int],
                                     valid_points: np.ndarray) -> List[int]:
        """将局部索引转换为原始索引"""
        original_route = []

        for idx in local_route:
            if idx == 0:
                original_route.append(0)  # 处理厂
            else:
                original_route.append(valid_points[idx - 1])  # 转换为原始索引

        return original_route

    def _calculate_route_distance(self, route: List[int]) -> float:
        """计算路径距离"""
        distance = 0
        for i in range(len(route) - 1):
            from_idx = route[i]
            to_idx = route[i + 1]
            distance += self.dist_matrix[from_idx, to_idx]
        return distance

    def _calculate_route_distance_from_matrix(self, route: List[int],
                                              dist_matrix: np.ndarray) -> float:
        """从给定距离矩阵计算路径距离"""
        distance = 0
        for i in range(len(route) - 1):
            from_idx = route[i]
            to_idx = route[i + 1]
            distance += dist_matrix[from_idx, to_idx]
        return distance

    def _calculate_route_time(self, route: List[int]) -> float:
        """计算路径时间"""
        # 计算行驶时间
        driving_time = self._calculate_route_distance(route) / self.vehicle_speed

        # 计算服务时间
        service_points = len(route) - 2  # 排除起点和终点
        total_service_time = service_points * self.service_time + self.loading_time

        return driving_time + total_service_time

    def _calculate_route_time_from_matrix(self, route: List[int],
                                          dist_matrix: np.ndarray) -> float:
        """从给定距离矩阵计算路径时间"""
        # 计算行驶时间
        driving_time = self._calculate_route_distance_from_matrix(route, dist_matrix) / self.vehicle_speed

        # 计算服务时间
        service_points = len(route) - 2  # 排除起点和终点
        total_service_time = service_points * self.service_time + self.loading_time

        return driving_time + total_service_time

    def _calculate_route_demand(self, route: List[int], vehicle_type: int) -> float:
        """计算路径需求量"""
        demand = 0
        for i in range(1, len(route) - 1):  # 排除起点和终点
            point_idx = route[i]
            demand += self.demands[point_idx, vehicle_type]
        return demand

    def _print_results(self, results: Dict):
        """打印结果汇总"""
        print("\n========== 含时间约束的多车型求解结果 ==========")
        print(f"总求解时间: {results['solve_time']:.4f}秒")
        print(f"总使用车辆: {int(np.sum(results['vehicle_counts']))}辆")
        print(f"总行驶距离: {np.sum(results['distances']):.2f}公里")
        print(f"总运输成本: {np.sum(results['costs']):.2f}元")
        print(f"总时间违约次数: {int(np.sum(results['violations']))}")

        # 详细输出每种车型的结果
        print("\n=== 各车型详细结果（含时间分析）===")
        for k in range(4):
            if results['vehicle_counts'][k] > 0:
                print(f"\n{self.garbage_types[k]}运输方案:")
                print(f"  车辆数量: {int(results['vehicle_counts'][k])}辆 "
                      f"(最大工作时间: {self.T_max[k]:.1f}小时)")
                print(f"  行驶距离: {results['distances'][k]:.2f}公里")
                print(f"  运输成本: {results['costs'][k]:.2f}元")
                print(
                    f"  载重利用率: {results['total_demands'][k] / (results['vehicle_counts'][k] * self.Q[k]) * 100:.1f}%")

                # 输出时间使用详情
                routes = results['routes'][k]
                time_usage = results['time_usage'][k]

                for v, route in enumerate(routes):
                    route_demand = self._calculate_route_demand(route, k)
                    route_distance = self._calculate_route_distance(route)

                    total_work_time = time_usage[v]
                    driving_time = route_distance / self.vehicle_speed
                    service_points = len(route) - 2
                    total_service_time = service_points * self.service_time + self.loading_time

                    # 时间约束检查
                    time_status = ' ⚠️超时' if total_work_time > self.T_max[k] else ''

                    route_str = '→'.join(map(str, route))

                    print(f"    车辆{k + 1}-{v + 1}: {route_str}")
                    print(f"      载重:{route_demand:.2f}/{self.Q[k]:.1f}吨 | 距离:{route_distance:.2f}公里")#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
                    print(f"      工作时间:{total_work_time:.2f}/{self.T_max[k]:.1f}小时 "
                          f"(行驶:{driving_time:.2f}+服务:{total_service_time:.2f}){time_status}")

    def _analyze_time_constraint_impact(self, results: Dict):
        """分析时间约束影响"""
        print("\n=== 时间约束影响分析 ===")
        print("时间约束vs载重约束对比:")

        for k in range(4):
            if results['vehicle_counts'][k] > 0:
                weight_based_vehicles = math.ceil(results['total_demands'][k] / self.Q[k])
                actual_vehicles = int(results['vehicle_counts'][k])

                print(f"  车型{k + 1}:")
                print(f"    载重约束下理论车辆数: {weight_based_vehicles}辆")
                print(f"    实际使用车辆数: {actual_vehicles}辆")

                if actual_vehicles > weight_based_vehicles:
                    increase_rate = (actual_vehicles - weight_based_vehicles) / weight_based_vehicles * 100
                    print(f"    时间约束影响: +{actual_vehicles - weight_based_vehicles}辆 ({increase_rate:.1f}%)")

                # 时间利用率分析
                if results['time_usage'][k] is not None and len(results['time_usage'][k]) > 0:
                    avg_time_usage = np.mean(results['time_usage'][k])
                    max_time_usage = np.max(results['time_usage'][k])
                    time_utilization = avg_time_usage / self.T_max[k] * 100

                    print(f"    平均工作时间: {avg_time_usage:.2f}/{self.T_max[k]:.1f}小时 "
                          f"(利用率: {time_utilization:.1f}%)")
                    print(f"    最大工作时间: {max_time_usage:.2f}小时")

                    if max_time_usage > self.T_max[k]:
                        print("    ⚠️ 存在超时情况")

                if results['violations'][k] > 0:
                    print(f"    时间违约车辆: {int(results['violations'][k])}辆")
                print()

        # 整体时间约束效应分析
        total_weight_vehicles = sum(math.ceil(results['total_demands'][k] / self.Q[k]) for k in range(4))
        total_actual_vehicles = int(np.sum(results['vehicle_counts']))

        print("整体分析:")
        print(f"  无时间约束理论车辆: {total_weight_vehicles}辆")
        print(f"  含时间约束实际车辆: {total_actual_vehicles}辆")

        if total_actual_vehicles > total_weight_vehicles:
            increase_rate = (total_actual_vehicles - total_weight_vehicles) / total_weight_vehicles * 100
            print(f"  时间约束导致增加: {total_actual_vehicles - total_weight_vehicles}辆 ({increase_rate:.1f}%)")

        # 时间约束紧度分析
        print("\n时间约束紧度分析:")
        constraint_types = ['宽松', '适中', '紧张', '极紧']

        for k in range(4):
            if (results['vehicle_counts'][k] > 0 and
                    results['time_usage'][k] is not None and
                    len(results['time_usage'][k]) > 0):

                avg_utilization = np.mean(results['time_usage'][k]) / self.T_max[k]

                if avg_utilization < 0.6:
                    constraint_level = 0
                elif avg_utilization < 0.8:
                    constraint_level = 1
                elif avg_utilization < 0.95:
                    constraint_level = 2
                else:
                    constraint_level = 3

                print(f"  车型{k + 1}时间约束: {constraint_types[constraint_level]} "
                      f"(平均利用率: {avg_utilization * 100:.1f}%)")

    # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    def _visualize_results(self, results: Dict):
        """可视化结果"""
        # 创建图形
        fig = plt.figure(figsize=(20, 15))
        fig.suptitle('含时间约束的多车型垃圾运输路径优化结果\n'
                     f'总车辆:{int(np.sum(results["vehicle_counts"]))}辆, '
                     f'总成本:{np.sum(results["costs"]):.0f}元',
                     fontsize=16)

        # 颜色和标记定义
        colors = ['red', 'blue', 'green', 'orange']
        markers = ['s', 'o', '^', 'd']

        # 绘制路径图（上半部分）
        for k in range(4):
            ax = plt.subplot(3, 4, k + 1)

            # 绘制处理厂
            ax.plot(self.coords[0, 0], self.coords[0, 1], 'ks',
                    markersize=12, markerfacecolor='black', linewidth=2)
            ax.text(self.coords[0, 0] + 1, self.coords[0, 1] + 1, '处理厂',
                    fontsize=9, fontweight='bold')

            # 绘制收集点
            for i in range(1, len(self.coords)):
                ax.plot(self.coords[i, 0], self.coords[i, 1], markers[k],
                        markersize=5, markerfacecolor='white',
                        markeredgecolor=colors[k], linewidth=1.2)
                ax.text(self.coords[i, 0] + 0.5, self.coords[i, 1] + 0.5, str(i),
                        fontsize=7)

            # 绘制路径
            if results['routes'][k]:
                for v, route in enumerate(results['routes'][k]):
                    # 根据是否超时选择线型
                    line_style = '-'
                    line_width = 2

                    if (results['time_usage'][k] is not None and
                            v < len(results['time_usage'][k]) and
                            results['time_usage'][k][v] > self.T_max[k]):
                        line_style = '--'  # 超时用虚线
                        line_width = 3

                    for i in range(len(route) - 1):
                        from_idx = route[i]
                        to_idx = route[i + 1]

                        ax.plot([self.coords[from_idx, 0], self.coords[to_idx, 0]],
                                [self.coords[from_idx, 1], self.coords[to_idx, 1]],
                                color=colors[k], linestyle=line_style, linewidth=line_width)

            # 设置子图属性
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('X坐标 (公里)', fontsize=9)
            ax.set_ylabel('Y坐标 (公里)', fontsize=9)
            ax.set_title(f'{self.garbage_types[k]}\n车辆:{int(results["vehicle_counts"][k])}辆, '
                         f'限时:{self.T_max[k]:.1f}h', fontsize=10)
            ax.set_xlim(-2, 42)
            ax.set_ylim(-2, 38)
            ax.set_aspect('equal')

        # 绘制时间利用率分析图
        ax5 = plt.subplot(3, 2, 3)

        vehicle_types = []
        time_utilizations = []
        bar_colors = []

        for k in range(4):
            if (results['vehicle_counts'][k] > 0 and
                    results['time_usage'][k] is not None):
                for v in range(len(results['time_usage'][k])):
                    vehicle_types.append(f'{k + 1}-{v + 1}')
                    utilization = results['time_usage'][k][v] / self.T_max[k] * 100
                    time_utilizations.append(utilization)
                    bar_colors.append(colors[k])

        if time_utilizations:
            bars = ax5.bar(range(len(time_utilizations)), time_utilizations,
                           color=bar_colors, alpha=0.7)

            # 添加100%参考线
            ax5.axhline(y=100, color='red', linestyle='--', linewidth=2, label='时间上限')

            # 标记超时车辆
            for i, util in enumerate(time_utilizations):
                if util > 100:
                    ax5.text(i, util + 5, '超时', ha='center',
                             color='red', fontweight='bold', fontsize=8)

            ax5.set_xlabel('车辆编号')
            ax5.set_ylabel('时间利用率 (%)')
            ax5.set_title('各车辆时间利用率分析')
            ax5.grid(True, alpha=0.3)
            ax5.set_xticks(range(len(vehicle_types)))
            ax5.set_xticklabels(vehicle_types, rotation=45)

        # 绘制车型时间统计图
        ax6 = plt.subplot(3, 2, 4)

        avg_utilizations = []
        max_utilizations = []

        for k in range(4):
            if (results['vehicle_counts'][k] > 0 and
                    results['time_usage'][k] is not None and
                    len(results['time_usage'][k]) > 0):
                avg_util = np.mean(results['time_usage'][k]) / self.T_max[k] * 100
                max_util = np.max(results['time_usage'][k]) / self.T_max[k] * 100
                avg_utilizations.append(avg_util)
                max_utilizations.append(max_util)
            else:
                avg_utilizations.append(0)
                max_utilizations.append(0)

        x_pos = np.arange(4)
        bar_width = 0.35

        ax6.bar(x_pos - bar_width / 2, avg_utilizations, bar_width,
                color='skyblue', alpha=0.7, label='平均利用率')
        ax6.bar(x_pos + bar_width / 2, max_utilizations, bar_width,
                color='lightcoral', alpha=0.7, label='最大利用率')

        ax6.axhline(y=100, color='black', linestyle='--', linewidth=1.5, label='时间上限')

        ax6.set_xlabel('车辆类型')
        ax6.set_ylabel('时间利用率 (%)')
        ax6.set_title('各车型时间利用率统计')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        ax6.set_xticks(x_pos)
        ax6.set_xticklabels(['厨余', '可回收', '有害', '其他'])

        # 绘制成本对比分析
        ax7 = plt.subplot(3, 2, 5)

        # 模拟无时间约束的成本（基于理论最少车辆数）
        theoretical_vehicles = [math.ceil(results['total_demands'][k] / self.Q[k]) for k in range(4)]
        theoretical_costs = [tv * 60 * self.C[k] for k, tv in enumerate(theoretical_vehicles)]  # 假设平均60km

        actual_costs = results['costs']

        x_labels = ['厨余', '可回收', '有害', '其他']
        x = np.arange(4)

        ax7.bar(x - 0.2, theoretical_costs, 0.4, color='lightgray',
                alpha=0.7, label='理论成本')
        ax7.bar(x + 0.2, actual_costs, 0.4, color='mediumpurple',
                alpha=0.7, label='实际成本')

        ax7.set_xlabel('车辆类型')
        ax7.set_ylabel('运输成本 (元)')
        ax7.set_title('时间约束对成本的影响')
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        ax7.set_xticks(x)
        ax7.set_xticklabels(x_labels)

        # 绘制车辆数对比
        ax8 = plt.subplot(3, 2, 6)

        theoretical_vehicles_plot = [math.ceil(results['total_demands'][k] / self.Q[k]) for k in range(4)]
        actual_vehicles = results['vehicle_counts'].astype(int)

        ax8.bar(x - 0.2, theoretical_vehicles_plot, 0.4, color='lightgreen',
                alpha=0.7, label='理论车辆数')
        ax8.bar(x + 0.2, actual_vehicles, 0.4, color='lightcoral',
                alpha=0.7, label='实际车辆数')

        ax8.set_xlabel('车辆类型')
        ax8.set_ylabel('车辆数量')
        ax8.set_title('时间约束对车辆数的影响')
        ax8.legend()
        ax8.grid(True, alpha=0.3)
        ax8.set_xticks(x)
        ax8.set_xticklabels(x_labels)

        plt.tight_layout()
        plt.savefig('Time_Constrained_Multi_Vehicle_CVRP.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("\n结果图已保存为 Time_Constrained_Multi_Vehicle_CVRP.png")

    def demonstrate_time_constraint_effect(self):
        """时间约束影响案例演示"""
        print("\n=== 时间约束影响案例演示 ===")

        print("案例1: 厨余垃圾车任务拆分")
        print("原始方案: 车辆A负责收集点{1,4,6,13,19}")
        print("  - 估算距离: 85公里")
        print("  - 行驶时间: 85/40 = 2.13小时")
        print("  - 服务时间: 5×0.25 + 0.1 = 1.35小时")
        print("  - 总工作时间: 3.48小时 < 8小时 ✓")

        print("\n服务时间增加后:")
        print("  - 服务时间: 5×0.5 + 0.2 = 2.7小时")
        print("  - 总工作时间: 4.83小时 < 8小时 ✓")

        print("\n路况恶化，距离增加30%:")
        print("  - 实际距离: 110公里")
        print("  - 行驶时间: 110/40 = 2.75小时")
        print("  - 总工作时间: 5.45小时 < 8小时 ✓")

        print("\n极端情况，工作时间限制为4小时:")
        print("  - 总工作时间: 5.45小时 > 4小时 ✗")
        print("  - 解决方案: 拆分为两辆车")
        print("    车辆A1: {1,4,6} → 约3.2小时")
        print("    车辆A2: {13,19} → 约2.8小时")
        print("  - 成本影响: 增加约30%")

        print("\n案例2: 有害垃圾车的时间压力")
        print("有害垃圾车工作时间限制: 6小时")
        print("单车理论最大服务点数: 约8-10个")
        print("实际考虑行驶距离后: 约5-7个")
        print("这解释了为什么有害垃圾可能需要更多车辆")


def main():
    """主函数"""
    print("开始求解含时间约束的多车型垃圾运输路径优化问题...")

    # 创建求解器实例
    solver = TimeConstrainedMCHFVRP()

    # 求解问题
    results = solver.solve()

    # 演示时间约束效应
    solver.demonstrate_time_constraint_effect()

    print("\n=== 求解完成 ===")
    print(f"最终结果: 使用{int(np.sum(results['vehicle_counts']))}辆车, "
          f"总成本{np.sum(results['costs']):.2f}元")

    return results


if __name__ == "__main__":
    results = main()