%% 非对称网络下的中转站收益评估
function [benefit, allocation] = evaluate_asymmetric_station_benefit(...
    station_id, capacities, remaining_demand, transport_costs, build_cost, demands)
    
    n = length(transport_costs);
    transport_savings = 0;
    allocation = [];
    
    [sorted_costs, sorted_indices] = sort(transport_costs);
    current_capacity = capacities;
    
    for i = 1:length(sorted_indices)
        point_idx = sorted_indices(i);
        point_demands = demands(point_idx + 1, :);
        
        if all(point_demands <= current_capacity)
            allocation = [allocation, point_idx - 1]; % 转为0-based
            current_capacity = current_capacity - point_demands;
            transport_savings = transport_savings + max(0, 60 - sorted_costs(i));
        end
    end
    
    benefit = transport_savings - build_cost;
end

