import pulp
import pandas as pd

# 读取收集点坐标及垃圾量数据
df1 = pd.read_excel('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\附件1.xlsx', sheet_name='附件130个垃圾分类收集点坐标及总垃圾量')
# 读取距离矩阵数据
df2 = pd.read_excel('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\distance_matrix.xlsx', sheet_name='Sheet1')

# 提取收集点信息，原表头和第一行数据是说明性内容，从第二行开始加载
collection_points = df1[2:]
# 设置列名
collection_points.columns = ['收集点编号', 'x坐标(km)', 'y坐标(km)', '垃圾量(吨)', '备注']
# 重置索引
collection_points = collection_points.reset_index(drop=True)

# 定义节点集合，包含垃圾处理厂（编号0）和各个收集点
V = list(range(len(collection_points) + 1))

# 获取距离矩阵
distance_matrix = df2.values

# 获取每个收集点的垃圾量，垃圾处理厂（编号0）的垃圾量设为0
waste_amount = [0] + collection_points['垃圾量(吨)'].tolist()

# 车辆最大载重
Q = 5

# 假设车辆集合，这里简单设定为 10 辆车，可根据实际情况调整
K = list(range(20))

# 创建线性规划问题，目标是最小化总行驶距离
prob = pulp.LpProblem("GarbageCollectionRouting", pulp.LpMinimize)

# 定义决策变量 x[i, j, k]，如果车辆 k 从节点 i 到节点 j 有路径则为 1，否则为 0
x = {}
for k in K:
    for i in V:
        for j in V:
            x[(i, j, k)] = pulp.LpVariable(f"x_{i}_{j}_{k}", cat='Binary')

# 定义目标函数：最小化所有车辆所有可能路径的距离总和
prob += pulp.lpSum(distance_matrix[i][j] * x[(i, j, k)] for k in K for i in V for j in V if i != j)

# 添加约束条件

# 每辆车从垃圾处理厂（编号0）出发一次
for k in K:
    prob += pulp.lpSum(x[(0, j, k)] for j in V if j != 0) <= 1

# 每辆车回到垃圾处理厂（编号0）一次
for k in K:
    prob += pulp.lpSum(x[(i, 0, k)] for i in V if i != 0) <= 1

# 对于每个收集点（非0节点），恰好被一辆车访问一次
for n in V[1:]:
    prob += pulp.lpSum(x[(i, n, k)] for k in K for i in V if i != n) == 1

# 载重约束，每辆车收集的垃圾量不超过最大载重
for k in K:
    prob += pulp.lpSum(waste_amount[j] * x[(i, j, k)] for i in V for j in V[1:]) <= Q

# 流量守恒约束，确保车辆进入和离开每个节点的一致性
for k in K:
    for n in V:
        prob += pulp.lpSum(x[(i, n, k)] for i in V if i != n) == pulp.lpSum(x[(n, j, k)] for j in V if j != n)

# 消除子回路约束（使用 Miller - Tucker - Zemlin 约束）
u = {}
for k in K:
    for i in V[1:]:
        u[(i, k)] = pulp.LpVariable(f"u_{i}_{k}", lowBound=1, upBound=len(V) - 1, cat='Integer')

for k in K:
    for i in V[1:]:
        for j in V[1:]:
            if i != j:
                prob += u[(i, k)] - u[(j, k)] + (len(V) - 1) * x[(i, j, k)] <= len(V) - 2

# 设置求解器参数，限制求解时间为 60 秒
solver = pulp.PULP_CBC_CMD(timeLimit=600)

# 求解线性规划问题
prob.solve(solver)

# 输出结果
if pulp.LpStatus[prob.status] == 'Optimal':
    for k in K:
        path = []
        start_node = None
        for i in V:
            for j in V:
                if x[(i, j, k)].varValue == 1:
                    if start_node is None:
                        start_node = i
                    path.append((i, j))
        if path:
            print(f"车辆 {k} 的最优路径: 0 -> ", end='')
            for edge in path:
                if edge[0] != 0:
                    print(f"{edge[0]} -> ", end='')
            print("0")
    print(f"最小总行驶距离: {pulp.value(prob.objective)}")
elif pulp.LpStatus[prob.status] == 'Not Solved':
    print("在 60 秒内未能找到解，请尝试增加求解时间或检查问题设置。")
else:
    print("未找到最优解")