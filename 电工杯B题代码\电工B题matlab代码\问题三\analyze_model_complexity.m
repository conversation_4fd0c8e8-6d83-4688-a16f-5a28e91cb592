%% 模型复杂度分析
function analyze_model_complexity(n, m)
    
    fprintf('\n=== 模型复杂度分析 ===\n');
    
    fprintf('问题规模:\n');
    fprintf('  收集点数量: %d\n', n);
    fprintf('  候选中转站数量: %d\n', m);
    fprintf('  车辆类型数量: 4\n');
    
    fprintf('\n第一阶段（设施选址）复杂度:\n');
    fprintf('  决策变量数量: O(n×m) = O(%d×%d) = O(%d)\n', n, m, n*m);
    fprintf('  约束数量: O(n+m) = O(%d)\n', n+m);
    fprintf('  时间复杂度: O(n×m×log(m)) = O(%d)\n', n*m*ceil(log2(m)));
    
    fprintf('\n第二阶段（路径优化）复杂度:\n');
    selected_stations_count = min(3, m); % 假设选择3个中转站
    avg_points_per_station = ceil(n / selected_stations_count);
    
    fprintf('  每个中转站平均服务点数: %d\n', avg_points_per_station);
    fprintf('  单中转站CVRP复杂度: O(n²) = O(%d²) = O(%d)\n', ...
            avg_points_per_station, avg_points_per_station^2);
    fprintf('  总路径优化复杂度: O(k×s×n²) = O(4×%d×%d) = O(%d)\n', ...
            selected_stations_count, avg_points_per_station^2, ...
            4*selected_stations_count*avg_points_per_station^2);
    
    fprintf('\n整体算法复杂度:\n');
    total_complexity = n*m*ceil(log2(m)) + 4*selected_stations_count*avg_points_per_station^2;
    fprintf('  总时间复杂度: O(%d)\n', total_complexity);
    fprintf('  空间复杂度: O((n+m)²) = O(%d²) = O(%d)\n', n+m, (n+m)^2);
    
    fprintf('\n与传统VRP的复杂度对比:\n');
    traditional_vrp = n^2;
    lrp_complexity = total_complexity;
    
    fprintf('  传统VRP: O(n²) = O(%d)\n', traditional_vrp);
    fprintf('  LRP问题: O(%d)\n', lrp_complexity);
    fprintf('  复杂度增加倍数: %.1f\n', lrp_complexity / traditional_vrp);
    
    fprintf('\n求解策略建议:\n');
    if n <= 50 && m <= 10
        fprintf('  小规模问题: 可使用精确算法求解\n');
    elseif n <= 100 && m <= 20
        fprintf('  中等规模问题: 建议使用启发式算法\n');
    else
        fprintf('  大规模问题: 必须使用元启发式或分解算法\n');
    end
end
