%% 修改后的2-opt改进
function improved_route = two_opt_improvement_modified(route, dist_matrix)
    improved_route = route;
    n = length(route);
    
    if n <= 3
        return;
    end
    
    improved = true;
    max_iterations = 50;
    iteration = 0;
    
    while improved && iteration < max_iterations
        improved = false;
        iteration = iteration + 1;
        
        for i = 2:n-2
            for j = i+1:n-1
                if j - i == 1
                    continue;
                end
                
                % 计算改进量
                current_dist = dist_matrix(improved_route(i-1)+1, improved_route(i)+1) + ...
                              dist_matrix(improved_route(j)+1, improved_route(j+1)+1);
                
                new_dist = dist_matrix(improved_route(i-1)+1, improved_route(j)+1) + ...
                          dist_matrix(improved_route(i)+1, improved_route(j+1)+1);
                
                if new_dist < current_dist
                    % 执行2-opt交换
                    improved_route(i:j) = improved_route(j:-1:i);
                    improved = true;
                end
            end
        end
    end
end

