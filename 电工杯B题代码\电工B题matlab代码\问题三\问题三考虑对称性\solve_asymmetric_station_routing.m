%% 单中转站非对称路径优化
function [routes, cost, emissions] = solve_asymmetric_station_routing(...
    station_id, assigned_points, coords, ts_coords, demands, ...
    dist_matrix, time_matrix, Q, C, alpha, beta, vehicle_speed)
    
    routes = cell(4, 1);
    cost = 0;
    emissions = 0;
    
    station_local_idx = station_id + 1;
    station_global_idx = size(coords, 1) + station_local_idx;
    
    for k = 1:4
        if isempty(assigned_points)
            continue;
        end
        
        % 提取k类垃圾的需求
        k_demands = [];
        valid_points = [];
        
        for point_idx = assigned_points
            if point_idx >= 0 && point_idx < size(demands, 1) - 1
                demand = demands(point_idx + 1, k);
                if demand > 0
                    k_demands = [k_demands, demand];
                    valid_points = [valid_points, point_idx];
                end
            end
        end
        
        if isempty(valid_points)
            continue;
        end
        
        % 非对称TSP求解
        [k_routes, k_cost, k_emissions] = optimize_asymmetric_routes(...
            valid_points, station_global_idx, k_demands, k, ...
            dist_matrix, time_matrix, Q(k), C(k), alpha(k), beta(k));
        
        routes{k} = k_routes;
        cost = cost + k_cost;
        emissions = emissions + k_emissions;
    end
end


