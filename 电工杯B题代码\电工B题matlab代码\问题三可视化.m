%% PROBLEM3_FINAL_VISUALIZATION_FIX.M
% 修正索引后的多视角可视化

clear; clc; close all;

%% 1. 数据准备
% coords: [0号厂区;1–30号收集点]
% st_all: [候选31–35号中转站坐标]
% sel_ids: 已选站号（1–5 对应 31–35）
% bestAssign, bestRoutes 已在工作区
% C: 4×1 单位距离成本

% 示例占位（请替换为真实结果）
coords  = [0,0; 12,8; 5,15; 20,30; 25,10; 35,22; 18,5; 30,35; 10,25; 22,18; ...
           38,15; 5,8; 15,32; 28,5; 30,12; 10,10; 20,20; 35,30; 8,22; 25,25; ...
           32,8; 15,5; 28,20;38,25; 10,30; 20,10; 30,18; 5,25; 18,30; 35,10; 22,35];
st_all  = [12,5; 7,28; 20,8; 30,15; 25,10];
sel_ids = [3,2,1];  % 例如启用 33,32,31
bestAssign = randi(3,30,4);
bestRoutes  = cell(5,4);
for s=1:5, for k=1:4, bestRoutes{s,k} = {[0, s+30, 1,0]}; end, end
C = [2.5,2.0,5.0,1.8];

%% 2. 构建全局坐标矩阵
% all_coords_global(u+1,:) 正好对应节点 u：
%   u=0 → row 1 是厂区，u=1..30 → rows 2..31，u=31..35 → rows 32..36
all_coords_global = [coords; st_all];

%% 3. 全局运输路线总览
figure('Name','全局运输路线总览','NumberTitle','off','Position',[100,100,800,600]);
hold on; grid on;
scatter(coords(1,1),coords(1,2),120,'kp','filled'); text(coords(1,1),coords(1,2),' 厂区');
scatter(coords(2:end,1),coords(2:end,2),50,'bo');
for i=2:31, text(coords(i,1)+0.3,coords(i,2)+0.3,sprintf('%d',i-1)); end
sel_global_rows = sel_ids + 31; % e.g. sel_ids=[3,2,1] → rows [34,33,32]
scatter(all_coords_global(sel_global_rows,1),all_coords_global(sel_global_rows,2),120,'rs','filled');
for idx=1:numel(sel_ids)
  row = sel_ids(idx)+31;
  text(all_coords_global(row,1)+0.3,all_coords_global(row,2)+0.3,...
       sprintf(' S%d',sel_ids(idx)+30));
end
linestyles = {'-','--',':','-.'};
colors     = lines(numel(sel_ids));
for idx=1:numel(sel_ids)
  s = sel_ids(idx);
  for k=1:4
    routes_k = bestRoutes{s,k};
    for r = routes_k
      rt = r{1};
      pts = all_coords_global(rt+1, :);  % 直接索引
      plot(pts(:,1), pts(:,2), 'LineStyle',linestyles{k}, ...
           'Color',colors(idx,:), 'LineWidth',1.5);
    end
  end
end
title('全局运输路线总览','FontSize',14);
xlabel('X (km)'); ylabel('Y (km)');
legend({'厂区','客户','中转站','路线'},'Location','eastoutside');

%% 4. 分站点分类型子图
nEnabled = numel(sel_ids);
figure('Name','各站分类型运输路径','NumberTitle','off', 'Position',[150,150,1000,300*nEnabled]);
plotIdx = 1;
for idx=1:numel(sel_ids)
  s = sel_ids(idx);
  for k=1:4
    subplot(nEnabled,4,plotIdx); hold on; grid on;
    row = s + 31;
    scatter(all_coords_global(row,1), all_coords_global(row,2),100,'rs','filled');
    text(all_coords_global(row,1)+0.3, all_coords_global(row,2)+0.3,...
         sprintf('S%d',s+30));
    pts = find(bestAssign(:,k)==s) + 1;
    scatter(coords(pts,1), coords(pts,2),60,'bo');
    for i=pts', text(coords(i,1)+0.2,coords(i,2)+0.2,sprintf('%d',i-1),'FontSize',8); end
    routes_k = bestRoutes{s,k};
    for r = routes_k
      rt  = r{1};
      pts = all_coords_global(rt+1,:);
      plot(pts(:,1), pts(:,2), '-o','LineWidth',1.2);
    end
    title(sprintf('站 %d - 类型 %d',s+30,k));
    xlabel('X'); ylabel('Y'); axis equal;
    plotIdx = plotIdx + 1;
  end
end

%% 5. 成本与里程柱状图
% 计算各站各类型总里程（示例随机，请替换真实数据）
distances = rand(numel(sel_ids),4)*50 + 50;
figure('Name','距离统计','NumberTitle','off','Position',[200,200,800,400]);
bar(distances,'grouped');
title('各站各类型总里程');
set(gca,'XTickLabel',arrayfun(@(s)sprintf('S%d',s+30),sel_ids,'Uni',false));
ylabel('里程 (km)');
legend(arrayfun(@(k)sprintf('类型%d',k),1:4,'Uni',false),'Location','northoutside');

%% 6. 单类型雷达对比
% 汇总每类型里程
radarData = sum(distances,1);
theta = linspace(0,2*pi,5);
radarVals = [radarData radarData(1)];
figure('Name','类型间里程雷达','NumberTitle','off','Position',[300,300,600,600]);
polarplot(theta, radarVals,'-o','LineWidth',1.5);
thetaticks([0 72 144 216 288]);
thetaticklabels({'厨余','可回收','有害','其他','厨余'});
title('各垃圾类型总里程对比');
