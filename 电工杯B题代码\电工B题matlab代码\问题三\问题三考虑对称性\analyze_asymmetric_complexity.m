%% 复杂度对比分析
function analyze_asymmetric_complexity(n, m, constraints)
    fprintf('非对称网络复杂度分析:\n');
    
    % 对称网络复杂度
    symmetric_space = n * (n - 1) / 2;  % 对称矩阵只需存储上三角
    symmetric_time = n^2;               % TSP求解复杂度
    
    % 非对称网络复杂度
    asymmetric_space = n * (n - 1);     % 非对称矩阵需要存储所有边
    asymmetric_time = n^2 * 2;          % 非对称TSP复杂度更高
    
    fprintf('  存储复杂度:\n');
    fprintf('    对称网络: O(%d) = %d\n', n*(n-1)/2, symmetric_space);
    fprintf('    非对称网络: O(%d) = %d\n', n*(n-1), asymmetric_space);
    fprintf('    复杂度增加: %.1f倍\n', asymmetric_space / symmetric_space);
    
    fprintf('  计算复杂度:\n');
    fprintf('    对称TSP: O(n²) = O(%d)\n', symmetric_time);
    fprintf('    非对称TSP: O(2n²) = O(%d)\n', asymmetric_time);
    fprintf('    复杂度增加: %.1f倍\n', asymmetric_time / symmetric_time);
    
    fprintf('  约束处理:\n');
    fprintf('    时间相关约束: %d条\n', sum(constraints(:,4) > 0));
    fprintf('    单向道路约束: %d条\n', size(constraints, 1) - sum(constraints(:,4) > 0));
    
    % 求解策略建议
    fprintf('  求解策略建议:\n');
    if n <= 30 && size(constraints, 1) <= 10
        fprintf('    小规模非对称问题: 可使用精确算法\n');
    elseif n <= 100 && size(constraints, 1) <= 50
        fprintf('    中等规模: 建议启发式算法 + 局部搜索\n');
    else
        fprintf('    大规模: 必须使用元启发式算法\n');
    end
end
