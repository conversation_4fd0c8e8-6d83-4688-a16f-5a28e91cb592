
clear; clc; close all;
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 1. 数据输入
% 收集点坐标和垃圾产生量数据
data = [
    0,  0,  0,   0;     % 处理厂(编号0)
    1,  12, 8,   1.2;   % 收集点1
    2,  5,  15,  2.3;   % 收集点2
    3,  20, 30,  1.8;   % 收集点3
    4,  25, 10,  3.1;   % 收集点4
    5,  35, 22,  2.7;   % 收集点5
    6,  18, 5,   1.5;   % 收集点6
    7,  30, 35,  2.9;   % 收集点7
    8,  10, 25,  1.1;   % 收集点8
    9,  22, 18,  2.4;   % 收集点9
    10, 38, 15,  3.0;   % 收集点10
    11, 5,  8,   1.7;   % 收集点11
    12, 15, 32,  2.1;   % 收集点12
    13, 28, 5,   3.2;   % 收集点13
    14, 30, 12,  2.6;   % 收集点14
    15, 10, 10,  1.9;   % 收集点15
    16, 20, 20,  2.5;   % 收集点16%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    17, 35, 30,  3.3;   % 收集点17
    18, 8,  22,  1.3;   % 收集点18
    19, 25, 25,  2.8;   % 收集点19
    20, 32, 8,   3.4;   % 收集点20
    21, 15, 5,   1.6;   % 收集点21
    22, 28, 20,  2.2;   % 收集点22
    23, 38, 25,  3.5;   % 收集点23
    24, 10, 30,  1.4;   % 收集点24
    25, 20, 10,  2.0;   % 收集点25
    26, 30, 18,  3.6;   % 收集点26
    27, 5,  25,  1.0;   % 收集点27
    28, 18, 30,  2.3;   % 收集点28
    29, 35, 10,  3.7;   % 收集点29
    30, 22, 35,  1.9;   % 收集点30
];

% 提取坐标和需求量
coords = data(:, 2:3);  % 坐标 [x, y]
demands = data(:, 4);   % 垃圾产生量
n = size(coords, 1) - 1; % 收集点数量（不包括处理厂）%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
Q = 5; % 车辆最大载重量（吨）

fprintf('问题规模: %d个收集点, 车辆载重: %.1f吨\n', n, Q);
fprintf('总垃圾量: %.1f吨\n', sum(demands(2:end)));
fprintf('理论最少车辆数: %d辆\n', ceil(sum(demands(2:end))/Q));

%% 2. 计算距离矩阵
dist_matrix = zeros(n+1, n+1);
for i = 1:n+1
    for j = 1:n+1
        if i ~= j
            dist_matrix(i,j) = sqrt((coords(i,1) - coords(j,1))^2 + ...
                                  (coords(i,2) - coords(j,2))^2);
        end
    end
end

fprintf('距离矩阵计算完成\n');

%% 3. 主求解函数
tic;
[best_routes, best_distance, vehicle_count] = solve_cvrp(coords, demands, Q, dist_matrix);
solve_time = toc;

%% 4. 结果输出
fprintf('\n========== 求解结果 ==========\n');
fprintf('求解时间: %.4f秒\n', solve_time);
fprintf('使用车辆数量: %d辆\n', vehicle_count);
fprintf('总行驶距离: %.2f公里\n', best_distance);
fprintf('平均每辆车行驶距离: %.2f公里\n', best_distance/vehicle_count);

% 输出每辆车的路径详情
for v = 1:length(best_routes)
    route = best_routes{v};
    route_demand = sum(demands(route(2:end-1)+1)); % +1因为索引差异
    route_distance = calculate_route_distance(route, dist_matrix);
    
    fprintf('\n车辆%d路径: ', v);
    for i = 1:length(route)
        if i == length(route)
            fprintf('%d', route(i));
        else
            fprintf('%d -> ', route(i));
        end
    end
    fprintf('\n  载重: %.1f/%.1f吨, 行驶距离: %.2f公里\n', ...
            route_demand, Q, route_distance);
end

%% 5. 可视化结果
visualize_solution(coords, best_routes, best_distance);

%% 6. 模型复杂度分析
fprintf('\n========== 复杂度分析 ==========\n');
fprintf('时间复杂度: O(n²) = O(%d²) = O(%d)\n', n, n^2);
fprintf('空间复杂度: O(n²) = O(%d²) = O(%d)\n', n, n^2);






