%% 扫描算法进行聚类
function clusters = sweep_algorithm(coords, demands, Q)
    n = size(coords, 1) - 1;
    depot = coords(1, :); % 处理厂坐标
    %论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    % 计算每个收集点相对于处理厂的极角
    angles = zeros(n, 1);
    for i = 1:n
        dx = coords(i+1, 1) - depot(1);
        dy = coords(i+1, 2) - depot(2);
        angles(i) = atan2(dy, dx);
    end
    
    % 按极角排序
    [~, sorted_idx] = sort(angles);
    
    % 按顺序分配到车辆
    clusters = {};
    current_cluster = [];
    current_load = 0;
    cluster_count = 0;
    
    for i = 1:n
        idx = sorted_idx(i);
        point_demand = demands(idx + 1); % +1因为索引差异
        %论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        if current_load + point_demand <= Q
            current_cluster = [current_cluster, idx];
            current_load = current_load + point_demand;
        else
            % 当前聚类已满，开始新聚类
            if ~isempty(current_cluster)
                cluster_count = cluster_count + 1;
                clusters{cluster_count} = current_cluster;
            end
            current_cluster = [idx];
            current_load = point_demand;
        end
    end
    
    % 添加最后一个聚类
    if ~isempty(current_cluster)
        cluster_count = cluster_count + 1;
        clusters{cluster_count} = current_cluster;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    end
    
    fprintf('  聚类完成: %d个聚类\n', length(clusters));
end

