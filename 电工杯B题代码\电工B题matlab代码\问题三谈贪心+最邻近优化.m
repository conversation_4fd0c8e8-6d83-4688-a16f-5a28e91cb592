%% PROBLEM3_INCREMENTAL_GREEDY_SELECT_COMPLETE.M
% 问题三：增量贪心选址 + 路径优化 + 全面可视化（MATLAB）
clear; clc; close all;

%% 1. 基本数据
coords = [
    0,0;
    12,8;
    5,15;
    20,30;
    25,10;
    35,22;
    18,5;
    30,35;
    10,25;
    22,18;
    38,15;
    5,8;
    15,32;
    28,5;
    30,12;
    10,10;
    20,20;
    35,30;
    8,22;
    25,25;
    32,8;
    15,5;
    28,20;
    38,25;
    10,30;
    20,10;
    30,18;
    5,25;
    18,30;
    35,10;
    22,35
];  % same coords
n = size(coords,1) - 1;  % 客户数量 30
w_all = [zeros(1,4); 0.72,0.12,0.06,0.30;1.38,0.23,0.05,0.64;1.08,0.18,0.04,0.50;...
         1.55,0.31,0.06,1.18;1.62,0.27,0.05,0.76;1.76,0.384,0.096,0.96;...
         0.77,0.168,0.042,0.42;1.02,0.238,0.068,0.374;1.32,0.176,0.044,0.66;...
         1.45,0.30,0.075,0.675;1.35,0.27,0.108,0.972;1.87,0.51,0.068,0.952;...
         2.58,0.516,0.129,1.075;1.134,0.21,0.063,0.693;0.78,0.13,0.065,0.325;...
         0.768,0.192,0.080,0.56;0.72,0.27,0.090,0.72;1.595,0.348,0.087,0.87;...
         1.50,0.36,0.090,1.05;1.08,0.18,0.090,0.45;0.912,0.19,0.038,0.76;...
         0.90,0.195,0.075,0.33;0.99,0.27,0.072,0.468;1.44,0.24,0.048,0.672;...
         1.74,0.319,0.116,0.725;1.17,0.39,0.13,0.91;1.70,0.34,0.17,1.19;...
         2.64,0.66,0.044,1.056;0.864,0.216,0.072,0.648;0.986,0.204,0.085,0.425];
st_coords = [12,5;7,28;20,8;30,15;25,10];  % 中转站31..35
m = size(st_coords,1);
Tj = 10000;  % 建设成本(元/天)
Q = [8,6,3,10]; C = [2.5,2.0,5.0,1.8]; v = 40;

%% 2. 距离矩阵
all_coords = [coords; st_coords];  % 36x2
D = squareform(pdist(all_coords));

%% 4. 增量贪心选址
S = false(m,1);
assign0 = zeros(n,4);
[~, currTransCost] = solveCVRP(true(m,1), assign0, n, m, D, Q, C, w_all);
bestAssign = assign0; bestRoutes = cell(m,4); bestY = S;

while true
    bestDelta = 0; sel = 0; tempAssign = []; tempRoutes = [];
    for s0 = find(~S)'
        yTemp = S; yTemp(s0) = true;
        % 分配到最近站
        assignTemp = zeros(n,4);
        for ii = 1:n
            for kk = 1:4
                dmin = inf; selk = 0;
                for ss = 1:m
                    if yTemp(ss)
                        dtmp = D(ii+1, n+ss+1);
                        if dtmp < dmin
                            dmin = dtmp; selk = ss;
                        end
                    end
                end
                assignTemp(ii,kk) = selk;
            end
        end
        [routesTemp, tcTemp] = solveCVRP(yTemp, assignTemp, n, m, D, Q, C, w_all);
        delta = (currTransCost - tcTemp) - Tj;
        if delta > bestDelta
            bestDelta = delta; sel = s0;
            tempAssign = assignTemp; tempRoutes = routesTemp;
        end
    end
    if bestDelta <= 0
        break;
    end
    % 更新选址
    S(sel) = true;
    currTransCost = currTransCost - bestDelta - Tj;
    bestAssign = tempAssign;
    bestRoutes = tempRoutes;
    bestY = S;
end

% 保证至少增加一个中转站
if ~any(bestY)
    minCost = inf; pick = 0;
    for s0 = 1:m
        yTemp = false(m,1); yTemp(s0) = true;
        % 分配
        assignTemp = zeros(n,4);
        for ii = 1:n
            for kk = 1:4
                dmin = inf; selk = 0;
                for ss = 1:m
                    if yTemp(ss)
                        dtmp = D(ii+1, n+ss+1);
                        if dtmp < dmin
                            dmin = dtmp; selk = ss;
                        end
                    end
                end
                assignTemp(ii,kk) = selk;
            end
        end
        [routesTemp, tcTemp] = solveCVRP(yTemp, assignTemp, n, m, D, Q, C, w_all);
        totalTemp = tcTemp + Tj;
        if totalTemp < minCost
            minCost = totalTemp; pick = s0;
            bestAssign = assignTemp; bestRoutes = routesTemp;
        end
    end
    bestY = false(m,1); bestY(pick) = true;
    currTransCost = minCost - Tj;
end

bestCost = currTransCost + sum(bestY)*Tj;

%% 5. 输出
fprintf('选址中转站：'); for ss=1:m, if bestY(ss), fprintf('%d ', n+ss); end; end; fprintf('\n');
fprintf('最小总成本：%.2f\n', bestCost);
for ss=1:m
    if ~bestY(ss), continue; end; j=n+ss;
    fprintf('\n-- 中转站 %d 路线 --\n',j);
    for kk=1:4
        for vv=1:numel(bestRoutes{ss,kk})
            fprintf('类型%d 车%d: %s\n',kk,vv,mat2str(bestRoutes{ss,kk}{vv}));
        end
    end
end

%% 6. 可视化
% 全局概览
figure('Name','全局运输路线','NumberTitle','off'); hold on; grid on;
scatter(all_coords(1,1),all_coords(1,2),100,'kd','filled'); text(all_coords(1,1),all_coords(1,2),' 处理厂');
scatter(all_coords(2:31,1),all_coords(2:31,2),40,'ko'); for i=1:30, text(all_coords(i+1,1),all_coords(i+1,2),[' 点',num2str(i)]); end
scatter(all_coords(32:36,1),all_coords(32:36,2),100,'rs','filled'); for s=1:m, if bestY(s), text(all_coords(n+s+1,1),all_coords(n+s+1,2),[' 中转',num2str(n+s)]); end; end
colors = lines(m); lstyles={'-','--',':','-.'};
for s=1:m
    if ~bestY(s), continue; end; j_idx=n+s+1;
    for kk=1:4
        for idx=1:numel(bestRoutes{s,kk})
            route = bestRoutes{s,kk}{idx};
            plot(all_coords(route,1),all_coords(route,2),'Color',colors(s,:),'LineStyle',lstyles{kk},'LineWidth',1.5);
        end
    end
end
title('全局运输路线概览'); xlabel('X (km)'); ylabel('Y (km)'); legend({'厂区','客户','中转站','路线'},'Location','eastoutside');

% 各站详图
for s=1:m
    if ~bestY(s), continue; end; j_idx=n+s+1;
    figure('Name',sprintf('中转站%d 运输详细',n+s),'NumberTitle','off'); hold on; grid on;
    scatter(all_coords(j_idx,1),all_coords(j_idx,2),100,'rs','filled'); text(all_coords(j_idx,1),all_coords(j_idx,2),[' 中转',num2str(n+s)]);
    for kk=1:4
        pts=find(bestAssign(:,kk)==s);
        scatter(all_coords(pts+1,1),all_coords(pts+1,2),50,'bo'); for i=pts', text(all_coords(i+1,1),all_coords(i+1,2),[' 点',num2str(i)]); end
        for idx=1:numel(bestRoutes{s,kk})
            route=bestRoutes{s,kk}{idx}; plot(all_coords(route,1),all_coords(route,2),'LineWidth',1.5);
        end
    end
title(sprintf('中转站%d 运输详细',n+s)); xlabel('X'); ylabel('Y');
end
