%% 问题三：含非对称路网的中转站选址与时间窗口综合优化（简化版）
% 支持单行道、禁行时段等非对称约束

clear; clc; close all;

%% 1. 数据输入与初始化
fprintf('=== 问题三：含非对称路网的综合优化 ===\n');

% 收集点坐标和各类垃圾产生量数据
collection_data = [
    0,  0,  0,   0,     0,     0,     0;      % 处理厂
    1,  12, 8,   0.72,  0.12,  0.06,  0.3;   % 收集点1
    2,  5,  15,  1.38,  0.23,  0.05,  0.64;  % 收集点2
    3,  20, 30,  1.08,  0.18,  0.04,  0.5;   % 收集点3
    4,  25, 10,  1.55,  0.31,  0.06,  1.18;  % 收集点4
    5,  35, 22,  1.62,  0.27,  0.05,  0.76;  % 收集点5
    6,  18, 5,   1.76,  0.384, 0.096, 0.96;  % 收集点6
    7,  30, 35,  0.77,  0.168, 0.042, 0.42;  % 收集点7
    8,  10, 25,  1.02,  0.238, 0.068, 0.374; % 收集点8
    9,  22, 18,  1.32,  0.176, 0.044, 0.66;  % 收集点9
    10, 38, 15,  1.45,  0.3,   0.075, 0.675; % 收集点10
    11, 5,  8,   1.35,  0.27,  0.108, 0.972; % 收集点11
    12, 15, 32,  1.87,  0.51,  0.068, 0.952; % 收集点12
    13, 28, 5,   2.58,  0.516, 0.129, 1.075; % 收集点13
    14, 30, 12,  1.134, 0.21,  0.063, 0.693; % 收集点14
    15, 10, 10,  0.78,  0.13,  0.065, 0.325; % 收集点15
    16, 20, 20,  0.768, 0.192, 0.08,  0.56;  % 收集点16
    17, 35, 30,  0.72,  0.27,  0.09,  0.72;  % 收集点17
    18, 8,  22,  1.595, 0.348, 0.087, 0.87;  % 收集点18
    19, 25, 25,  1.5,   0.36,  0.09,  1.05;  % 收集点19
    20, 32, 8,   1.08,  0.18,  0.09,  0.45;  % 收集点20
    21, 15, 5,   0.912, 0.19,  0.038, 0.76;  % 收集点21
    22, 28, 20,  0.9,   0.195, 0.075, 0.33;  % 收集点22
    23, 38, 25,  0.99,  0.27,  0.072, 0.468; % 收集点23
    24, 10, 30,  1.44,  0.24,  0.048, 0.672; % 收集点24
    25, 20, 10,  1.74,  0.319, 0.116, 0.725; % 收集点25
    26, 30, 18,  1.17,  0.39,  0.13,  0.91;  % 收集点26
    27, 5,  25,  1.7,   0.34,  0.17,  1.19;  % 收集点27
    28, 18, 30,  2.64,  0.66,  0.044, 1.056; % 收集点28
    29, 35, 10,  0.864, 0.216, 0.072, 0.648; % 收集点29
    30, 22, 35,  0.986, 0.204, 0.085, 0.425; % 收集点30
];

% 候选中转站数据
transfer_station_data = [
    31, 15, 15, 50000, 6,  18, 15, 10, 5,  20;  % 中转站1
    32, 25, 20, 45000, 7,  17, 12, 8,  4,  18;  % 中转站2
    33, 10, 25, 40000, 8,  16, 10, 6,  3,  15;  % 中转站3
    34, 30, 10, 55000, 6,  18, 18, 12, 6,  25;  % 中转站4
    35, 20, 30, 48000, 7,  17, 14, 9,  4,  20;  % 中转站5
];

% 非对称路网约束
asymmetric_constraints = [
    4,  31, 18, -1, -1;  % 收集点4到中转站31，单行道绕行
    31, 4,  15, -1, -1;  % 中转站31到收集点4
    27, 28, 14, -1, -1;  % 收集点27到28，单行道绕行
    28, 27, 18, -1, -1;  % 收集点28到27
    23, 0,  45, 9,  12;  % 收集点23到处理厂，在9:00-12:00禁行需绕行
    0,  23, 40, -1, -1;  % 处理厂到收集点23
    9,  16, 8,  9,  11;  % 收集点9到16，在9:00-11:00禁行需绕行
    16, 9,  10, -1, -1;  % 收集点16到9
];

% 提取基础数据
coords = collection_data(:, 2:3);
demands = collection_data(:, 4:7);
n = size(coords, 1) - 1; % 收集点数量
m = size(transfer_station_data, 1); % 中转站数量

ts_coords = transfer_station_data(:, 2:3);
ts_costs = transfer_station_data(:, 4) / 10;
ts_capacities = transfer_station_data(:, 7:10);

% 车辆参数
Q = [8, 6, 3, 10];      % 容量
C = [2.5, 2.0, 5.0, 1.8]; % 单位成本
alpha = [0.8, 0.6, 1.2, 0.7]; % 距离排放系数
beta = [0.3, 0.2, 0.5, 0.25];  % 载重排放系数

fprintf('收集点数量: %d个\n', n);
fprintf('候选中转站数量: %d个\n', m);
fprintf('非对称约束数量: %d条\n', size(asymmetric_constraints, 1));

%% 2. 构建非对称距离矩阵
fprintf('\n=== 构建非对称距离矩阵 ===\n');

% 合并所有坐标
all_coords = [coords; ts_coords];
total_nodes = size(all_coords, 1);

% 初始化距离矩阵
dist_matrix = zeros(total_nodes, total_nodes);

% 计算基础欧几里得距离
for i = 1:total_nodes
    for j = 1:total_nodes
        if i ~= j
            dist_matrix(i,j) = sqrt((all_coords(i,1) - all_coords(j,1))^2 + ...
                                  (all_coords(i,2) - all_coords(j,2))^2);
        end
    end
end

% 应用非对称约束
fprintf('应用非对称约束:\n');
for k = 1:size(asymmetric_constraints, 1)
    from_node = asymmetric_constraints(k, 1);
    to_node = asymmetric_constraints(k, 2);
    distance = asymmetric_constraints(k, 3);
    
    % 转换节点编号为矩阵索引
    if from_node == 0
        from_idx = 1;
    elseif from_node <= 30
        from_idx = from_node + 1;
    else
        from_idx = from_node - 30 + n + 2;
    end
    
    if to_node == 0
        to_idx = 1;
    elseif to_node <= 30
        to_idx = to_node + 1;
    else
        to_idx = to_node - 30 + n + 2;
    end
    
    if from_idx <= total_nodes && to_idx <= total_nodes
        dist_matrix(from_idx, to_idx) = distance;
        fprintf('  约束 %d->%d: 距离%.1fkm\n', from_node, to_node, distance);
    end
end

%% 3. 第一阶段：设施选址优化（改进算法）
fprintf('\n=== 第一阶段：设施选址优化 ===\n');
tic;

% 计算每个收集点到各中转站的运输成本
transport_cost = zeros(n, m);
for i = 1:n
    for j = 1:m
        % 收集点到中转站的距离
        dist_to_ts = dist_matrix(i+1, n+1+j);
        % 中转站到处理厂的距离
        dist_ts_to_depot = dist_matrix(n+1+j, 1);
        transport_cost(i, j) = (dist_to_ts + dist_ts_to_depot) * mean(C);
    end
end

% 改进的贪心选址算法
selected_stations = [];
unassigned_points = 1:n;
total_facility_cost = 0;
allocation = {}; % 使用cell数组存储分配方案

while ~isempty(unassigned_points) && length(selected_stations) < m
    best_station = -1;
    best_benefit = -inf;
    best_assignment = [];
    
    % 评估每个未选择的中转站
    for j = 1:m
        if ismember(j, selected_stations)
            continue;
        end
        
        % 为当前中转站寻找最优分配
        current_capacity = ts_capacities(j, :);
        current_assignment = [];
        current_benefit = 0;
        temp_capacity = current_capacity;
        
        % 按运输成本排序未分配的收集点
        [sorted_costs, cost_order] = sort(transport_cost(unassigned_points, j));
        
        for k = 1:length(cost_order)
            point_idx = unassigned_points(cost_order(k));
            point_demand = demands(point_idx+1, :);
            
            % 检查容量约束
            if all(point_demand <= temp_capacity)
                current_assignment = [current_assignment, point_idx];
                temp_capacity = temp_capacity - point_demand;
                current_benefit = current_benefit + (50 - sorted_costs(k)); % 基准成本50
            end
        end
        
        % 计算净收益（考虑建设成本）
        net_benefit = current_benefit - ts_costs(j);
        
        if net_benefit > best_benefit && length(current_assignment) > 0
            best_benefit = net_benefit;
            best_station = j;
            best_assignment = current_assignment;
        end
    end
    
    % 如果找到有益的中转站，则选择它
    if best_station > 0
        selected_stations = [selected_stations, best_station];
        allocation{length(selected_stations)} = best_assignment;
        total_facility_cost = total_facility_cost + ts_costs(best_station);
        unassigned_points = setdiff(unassigned_points, best_assignment);
        
        fprintf('选择中转站 %d, 服务收集点 %d 个\n', ...
                best_station + 30, length(best_assignment));
    else
        % 如果没有有益的中转站，强制选择成本最低的
        remaining_stations = setdiff(1:m, selected_stations);
        if ~isempty(remaining_stations)
            [~, min_idx] = min(ts_costs(remaining_stations));
            forced_station = remaining_stations(min_idx);
            
            % 为强制选择的中转站分配剩余收集点
            forced_assignment = unassigned_points(1:min(length(unassigned_points), 5));
            
            selected_stations = [selected_stations, forced_station];
            allocation{length(selected_stations)} = forced_assignment;
            total_facility_cost = total_facility_cost + ts_costs(forced_station);
            unassigned_points = setdiff(unassigned_points, forced_assignment);
            
            fprintf('强制选择中转站 %d, 服务收集点 %d 个\n', ...
                    forced_station + 30, length(forced_assignment));
        end
    end
end

% 处理剩余未分配的收集点
if ~isempty(unassigned_points)
    % 将剩余收集点分配给最近的已选中转站
    for point = unassigned_points
        min_cost = inf;
        best_station_idx = 1;
        
        for s = 1:length(selected_stations)
            cost = transport_cost(point, selected_stations(s));
            if cost < min_cost
                min_cost = cost;
                best_station_idx = s;
            end
        end
        
        allocation{best_station_idx} = [allocation{best_station_idx}, point];
    end
    fprintf('剩余 %d 个收集点已重新分配\n', length(unassigned_points));
end

phase1_time = toc;

fprintf('第一阶段完成:\n');
fprintf('选择中转站数量: %d个\n', length(selected_stations));
fprintf('中转站编号: ');
for i = 1:length(selected_stations)
    fprintf('%d ', selected_stations(i) + 30);
end
fprintf('\n');
fprintf('设施建设成本: %.2f元/年\n', total_facility_cost);

%% 4. 第二阶段：路径优化
%% 问题三：含非对称路网的中转站选址与时间窗口综合优化（简化版）
% 支持单行道、禁行时段等非对称约束

clear; clc; close all;

%% 1. 数据输入与初始化
fprintf('=== 问题三：含非对称路网的综合优化 ===\n');

% 收集点坐标和各类垃圾产生量数据
collection_data = [
    0,  0,  0,   0,     0,     0,     0;      % 处理厂
    1,  12, 8,   0.72,  0.12,  0.06,  0.3;   % 收集点1
    2,  5,  15,  1.38,  0.23,  0.05,  0.64;  % 收集点2
    3,  20, 30,  1.08,  0.18,  0.04,  0.5;   % 收集点3
    4,  25, 10,  1.55,  0.31,  0.06,  1.18;  % 收集点4
    5,  35, 22,  1.62,  0.27,  0.05,  0.76;  % 收集点5
    6,  18, 5,   1.76,  0.384, 0.096, 0.96;  % 收集点6
    7,  30, 35,  0.77,  0.168, 0.042, 0.42;  % 收集点7
    8,  10, 25,  1.02,  0.238, 0.068, 0.374; % 收集点8
    9,  22, 18,  1.32,  0.176, 0.044, 0.66;  % 收集点9
    10, 38, 15,  1.45,  0.3,   0.075, 0.675; % 收集点10
    11, 5,  8,   1.35,  0.27,  0.108, 0.972; % 收集点11
    12, 15, 32,  1.87,  0.51,  0.068, 0.952; % 收集点12
    13, 28, 5,   2.58,  0.516, 0.129, 1.075; % 收集点13
    14, 30, 12,  1.134, 0.21,  0.063, 0.693; % 收集点14
    15, 10, 10,  0.78,  0.13,  0.065, 0.325; % 收集点15
    16, 20, 20,  0.768, 0.192, 0.08,  0.56;  % 收集点16
    17, 35, 30,  0.72,  0.27,  0.09,  0.72;  % 收集点17
    18, 8,  22,  1.595, 0.348, 0.087, 0.87;  % 收集点18
    19, 25, 25,  1.5,   0.36,  0.09,  1.05;  % 收集点19
    20, 32, 8,   1.08,  0.18,  0.09,  0.45;  % 收集点20
    21, 15, 5,   0.912, 0.19,  0.038, 0.76;  % 收集点21
    22, 28, 20,  0.9,   0.195, 0.075, 0.33;  % 收集点22
    23, 38, 25,  0.99,  0.27,  0.072, 0.468; % 收集点23
    24, 10, 30,  1.44,  0.24,  0.048, 0.672; % 收集点24
    25, 20, 10,  1.74,  0.319, 0.116, 0.725; % 收集点25
    26, 30, 18,  1.17,  0.39,  0.13,  0.91;  % 收集点26
    27, 5,  25,  1.7,   0.34,  0.17,  1.19;  % 收集点27
    28, 18, 30,  2.64,  0.66,  0.044, 1.056; % 收集点28
    29, 35, 10,  0.864, 0.216, 0.072, 0.648; % 收集点29
    30, 22, 35,  0.986, 0.204, 0.085, 0.425; % 收集点30
];

% 候选中转站数据
transfer_station_data = [
    31, 15, 15, 50000, 6,  18, 15, 10, 5,  20;  % 中转站1
    32, 25, 20, 45000, 7,  17, 12, 8,  4,  18;  % 中转站2
    33, 10, 25, 40000, 8,  16, 10, 6,  3,  15;  % 中转站3
    34, 30, 10, 55000, 6,  18, 18, 12, 6,  25;  % 中转站4
    35, 20, 30, 48000, 7,  17, 14, 9,  4,  20;  % 中转站5
];

% 非对称路网约束
asymmetric_constraints = [
    4,  31, 18, -1, -1;  % 收集点4到中转站31，单行道绕行
    31, 4,  15, -1, -1;  % 中转站31到收集点4
    27, 28, 14, -1, -1;  % 收集点27到28，单行道绕行
    28, 27, 18, -1, -1;  % 收集点28到27
    23, 0,  45, 9,  12;  % 收集点23到处理厂，在9:00-12:00禁行需绕行
    0,  23, 40, -1, -1;  % 处理厂到收集点23
    9,  16, 8,  9,  11;  % 收集点9到16，在9:00-11:00禁行需绕行
    16, 9,  10, -1, -1;  % 收集点16到9
];

% 提取基础数据
coords = collection_data(:, 2:3);
demands = collection_data(:, 4:7);
n = size(coords, 1) - 1; % 收集点数量
m = size(transfer_station_data, 1); % 中转站数量

ts_coords = transfer_station_data(:, 2:3);
ts_costs = transfer_station_data(:, 4) / 10;
ts_capacities = transfer_station_data(:, 7:10);

% 车辆参数
Q = [8, 6, 3, 10];      % 容量
C = [2.5, 2.0, 5.0, 1.8]; % 单位成本
alpha = [0.8, 0.6, 1.2, 0.7]; % 距离排放系数
beta = [0.3, 0.2, 0.5, 0.25];  % 载重排放系数

fprintf('收集点数量: %d个\n', n);
fprintf('候选中转站数量: %d个\n', m);
fprintf('非对称约束数量: %d条\n', size(asymmetric_constraints, 1));

%% 2. 构建非对称距离矩阵
fprintf('\n=== 构建非对称距离矩阵 ===\n');

% 合并所有坐标
all_coords = [coords; ts_coords];
total_nodes = size(all_coords, 1);

% 初始化距离矩阵
dist_matrix = zeros(total_nodes, total_nodes);

% 计算基础欧几里得距离
for i = 1:total_nodes
    for j = 1:total_nodes
        if i ~= j
            dist_matrix(i,j) = sqrt((all_coords(i,1) - all_coords(j,1))^2 + ...
                                  (all_coords(i,2) - all_coords(j,2))^2);
        end
    end
end

% 应用非对称约束
fprintf('应用非对称约束:\n');
for k = 1:size(asymmetric_constraints, 1)
    from_node = asymmetric_constraints(k, 1);
    to_node = asymmetric_constraints(k, 2);
    distance = asymmetric_constraints(k, 3);
    
    % 转换节点编号为矩阵索引
    if from_node == 0
        from_idx = 1;
    elseif from_node <= 30
        from_idx = from_node + 1;
    else
        from_idx = from_node - 30 + n + 2;
    end
    
    if to_node == 0
        to_idx = 1;
    elseif to_node <= 30
        to_idx = to_node + 1;
    else
        to_idx = to_node - 30 + n + 2;
    end
    
    if from_idx <= total_nodes && to_idx <= total_nodes
        dist_matrix(from_idx, to_idx) = distance;
        fprintf('  约束 %d->%d: 距离%.1fkm\n', from_node, to_node, distance);
    end
end

%% 3. 第一阶段：设施选址优化（改进算法）
fprintf('\n=== 第一阶段：设施选址优化 ===\n');
tic;

% 计算每个收集点到各中转站的运输成本
transport_cost = zeros(n, m);
for i = 1:n
    for j = 1:m
        % 收集点到中转站的距离
        dist_to_ts = dist_matrix(i+1, n+1+j);
        % 中转站到处理厂的距离
        dist_ts_to_depot = dist_matrix(n+1+j, 1);
        transport_cost(i, j) = (dist_to_ts + dist_ts_to_depot) * mean(C);
    end
end

% 改进的贪心选址算法
selected_stations = [];
unassigned_points = 1:n;
total_facility_cost = 0;
allocation = {}; % 使用cell数组存储分配方案

while ~isempty(unassigned_points) && length(selected_stations) < m
    best_station = -1;
    best_benefit = -inf;
    best_assignment = [];
    
    % 评估每个未选择的中转站
    for j = 1:m
        if ismember(j, selected_stations)
            continue;
        end
        
        % 为当前中转站寻找最优分配
        current_capacity = ts_capacities(j, :);
        current_assignment = [];
        current_benefit = 0;
        temp_capacity = current_capacity;
        
        % 按运输成本排序未分配的收集点
        [sorted_costs, cost_order] = sort(transport_cost(unassigned_points, j));
        
        for k = 1:length(cost_order)
            point_idx = unassigned_points(cost_order(k));
            point_demand = demands(point_idx+1, :);
            
            % 检查容量约束
            if all(point_demand <= temp_capacity)
                current_assignment = [current_assignment, point_idx];
                temp_capacity = temp_capacity - point_demand;
                current_benefit = current_benefit + (50 - sorted_costs(k)); % 基准成本50
            end
        end
        
        % 计算净收益（考虑建设成本）
        net_benefit = current_benefit - ts_costs(j);
        
        if net_benefit > best_benefit && length(current_assignment) > 0
            best_benefit = net_benefit;
            best_station = j;
            best_assignment = current_assignment;
        end
    end
    
    % 如果找到有益的中转站，则选择它
    if best_station > 0
        selected_stations = [selected_stations, best_station];
        allocation{length(selected_stations)} = best_assignment;
        total_facility_cost = total_facility_cost + ts_costs(best_station);
        unassigned_points = setdiff(unassigned_points, best_assignment);
        
        fprintf('选择中转站 %d, 服务收集点 %d 个\n', ...
                best_station + 30, length(best_assignment));
    else
        % 如果没有有益的中转站，强制选择成本最低的
        remaining_stations = setdiff(1:m, selected_stations);
        if ~isempty(remaining_stations)
            [~, min_idx] = min(ts_costs(remaining_stations));
            forced_station = remaining_stations(min_idx);
            
            % 为强制选择的中转站分配剩余收集点
            forced_assignment = unassigned_points(1:min(length(unassigned_points), 5));
            
            selected_stations = [selected_stations, forced_station];
            allocation{length(selected_stations)} = forced_assignment;
            total_facility_cost = total_facility_cost + ts_costs(forced_station);
            unassigned_points = setdiff(unassigned_points, forced_assignment);
            
            fprintf('强制选择中转站 %d, 服务收集点 %d 个\n', ...
                    forced_station + 30, length(forced_assignment));
        end
    end
end

% 处理剩余未分配的收集点
if ~isempty(unassigned_points)
    % 将剩余收集点分配给最近的已选中转站
    for point = unassigned_points
        min_cost = inf;
        best_station_idx = 1;
        
        for s = 1:length(selected_stations)
            cost = transport_cost(point, selected_stations(s));
            if cost < min_cost
                min_cost = cost;
                best_station_idx = s;
            end
        end
        
        allocation{best_station_idx} = [allocation{best_station_idx}, point];
    end
    fprintf('剩余 %d 个收集点已重新分配\n', length(unassigned_points));
end

phase1_time = toc;

fprintf('第一阶段完成:\n');
fprintf('选择中转站数量: %d个\n', length(selected_stations));
fprintf('中转站编号: ');
for i = 1:length(selected_stations)
    fprintf('%d ', selected_stations(i) + 30);
end
fprintf('\n');
fprintf('设施建设成本: %.2f元/年\n', total_facility_cost);

%% 4. 第二阶段：路径优化
fprintf('\n=== 第二阶段：路径优化 ===\n');
tic;

total_transport_cost = 0;
total_emissions = 0;
routing_details = {};

for s = 1:length(selected_stations)
    station_id = selected_stations(s);
    assigned_points = allocation{s};
    
    if isempty(assigned_points)
        continue;
    end
    
    fprintf('优化中转站 %d 的路径 (服务 %d 个收集点)...\n', ...
            station_id + 30, length(assigned_points));
    
    station_cost = 0;
    station_emissions = 0;
    station_routes = {};
    
    % 为每种垃圾类型优化路径
    for k = 1:4
        % 提取该类垃圾的有效收集点
        valid_points = [];
        point_demands = [];
        
        for p = assigned_points
            demand = demands(p+1, k);
            if demand > 0
                valid_points = [valid_points, p];
                point_demands = [point_demands, demand];
            end
        end
        
        if isempty(valid_points)
            continue;
        end
        
        % 简化的车辆路径规划
        current_capacity = Q(k);
        current_load = 0;
        current_route = [];
        routes = {};
        
        % 贪心分配到车辆
        for p = 1:length(valid_points)
            if current_load + point_demands(p) <= current_capacity
                current_route = [current_route, valid_points(p)];
                current_load = current_load + point_demands(p);
            else
                % 完成当前路径
                if ~isempty(current_route)
                    routes{end+1} = current_route;
                end
                % 开始新路径
                current_route = [valid_points(p)];
                current_load = point_demands(p);
            end
        end
        
        % 添加最后一条路径
        if ~isempty(current_route)
            routes{end+1} = current_route;
        end
        
        % 计算每条路径的成本和排放
        for r = 1:length(routes)
            route = routes{r};
            route_demand = sum(demands(route+1, k));
            
            % 简化距离计算：处理厂->收集点->中转站->处理厂
            route_distance = 0;
            prev_node = 1; % 处理厂
            
            for point = route
                route_distance = route_distance + dist_matrix(prev_node, point+1);
                prev_node = point + 1;
            end
            
            % 到中转站
            route_distance = route_distance + dist_matrix(prev_node, n+1+station_id);
            % 中转站回处理厂
            route_distance = route_distance + dist_matrix(n+1+station_id, 1);
            
            % 成本和排放计算
            route_cost = route_distance * C(k);
            route_emission = route_distance * alpha(k) + route_demand * beta(k);
            
            station_cost = station_cost + route_cost;
            station_emissions = station_emissions + route_emission;
        end
        
        station_routes{k} = routes;
    end
    
    routing_details{s} = station_routes;
    total_transport_cost = total_transport_cost + station_cost;
    total_emissions = total_emissions + station_emissions;
    
    fprintf('  中转站 %d: 成本 %.2f元, 碳排放 %.2f kg\n', ...
            station_id + 30, station_cost, station_emissions);
end

phase2_time = toc;
total_time = phase1_time + phase2_time;
total_cost = total_facility_cost + total_transport_cost;

%% 5. 结果输出与分析
fprintf('\n========== 非对称网络求解结果 ==========\n');
fprintf('总求解时间: %.4f秒\n', total_time);
fprintf('第一阶段时间: %.4f秒\n', phase1_time);
fprintf('第二阶段时间: %.4f秒\n', phase2_time);
fprintf('选中的中转站: %d个\n', length(selected_stations));
fprintf('中转站建设成本: %.2f元/年\n', total_facility_cost);
fprintf('运输成本: %.2f元/年\n', total_transport_cost);
fprintf('总成本: %.2f元/年\n', total_cost);
fprintf('总碳排放: %.2f kg/年\n', total_emissions);

% 详细分配信息
fprintf('\n=== 详细分配方案 ===\n');
for s = 1:length(selected_stations)
    station_id = selected_stations(s);
    assigned_points = allocation{s};
    
    fprintf('中转站 %d (坐标: %.1f, %.1f):\n', station_id + 30, ...
            ts_coords(station_id, 1), ts_coords(station_id, 2));
    fprintf('  服务收集点: ');
    for p = assigned_points
        fprintf('%d ', p);
    end
    fprintf('\n');
    
    % 计算该中转站的总需求
    total_demand = zeros(1, 4);
    for p = assigned_points
        total_demand = total_demand + demands(p+1, :);
    end
    fprintf('  总需求: [%.2f, %.2f, %.2f, %.2f] 吨/天\n', total_demand);
    fprintf('  容量限制: [%.0f, %.0f, %.0f, %.0f] 吨/天\n', ts_capacities(station_id, :));
    
    % 容量利用率
    utilization = total_demand ./ ts_capacities(station_id, :) * 100;
    fprintf('  容量利用率: [%.1f%%, %.1f%%, %.1f%%, %.1f%%]\n', utilization);
    fprintf('\n');
end

%% 6. 可视化结果
fprintf('=== 生成可视化图表 ===\n');
figure('Position', [100, 100, 1200, 900]);

% 主图：网络布局
subplot(2, 2, [1, 2]);
hold on;

% 绘制处理厂
plot(coords(1,1), coords(1,2), 'ks', 'MarkerSize', 15, 'MarkerFaceColor', 'black');
text(coords(1,1)-2, coords(1,2)+2, '处理厂', 'FontSize', 12, 'FontWeight', 'bold');

% 绘制收集点
scatter(coords(2:end,1), coords(2:end,2), 60, 'blue', 'filled', 'o');
for i = 2:size(coords, 1)
    text(coords(i,1)+1, coords(i,2), sprintf('%d', i-1), 'FontSize', 8);
end

% 绘制未选中的中转站
unselected = setdiff(1:m, selected_stations);
if ~isempty(unselected)
    scatter(ts_coords(unselected,1), ts_coords(unselected,2), 100, 'red', 's');
end

% 绘制选中的中转站
colors = ['g', 'r', 'b', 'c', 'm'];
for s = 1:length(selected_stations)
    station_id = selected_stations(s);
    coord = ts_coords(station_id, :);
    
    % 绘制中转站
    scatter(coord(1), coord(2), 150, colors(mod(s-1,5)+1), 'filled', 's');
    text(coord(1)+1, coord(2), sprintf('T%d', station_id+30), ...
         'FontSize', 10, 'FontWeight', 'bold');
    
    % 绘制分配连线
    assigned_points = allocation{s};
    for p = assigned_points
        point_coord = coords(p+1, :);
        plot([point_coord(1), coord(1)], [point_coord(2), coord(2)], ...
             ':', 'Color', colors(mod(s-1,5)+1), 'LineWidth', 2);
    end
end

% 绘制非对称约束
for i = 1:size(asymmetric_constraints, 1)
    from_node = asymmetric_constraints(i, 1);
    to_node = asymmetric_constraints(i, 2);
    
    % 获取坐标
    if from_node == 0
        from_coord = coords(1, :);
    elseif from_node <= 30
        from_coord = coords(from_node + 1, :);
    else
        from_coord = ts_coords(from_node - 30, :);
    end
    
    if to_node == 0
        to_coord = coords(1, :);
    elseif to_node <= 30
        to_coord = coords(to_node + 1, :);
    else
        to_coord = ts_coords(to_node - 30, :);
    end
    
    % 绘制约束箭头
    quiver(from_coord(1), from_coord(2), ...
           to_coord(1) - from_coord(1), to_coord(2) - from_coord(2), ...
           0, 'r', 'LineWidth', 2, 'MaxHeadSize', 0.3);
end

title(sprintf('非对称网络布局与优化结果\n总成本: %.0f元/年, 总排放: %.0f kg/年', ...
              total_cost, total_emissions));
xlabel('X坐标 (km)');
ylabel('Y坐标 (km)');
legend('处理厂', '收集点', '未选中转站', '选中中转站', 'Location', 'best');
grid on;
axis equal;
hold off;

% 成本分析
subplot(2, 2, 3);
cost_data = [total_facility_cost, total_transport_cost];
pie(cost_data, {'建设成本', '运输成本'});
title(sprintf('成本构成 (总计%.0f元/年)', total_cost));

% 容量利用率分析
subplot(2, 2, 4);
capacity_util = zeros(length(selected_stations), 4);
for s = 1:length(selected_stations)
    station_id = selected_stations(s);
    assigned_points = allocation{s};
    total_demand = zeros(1, 4);
    for p = assigned_points
        total_demand = total_demand + demands(p+1, :);
    end
    capacity_util(s, :) = total_demand ./ ts_capacities(station_id, :) * 100;
end

bar(capacity_util);
set(gca, 'XTickLabel', arrayfun(@(x) sprintf('T%d', x+30), selected_stations, 'UniformOutput', false));
legend('厨余垃圾', '可回收物', '有害垃圾', '其他垃圾');
title('各中转站容量利用率 (%)');
ylabel('利用率 (%)');
xlabel('中转站');

fprintf('可视化完成！\n');