import numpy as np
import matplotlib.pyplot as plt

# --- 1. 数据输入 ---
# 点 0 为处理厂，1–30 为收集点，31 为中转站
pts = np.array([
    [ 0,   0],    # 0: 处理厂
    [12,   8],
    [ 5,  15],
    [20,  30],
    [25,  10],
    [35,  22],
    [18,   5],
    [30,  35],#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    [10,  25],
    [22,  18],
    [38,  15],
    [ 5,   8],
    [15,  32],
    [28,   5],
    [30,  12],
    [10,  10],
    [20,  20],
    [35,  30],
    [ 8,  22],
    [25,  25],
    [32,   8],
    [15,   5],
    [28,  20],
    [38,  25],
    [10,  30],
    [20,  10],
    [30,  18],
    [ 5,  25],
    [18,  30],
    [35,  10],
    [22,  35],
    [25,  15]     # 31: 中转站
])
w = np.array([
    [0,    0,    0,    0   ],
    [0.72, 0.12, 0.06, 0.30],
    [1.38, 0.23, 0.05, 0.64],
    [1.08, 0.18, 0.04, 0.50],
    [1.55, 0.31, 0.06, 1.18],
    [1.62, 0.27, 0.05, 0.76],
    [1.76, 0.384,0.096,0.96],
    [0.77, 0.168,0.042,0.42],
    [1.02, 0.238,0.068,0.374],
    [1.32, 0.176,0.044,0.66],
    [1.45, 0.30, 0.075,0.675],
    [1.35, 0.27, 0.108,0.972],
    [1.87, 0.51, 0.068,0.952],
    [2.58, 0.516,0.129,1.075],
    [1.134,0.21, 0.063,0.693],
    [0.78, 0.13, 0.065,0.325],
    [0.768,0.192,0.080,0.56],
    [0.72, 0.27, 0.090,0.72],
    [1.595,0.348,0.087,0.87],
    [1.50, 0.36, 0.090,1.05],
    [1.08, 0.18, 0.090,0.45],
    [0.912,0.19, 0.038,0.76],
    [0.90, 0.195,0.075,0.33],
    [0.99, 0.27, 0.072,0.468],
    [1.44, 0.24, 0.048,0.672],
    [1.74, 0.319,0.116,0.725],
    [1.17, 0.39, 0.130,0.91],
    [1.70, 0.34, 0.170,1.19],
    [2.64, 0.66, 0.044,1.056],
    [0.864,0.216,0.072,0.648],
    [0.986,0.204,0.085,0.425],
    [0,    0,    0,    0   ]  # 31: 中转站
])
types = ['厨余','可回收','有害','其他']
colors = plt.cm.tab10(np.arange(4))
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# --- 2. 气泡图：四类垃圾量分布 ---
plt.figure(figsize=(8,6))
plt.scatter(pts[0,0], pts[0,1], c='k', s=150, marker='s', label='厂区')
for k in range(4):
    sz = 50 + 250 * (w[1:31,k] / w[1:31,k].max())
    plt.scatter(pts[1:31,0], pts[1:31,1], s=sz, alpha=0.6, color=colors[k], label=types[k])
plt.title('各收集点四类垃圾产量分布')
plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.legend(loc='upper right')
plt.grid(True)

# --- 3. 柱状图：累计产量对比 ---
plt.figure(figsize=(6,4))
total = w[1:31,:].sum(axis=0)
plt.bar(types, total, color=colors, edgecolor='k')
plt.title('四类垃圾累计产量对比')
plt.ylabel('总产量 (吨)')
plt.grid(axis='y')

# --- 4. 雷达图：车辆参数对比 ---
Qk = np.array([8,6,3,10])
Vk = np.array([20,25,10,18])
Ck = np.array([2.5,2.0,5.0,1.8])
P = np.vstack((Qk, Vk, Ck)).T
P_norm = P / P.max(axis=0)
angles = np.linspace(0, 2*np.pi, 3, endpoint=False)
angles = np.concatenate((angles, [angles[0]]))
labels = ['载重 Q_k','容积 V_k','距离成本 C_k']

plt.figure(figsize=(6,6))
ax = plt.subplot(projection='polar')
for i in range(4):
    vals = np.concatenate((P_norm[i], [P_norm[i,0]]))
    ax.plot(angles, vals, '-o', color=colors[i], label=types[i], linewidth=1.5)
ax.set_xticks(angles[:-1])
ax.set_xticklabels(labels)
ax.set_title('四类型车辆参数（归一化）', va='bottom')
plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1))

# --- 5. 示意图：单行道 & 时间窗 ---
plt.figure(figsize=(8,6))#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
plt.scatter(pts[0,0], pts[0,1], c='k', s=150, marker='s', label='处理厂')
plt.scatter(pts[1:31,0], pts[1:31,1], c='gray', s=60, label='收集点')
for i in range(1,31):
    plt.text(pts[i,0], pts[i,1]+0.5, str(i), fontsize=8)
# 单行道 4<->31
plt.plot([pts[4,0], pts[31,0]], [pts[4,1], pts[31,1]], 'r-', lw=2)
mid4 = (pts[4]+pts[31])/2
plt.text(*mid4, '4→31:18km\n31→4:15km', color='r', fontsize=8)
# 单行道 27<->28
plt.plot([pts[27,0], pts[28,0]], [pts[27,1], pts[28,1]], 'r--', lw=2)
mid274 = (pts[27]+pts[28])/2
plt.text(*mid274, '27→28:14km\n28→27:18km', color='r', fontsize=8)
# 时间窗注释
plt.text(pts[23,0], pts[23,1]+1.5, '23→0禁行9:00-12:00', color='m', fontsize=8)
plt.text(pts[9,0], pts[9,1]-1.5, '9→16禁行9:00-11:00', color='m', fontsize=8)
plt.title('单行道与禁行时段示意')
plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.legend(loc='lower left')
plt.grid(True)

plt.tight_layout()
plt.show()
