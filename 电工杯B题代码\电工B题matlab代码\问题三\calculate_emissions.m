%% 碳排放计算与分析
function [total_emissions, emission_breakdown] = calculate_emissions(...
    routing_solution, selected_stations, alpha, beta, demands)
    
    total_emissions = 0;
    emission_breakdown = zeros(4, 1);
    
    station_keys = keys(routing_solution);
    
    for i = 1:length(station_keys)
        station_id = station_keys{i};
        routes = routing_solution(station_id);
        
        for k = 1:4
            k_routes = routes{k};
            if ~isempty(k_routes)
                for v = 1:length(k_routes)
                    route = k_routes{v};
                    
                    % 计算路径距离
                    route_distance = 0;
                    for r = 1:length(route)-1
                        % 这里需要访问全局距离矩阵，简化处理
                        route_distance = route_distance + 10; % 假设平均段距离
                    end
                    
                    % 计算载重
                    route_weight = 0;
                    for r = 2:length(route)-1 % 排除起点终点
                        if route(r) > 0 && route(r) <= 30
                            route_weight = route_weight + demands(route(r)+1, k);
                        end
                    end
                    
                    % 计算排放
                    distance_emission = route_distance * alpha(k);
                    weight_emission = route_weight * beta(k);
                    route_emission = distance_emission + weight_emission;
                    
                    emission_breakdown(k) = emission_breakdown(k) + route_emission;
                    total_emissions = total_emissions + route_emission;
                end
            end
        end
    end
end
