%% 非对称路径优化核心算法
function [routes, cost, emissions] = optimize_asymmetric_routes(...
    collection_points, station_idx, demands, vehicle_type, ...
    dist_matrix, time_matrix, capacity, unit_cost, alpha_k, beta_k)
    
    routes = {};
    cost = 0;
    emissions = 0;
    
    if isempty(collection_points)
        return;
    end
    
    % 容量约束聚类
    clusters = simple_clustering_asymmetric(collection_points, demands, capacity);
    
    for i = 1:length(clusters)
        cluster_points = clusters{i}{1};
        cluster_demands = clusters{i}{2};
        cluster_demand = sum(cluster_demands);
        
        % 非对称TSP求解
        route = solve_asymmetric_tsp([0, cluster_points, station_idx, 0], dist_matrix);
        
        % 计算成本和排放
        route_distance = calculate_asymmetric_route_distance(route, dist_matrix);
        route_cost = route_distance * unit_cost;
        route_emissions = route_distance * alpha_k + cluster_demand * beta_k;
        
        routes{end+1} = route;
        cost = cost + route_cost;
        emissions = emissions + route_emissions;
    end
end
