%% 非对称性影响分析
function [symmetric_cost, impact] = analyze_asymmetric_impact(...
    selected_stations, allocation_plan, routing_solution, coords, ts_coords, ...
    asymmetric_dist_matrix, actual_cost)
    
    % 构建对称距离矩阵进行对比
    total_nodes = size(coords, 1) + size(ts_coords, 1);
    all_coords = [coords; ts_coords];
    symmetric_dist_matrix = zeros(total_nodes, total_nodes);
    
    for i = 1:total_nodes
        for j = 1:total_nodes
            if i ~= j
                symmetric_dist_matrix(i,j) = sqrt((all_coords(i,1) - all_coords(j,1))^2 + ...
                                                (all_coords(i,2) - all_coords(j,2))^2);
            end
        end
    end
    
    % 计算对称网络下的成本
    symmetric_cost = 0;
    station_keys = keys(routing_solution);
    
    for i = 1:length(station_keys)
        station_id = station_keys{i};
        routes = routing_solution(station_id);
        
        for k = 1:4
            k_routes = routes{k};
            if ~isempty(k_routes)
                for v = 1:length(k_routes)
                    route = k_routes{v};
                    sym_distance = calculate_asymmetric_route_distance(route, symmetric_dist_matrix);
                    symmetric_cost = symmetric_cost + sym_distance * 2.0; % 假设平均成本2元/km
                end
            end
        end
    end
    
    impact = actual_cost - symmetric_cost;
end

