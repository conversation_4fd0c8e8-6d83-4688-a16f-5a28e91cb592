import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle

# 设置图片清晰度
plt.rcParams['figure.dpi'] = 300

# 读取文件
excel_file = pd.ExcelFile('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\附件1.xlsx')

# 获取指定工作表中的数据，设置表头行为1
df = excel_file.parse('附件130个垃圾分类收集点坐标及总垃圾量', header=1)

# 重新设置列名
df.columns = ['Collection Point ID', 'X-coordinate (km)', 'Y-coordinate (km)', 'Garbage Volume (tons)', 'Remark']

# 提取坐标数据
coordinates = df[['X-coordinate (km)', 'Y-coordinate (km)']].values

num_points = len(coordinates)
distance_matrix = np.zeros((num_points, num_points))

# 计算距离矩阵（四舍五入取整）
for i in range(num_points):
    for j in range(num_points):
        # 计算欧氏距离并四舍五入取整
        distance = np.sqrt(((coordinates[i] - coordinates[j]) ** 2).sum())
        distance_matrix[i, j] = round(distance)  # 四舍五入取整

# 将距离矩阵转换为 DataFrame
distance_df = pd.DataFrame(distance_matrix, dtype=int)  # 指定整数类型

# 保存到 Excel 文件
distance_df.to_excel('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\distance_matrix_rounded.xlsx', index=False)

# 绘制收集点分布图
plt.figure(figsize=(10, 8))

# 处理厂（编号0）用红色五角星标记，其他收集点用蓝色圆点标记
for i in range(num_points):
    if df['Collection Point ID'][i] == 0:
        plt.scatter(coordinates[i, 0], coordinates[i, 1], c='red', marker='*', s=200,
                    label='Waste Treatment Plant' if i == 0 else "")
        # 添加处理厂标签
        plt.annotate(f'0', (coordinates[i, 0]+0.2, coordinates[i, 1]+0.2),
                     fontsize=12, fontweight='bold')
    else:
        plt.scatter(coordinates[i, 0], coordinates[i, 1], c='blue', marker='o', s=80,
                    label='Collection Point' if i == 1 else "")
        # 添加收集点编号标签
        plt.annotate(f'{df["Collection Point ID"][i]}',
                     (coordinates[i, 0]+0.2, coordinates[i, 1]+0.2),
                     fontsize=9)

# 添加网格线
plt.grid(True, linestyle='--', alpha=0.7)

# 添加图标题和坐标轴标签
plt.title('Distribution of Garbage Collection Points', fontsize=14)
plt.xlabel('X-coordinate (km)', fontsize=12)
plt.ylabel('Y-coordinate (km)', fontsize=12)

# 设置图例位置
plt.legend(loc='upper right')

# 保存图片
plt.savefig('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\collection_points_distribution_rounded.png')

# 显示图形
plt.show()

# 输出统计信息
print(f"距离矩阵已保存至 'distance_matrix_rounded.xlsx'")
print(f"收集点分布图已保存至 'collection_points_distribution_rounded.png'")
print(f"总收集点数: {num_points-1} (编号1~{num_points-1})")
print(f"处理厂编号: 0")