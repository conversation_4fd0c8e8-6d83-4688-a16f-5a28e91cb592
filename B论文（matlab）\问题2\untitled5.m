%% === 0. 环境初始化 ===
clear; clc; close all;

rng(1);  % 固定随机种子，确保结果稳定

speed      = 40;                  % 平均行驶速度 km/h
TmaxTable  = [12, 12, 12, 12];     % 每类车辆最大行驶时间（小时），即 6:00–18:00，共12小时

%% === 1. 数据读取与初始化 ===
T1 = readtable('附件1.xlsx', "VariableNamingRule", "preserve");
T1.Properties.VariableNames = {'id','x','y','w'};
T1.w(1) = 0;  % 处理厂垃圾量置零
coords = [T1.x, T1.y];
n = height(T1) - 1;  % 客户数量
D = squareform(pdist(coords));

tbl2 = readtable('附件2.xlsx', "HeaderLines", 1, "VariableNamingRule", "preserve");
for i = 1:4
    veh(i).Q = tbl2{i,3};
    veh(i).V = tbl2{i,4};
    veh(i).C = tbl2{i,5};
end

tbl3 = readtable('附件3.xlsx', "HeaderLines", 1, "VariableNamingRule", "preserve");
demW = [zeros(1,4); tbl3{:,2:5}];  % 加入处理厂行为全零
demV = demW;  % 默认体积与重量相同（密度为1）

%% === 2. 遗传算法参数 ===
popSize   = 120;
maxGen    = 250;
pc        = 0.7;
pm        = 0.2;
eliteNum  = 6;
numRuns   = 5;  % 每类垃圾多次运行求最优解

%% === 3. 主程序 ===
labels = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"];
allBest = cell(4, 1);
totalCost = 0;

for k = 1:4
    w_k = demW(:,k)';
    v_k = demV(:,k)';
    Qk = veh(k).Q; Vk = veh(k).V; Ck = veh(k).C; Tmaxk = TmaxTable(k);
    bestDistK = Inf;

    for run = 1:numRuns
        baseRoutes = savingsCW(D, w_k, Qk, Vk, Tmaxk, speed);
        for r = 1:numel(baseRoutes)
            baseRoutes{r} = twoOpt(baseRoutes{r}, D);
        end
        seedChrom = convertRoutesToChromosome(baseRoutes, n);

        pop = zeros(popSize, n);
        pop(1,:) = seedChrom;
        for p = 2:popSize
            pop(p,:) = randperm(n) + 1;
        end

        bestDist = Inf;
        for gen = 1:maxGen
            fit = zeros(popSize, 1);
            for p = 1:popSize
                [dist_p, ~] = decodeChrom(pop(p,:), w_k, v_k, Qk, Vk, Tmaxk, speed, D);
                fit(p) = 1 / max(dist_p, 1e-6);
                if dist_p < bestDist
                    bestDist = dist_p;
                    bestChrom = pop(p,:);
                end
            end
            [~, idx] = sort(fit, 'descend');
            newPop = pop(idx(1:eliteNum), :);
            sel = rouletteWheel(fit, popSize - eliteNum);
            newPop = [newPop; pop(sel,:)];
            for p = eliteNum+1:2:popSize-1
                if rand < pc
                    [newPop(p,:), newPop(p+1,:)] = OX(newPop(p,:), newPop(p+1,:));
                end
            end
            for p = eliteNum+1:popSize
                if rand < pm
                    newPop(p,:) = swapMut(newPop(p,:));
                end
            end
            pop = newPop;
        end

        [~, bestRoutes] = decodeChrom(bestChrom, w_k, v_k, Qk, Vk, Tmaxk, speed, D);
        bestDistRun = sum(cellfun(@(rt) routeDist(rt, D), bestRoutes));
        if bestDistRun < bestDistK
            bestDistK = bestDistRun;
            bestSolK = struct('routes',{bestRoutes}, 'dist',bestDistRun, 'veh',numel(bestRoutes), 'cost',bestDistRun * Ck);
        end
    end

    allBest{k} = bestSolK;
    totalCost = totalCost + bestSolK.cost;
end

%% === 4. 输出结果 ===
for k = 1:4
    info = allBest{k};
    fprintf('\n—— %s 最优运输方案 ——\n', labels(k));
    totalDist_k = 0;
    for r = 1:info.veh
        rt = info.routes{r};
        dist = routeDist(rt, D);
        load = sum(demW(rt(2:end-1), k));
        totalDist_k = totalDist_k + dist;
        fprintf('车辆%02d: 载重 = %.1f 吨  距离 = %.2f km  路线: ', r, load, dist);
        routeStr = sprintf('%d-', rt(1:end-1) - 1);
        fprintf('%s%d\n', routeStr, rt(end) - 1);
    end
    fprintf('车辆数 = %d\n', info.veh);
    fprintf('总行驶距离 = %.2f km\n', totalDist_k);
    fprintf('总运输成本 = %.2f 元\n', info.cost);
end

fprintf('\n>> 全类型合计运输成本 = %.2f 元 <<\n', totalCost);

%% === 5. 路径图绘制（4类垃圾分别绘图） ===
labels = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"];
for k = 1:4
    figure('Name', labels(k), 'NumberTitle', 'off');
    hold on; axis equal; box on;
    title(sprintf('%s 路径图', labels(k)));
    clr = slanCM(189,allBest{k}.veh);
    for r = 1:allBest{k}.veh
        rt = allBest{k}.routes{r};
        plot(coords(rt,1), coords(rt,2), '-o', 'LineWidth', 2, 'Color', clr(r,:), 'MarkerFaceColor', clr(r,:));
    end
    legend(compose('车辆%02d', 1:allBest{k}.veh), 'Location', 'eastoutside');
    text(coords(1,1), coords(1,2)+1, '处理厂 0', 'HorizontalAlignment', 'center');
    for i = 2:size(coords,1)
        text(coords(i,1), coords(i,2)+0.5, string(i-1), 'FontSize', 15);
    end

     xlim([-1 43]);
     ylim([-1 38]);
    % 
    % % 自动生成主刻度和次刻度
     xticks(0:5:40);
     yticks(0:5:35);
    
    set(gcf, 'Position', [100 100 1000 850]);
    set(gca, 'FontSize', 20);

end

%% === 工具函数区 ===
function chrom = convertRoutesToChromosome(routes,n)
    nodes = [];
    for r = 1:numel(routes)
        nodes = [nodes, routes{r}(2:end-1)];
    end
    chrom = nodes;
    assert(numel(nodes) == n);
end

function [totDist, routes] = decodeChrom(chrom, w, v, Q, V, Tmax, speed, D)
    depot = 1; routes = {}; cur = [depot]; loadW = 0; loadV = 0; distCur = 0;
    for idx = 1:length(chrom)
        nd = chrom(idx); dw = w(nd); dv = v(nd); inc = D(cur(end), nd);
        timeCheck = (distCur + inc + D(nd, depot)) / speed;
        exceedLoad = all(loadW + dw > Q);
        exceedVol  = all(loadV + dv > V);
        exceedTime = (~isinf(Tmax)) && all(timeCheck > Tmax);
        if exceedLoad || exceedVol || exceedTime
            cur = [cur, depot]; routes{end+1} = cur;
            cur = [depot, nd]; loadW = dw; loadV = dv; distCur = D(depot, nd);
        else
            cur = [cur, nd]; loadW = loadW + dw; loadV = loadV + dv; distCur = distCur + inc;
        end
    end
    cur = [cur, depot]; routes{end+1} = cur;
    totDist = sum(cellfun(@(rt) routeDist(rt, D), routes));
end

function d = routeDist(route, D)
    d = sum(D(sub2ind(size(D), route(1:end-1), route(2:end))));
end

function routes = savingsCW(D, w, Q, V, Tmax, speed)
    n = size(D,1) - 1; depot = 1;
    routes = arrayfun(@(i){[depot, i+1, depot]}, 1:n);
    S = [];
    for i = 1:n-1
        for j = i+1:n
            saving = D(depot,i+1) + D(depot,j+1) - D(i+1,j+1);
            S = [S; i, j, saving];
        end
    end
    S = sortrows(S, -3);
    for s = 1:size(S,1)
        i = S(s,1)+1; j = S(s,2)+1;
        idx_i = find(cellfun(@(r) r(end-1)==i, routes), 1);
        idx_j = find(cellfun(@(r) r(2)==j, routes), 1);
        if isempty(idx_i) || isempty(idx_j) || idx_i == idx_j
            continue;
        end
        ri = routes{idx_i}; rj = routes{idx_j};
        merged_nodes = [ri(2:end-1), rj(2:end-1)];
        loadW = sum(w(merged_nodes)); loadV = loadW;
        newRoute = [ri(1:end-1), rj(2:end)];
        routeDistVal = sum(D(sub2ind(size(D), newRoute(1:end-1), newRoute(2:end))));
        timeHours = routeDistVal / speed;
        if all(loadW <= Q) && all(loadV <= V) && (isinf(Tmax) || timeHours <= Tmax)
            routes{idx_i} = newRoute;
            routes(idx_j) = [];
        end
    end
end

function rt = twoOpt(rt, D)
    improved = true;
    while improved
        improved = false;
        for i = 2:length(rt)-2
            for k = i+1:length(rt)-1
                delta = D(rt(i-1),rt(k)) + D(rt(i),rt(k+1)) - D(rt(i-1),rt(i)) - D(rt(k),rt(k+1));
                if delta < -1e-6
                    rt(i:k) = rt(k:-1:i); improved = true;
                end
            end
        end
    end
end

function popIdx = rouletteWheel(fit, m)
    cum = cumsum(fit / sum(fit));
    popIdx = arrayfun(@(~) find(rand <= cum, 1), 1:m).';
end

function [c1, c2] = OX(p1, p2)
    n = numel(p1);
    cp = sort(randperm(n, 2));
    seg = p1(cp(1):cp(2));
    c1 = [p2(~ismember(p2, seg))]; c1 = [c1(1:cp(1)-1), seg, c1(cp(1):end)];
    seg = p2(cp(1):cp(2));
    c2 = [p1(~ismember(p1, seg))]; c2 = [c2(1:cp(1)-1), seg, c2(cp(1):end)];
end

function m = swapMut(c)
    idx = randperm(numel(c), 2);
    m = c;
    m(idx) = m(fliplr(idx));
end
