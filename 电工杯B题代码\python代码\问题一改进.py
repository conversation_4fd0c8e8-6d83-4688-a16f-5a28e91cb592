import numpy as np
from scipy.spatial.distance import pdist, squareform
import math
import matplotlib.pyplot as plt

# --- 1. 数据输入 ---
data = np.array([
    [0,    0,   0.0],
    [12,   8,   1.2],
    [5,    15,  2.3],
    [20,   30,  1.8],
    [25,   10,  3.1],
    [35,   22,  2.7],
    [18,   5,   1.5],
    [30,   35,  2.9],#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    [10,   25,  1.1],
    [22,   18,  2.4],
    [38,   15,  3.0],
    [5,    8,   1.7],
    [15,   32,  2.1],
    [28,   5,   3.2],
    [30,   12,  2.6],
    [10,   10,  1.9],
    [20,   20,  2.5],
    [35,   30,  3.3],
    [8,    22,  1.3],
    [25,   25,  2.8],
    [32,   8,   3.4],
    [15,   5,   1.6],
    [28,   20,  2.2],
    [38,   25,  3.5],
    [10,   30,  1.4],
    [20,   10,  2.0],
    [30,   18,  3.6],
    [5,    25,  1.0],
    [18,   30,  2.3],
    [35,   10,  3.7],
    [22,   35,  1.9]
])
coords = data[:, :2]
w      = data[:, 2]
Q      = 5.0            # 车辆最大载重
n      = len(coords)-1  # 客户数量

# --- 2. 距离矩阵 ---#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
d = squareform(pdist(coords))

# --- 3. 生成所有“单趟” trips ---
unserved = list(range(1, n+1))
trips = []
loads = []
dists = []
while unserved:
    curr_load = 0.0
    curr_node = 0
    trip = [0]
    while True:
        # 在未服务点中找最近且可装的
        candidates = [(i, d[curr_node, i]) for i in unserved if curr_load + w[i] <= Q]
        if not candidates:
            break
        # 选距离最小的
        best_i, best_dist = min(candidates, key=lambda x: x[1])
        trip.append(best_i)
        curr_load += w[best_i]
        curr_node = best_i
        unserved.remove(best_i)
    trip.append(0)  # 返回厂区
    # 计算该趟距离
    td = sum(d[trip[k], trip[k+1]] for k in range(len(trip)-1))
    trips.append(trip)
    loads.append(curr_load)
    dists.append(td)

# --- 4. 按日里程上限计算最少车辆数 ---
v_max = 40       # 速度 km/h
T_max = 8        # 最大工作时长 h
D_max = v_max * T_max  # 每车日最大里程
total_trip_dist = sum(dists)
K = math.ceil(total_trip_dist / D_max)
print(f"总行程 {total_trip_dist:.1f} km, 每车日上限 {D_max:.1f} km => 需车辆 {K} 辆")

# --- 5. First-Fit 分配 trips 给 K 辆车 ---
veh_remain = [D_max]*K
vehicles  = [[] for _ in range(K)]
veh_load  = [[] for _ in range(K)]
veh_dist  = [[] for _ in range(K)]

for t, trip in enumerate(trips):
    assigned = False
    for vid in range(K):
        if veh_remain[vid] >= dists[t]:
            vehicles[vid].append(trip)
            veh_load [vid].append(loads[t])
            veh_dist [vid].append(dists[t])
            veh_remain[vid] -= dists[t]
            assigned = True
            break
    if not assigned:
        raise RuntimeError("First-Fit 分配失败：车辆数不足")

# --- 6. 输出结果 ---
for vid in range(K):
    print(f"\n=== 车辆 {vid+1} (剩余 {veh_remain[vid]:.1f} km) ===")
    cum_load = 0.0
    cum_dist = 0.0
    for idx, trip in enumerate(vehicles[vid], 1):
        print(f" 趟 {idx:2d}: 路径 {trip} | 载重 {veh_load[vid][idx-1]:.2f} 吨 | 距离 {veh_dist[vid][idx-1]:.2f} km")
        cum_load += veh_load[vid][idx-1]
        cum_dist += veh_dist[vid][idx-1]
    print(f" 该车累计载重 {cum_load:.2f} 吨, 行程 {cum_dist:.2f} km")
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# --- 7. 可视化 ---
plt.figure(figsize=(8,6))
# 画厂区和客户点
plt.scatter(coords[0,0], coords[0,1], c='k', s=100, marker='s', label='厂区')
plt.scatter(coords[1:,0], coords[1:,1], c='b', s=50, label='客户')
# 画每辆车的所有趟
colors = plt.cm.get_cmap('tab10', K)
for vid in range(K):
    for trip in vehicles[vid]:
        xs, ys = zip(*(coords[trip]))
        plt.plot(xs, ys, '-o', color=colors(vid), linewidth=1.5)
plt.title('基于日里程上限的多趟路径分配')
plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.legend(loc='upper left'); plt.grid(True)#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
plt.show()
