%% 问题二：多车辆协同与载重约束下的优化求解代码
% 多车型多商品车辆路径问题（MC-HFVRP）


clear; clc; close all;

%% 1. 数据输入
% 收集点坐标和各类垃圾产生量数据
fprintf('=== 问题二：多车型垃圾运输路径优化 ===\n');

% 收集点数据 [编号, x坐标, y坐标, 厨余垃圾, 可回收物, 有害垃圾, 其他垃圾]
data = [
    0,  0,  0,   0,     0,     0,     0;      % 处理厂
    1,  12, 8,   0.72,  0.12,  0.06,  0.3;   % 收集点1
    2,  5,  15,  1.38,  0.23,  0.05,  0.64;  % 收集点2
    3,  20, 30,  1.08,  0.18,  0.04,  0.5;   % 收集点3
    4,  25, 10,  1.55,  0.31,  0.06,  1.18;  % 收集点4
    5,  35, 22,  1.62,  0.27,  0.05,  0.76;  % 收集点5
    6,  18, 5,   1.76,  0.384, 0.096, 0.96;  % 收集点6
    7,  30, 35,  0.77,  0.168, 0.042, 0.42;  % 收集点7
    8,  10, 25,  1.02,  0.238, 0.068, 0.374; % 收集点8
    9,  22, 18,  1.32,  0.176, 0.044, 0.66;  % 收集点9
    10, 38, 15,  1.45,  0.3,   0.075, 0.675; % 收集点10
    11, 5,  8,   1.35,  0.27,  0.108, 0.972; % 收集点11
    12, 15, 32,  1.87,  0.51,  0.068, 0.952; % 收集点12
    13, 28, 5,   2.58,  0.516, 0.129, 1.075; % 收集点13
    14, 30, 12,  1.134, 0.21,  0.063, 0.693; % 收集点14
    15, 10, 10,  0.78,  0.13,  0.065, 0.325; % 收集点15
    16, 20, 20,  0.768, 0.192, 0.08,  0.56;  % 收集点16
    17, 35, 30,  0.72,  0.27,  0.09,  0.72;  % 收集点17
    18, 8,  22,  1.595, 0.348, 0.087, 0.87;  % 收集点18
    19, 25, 25,  1.5,   0.36,  0.09,  1.05;  % 收集点19
    20, 32, 8,   1.08,  0.18,  0.09,  0.45;  % 收集点20
    21, 15, 5,   0.912, 0.19,  0.038, 0.76;  % 收集点21
    22, 28, 20,  0.9,   0.195, 0.075, 0.33;  % 收集点22
    23, 38, 25,  0.99,  0.27,  0.072, 0.468; % 收集点23
    24, 10, 30,  1.44,  0.24,  0.048, 0.672; % 收集点24
    25, 20, 10,  1.74,  0.319, 0.116, 0.725; % 收集点25
    26, 30, 18,  1.17,  0.39,  0.13,  0.91;  % 收集点26
    27, 5,  25,  1.7,   0.34,  0.17,  1.19;  % 收集点27
    28, 18, 30,  2.64,  0.66,  0.044, 1.056; % 收集点28
    29, 35, 10,  0.864, 0.216, 0.072, 0.648; % 收集点29
    30, 22, 35,  0.986, 0.204, 0.085, 0.425; % 收集点30
];

% 提取基础数据
coords = data(:, 2:3);  % 坐标矩阵
demands = data(:, 4:7); % 各类垃圾需求矩阵 [厨余, 可回收, 有害, 其他]
n = size(coords, 1) - 1; % 收集点数量

% 车辆参数 [载重限制, 容积限制, 单位成本]
vehicle_params = [
    8,  20, 2.5;  % 厨余垃圾车 (k=1)
    6,  25, 2.0;  % 可回收物车 (k=2)  
    3,  10, 5.0;  % 有害垃圾车 (k=3)
    10, 18, 1.8;  % 其他垃圾车 (k=4)
];

Q = vehicle_params(:, 1);  % 载重限制
V = vehicle_params(:, 2);  % 容积限制  
C = vehicle_params(:, 3);  % 单位距离成本

% 垃圾类型名称
garbage_types = {'厨余垃圾', '可回收物', '有害垃圾', '其他垃圾'};

fprintf('收集点数量: %d个\n', n);
fprintf('车辆类型数: %d种\n', length(garbage_types));

%% 2. 数据预处理与统计分析
fprintf('\n=== 垃圾产生量统计 ===\n');
total_demands = sum(demands(2:end, :), 1); % 各类垃圾总量（排除处理厂）

for k = 1:4
    fprintf('%s: %.3f吨, 理论最少车辆: %d辆\n', ...
            garbage_types{k}, total_demands(k), ceil(total_demands(k)/Q(k)));
end

fprintf('垃圾总量: %.3f吨\n', sum(total_demands));

%% 3. 计算距离矩阵
fprintf('\n=== 计算距离矩阵 ===\n');
dist_matrix = zeros(n+1, n+1);
for i = 1:n+1
    for j = 1:n+1
        if i ~= j
            dist_matrix(i,j) = sqrt((coords(i,1) - coords(j,1))^2 + ...
                                  (coords(i,2) - coords(j,2))^2);
        end
    end
end
fprintf('距离矩阵计算完成\n');

%% 4. 多车型协同求解
fprintf('\n=== 开始多车型协同求解 ===\n');
tic;

% 存储每种车型的解
all_routes = cell(4, 1);
all_costs = zeros(4, 1);
all_distances = zeros(4, 1);
all_vehicle_counts = zeros(4, 1);

% 分别求解每种车型的CVRP子问题
for k = 1:4
    fprintf('\n--- 求解%s运输子问题 ---\n', garbage_types{k});
    
    % 提取第k类垃圾的需求数据
    k_demands = [0; demands(2:end, k)]; % 包含处理厂的需求向量
    
    % 检查是否有需求
    if sum(k_demands(2:end)) == 0
        fprintf('警告: %s总需求为0，跳过求解\n', garbage_types{k});
        continue;
    end
    
    % 求解第k类车型的CVRP
    [k_routes, k_distance, k_vehicle_count] = solve_single_type_cvrp(...
        coords, k_demands, Q(k), dist_matrix, k, garbage_types{k});
    
    % 计算成本
    k_cost = k_distance * C(k);
    
    % 存储结果
    all_routes{k} = k_routes;
    all_costs(k) = k_cost;
    all_distances(k) = k_distance;
    all_vehicle_counts(k) = k_vehicle_count;
    
    fprintf('%s求解完成: %d辆车, %.2f公里, %.2f元\n', ...
            garbage_types{k}, k_vehicle_count, k_distance, k_cost);
end

total_time = toc;

%% 5. 结果汇总与输出
fprintf('\n========== 多车型协同求解结果 ==========\n');
fprintf('总求解时间: %.4f秒\n', total_time);
fprintf('总使用车辆: %d辆\n', sum(all_vehicle_counts));
fprintf('总行驶距离: %.2f公里\n', sum(all_distances));
fprintf('总运输成本: %.2f元\n', sum(all_costs));
fprintf('平均每公里成本: %.2f元/公里\n', sum(all_costs)/sum(all_distances));

% 详细输出每种车型的结果
fprintf('\n=== 各车型详细结果 ===\n');
for k = 1:4
    if all_vehicle_counts(k) > 0
        fprintf('\n%s运输方案:\n', garbage_types{k});
        fprintf('  车辆数量: %d辆\n', all_vehicle_counts(k));
        fprintf('  行驶距离: %.2f公里\n', all_distances(k));
        fprintf('  运输成本: %.2f元 (%.2f元/公里)\n', all_costs(k), C(k));
        fprintf('  平均每辆车距离: %.2f公里\n', all_distances(k)/all_vehicle_counts(k));
        fprintf('  载重利用率: %.1f%%\n', ...
                total_demands(k)/(all_vehicle_counts(k)*Q(k))*100);
        
        % 输出具体路径
        routes = all_routes{k};
        for v = 1:length(routes)
            route = routes{v};
            route_demand = calculate_route_demand(route, demands(:,k));
            route_distance = calculate_route_distance(route, dist_matrix);
            
            fprintf('    车辆%d-%d路径: ', k, v);
            for i = 1:length(route)
                if i == length(route)
                    fprintf('%d', route(i));
                else
                    fprintf('%d→', route(i));
                end
            end
            fprintf(' | 载重:%.2f/%.1f吨 | 距离:%.2f公里\n', ...
                    route_demand, Q(k), route_distance);
        end
    end
end

%% 6. 可视化结果
visualize_multi_vehicle_solution(coords, all_routes, garbage_types, ...
                                all_costs, all_vehicle_counts);

%% 7. 敏感性分析
fprintf('\n=== 敏感性分析 ===\n');
analyze_sensitivity(total_demands, Q, C, all_costs, all_vehicle_counts);



