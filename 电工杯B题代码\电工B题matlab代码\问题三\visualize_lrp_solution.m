%% 可视化LRP解
function visualize_lrp_solution(coords, ts_coords, selected_stations, ...
                               allocation_plan, routing_solution, total_cost, total_emissions)
    
    figure('Name', '中转站选址与路径优化结果', 'Position', [50, 50, 1400, 1000]);
    
    % 主图：显示选址和分配结果
    subplot(2, 2, [1, 2]);
    hold on;
    
    % 绘制处理厂
    plot(coords(1,1), coords(1,2), 'ks', 'MarkerSize', 15, ...
         'MarkerFaceColor', 'black', 'LineWidth', 2);
    text(coords(1,1)+1, coords(1,2)+1, '处理厂', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 绘制收集点
    colors = ['r', 'b', 'g', 'm', 'c'];
    
    for i = 1:length(selected_stations)
        station_id = selected_stations(i);
        
        % 获取分配的收集点
        if isKey(allocation_plan, station_id)
            assigned_points = allocation_plan(station_id);
        else
            fprintf('警告：中转站 %d 无分配信息\n', station_id);
            continue;
        end
        
        color = colors(mod(i-1, length(colors)) + 1);
        
        % 绘制分配给此中转站的收集点
        for j = 1:length(assigned_points)
            point_idx = assigned_points(j);
            
            % 检查收集点索引有效性 (应该是0-29对应收集点1-30)
            if point_idx < 0 || point_idx >= size(coords, 1) - 1
                fprintf('警告：收集点索引 %d 超出范围\n', point_idx);
                continue;
            end
            
            plot(coords(point_idx+1, 1), coords(point_idx+1, 2), 'o', ...
                 'MarkerSize', 8, 'MarkerFaceColor', color, ...
                 'MarkerEdgeColor', 'k', 'LineWidth', 1);
            text(coords(point_idx+1, 1)+0.5, coords(point_idx+1, 2)+0.5, ...
                 num2str(point_idx+1), 'FontSize', 8);  % 显示为1-30
        end
    end
    
    % 绘制选中的中转站
    for i = 1:length(selected_stations)
        station_id = selected_stations(i);
        
        % 调试信息
        fprintf('绘制中转站: station_id = %d\n', station_id);
        
        % station_id 应该是1-5的索引，直接使用
        ts_idx = station_id;
        
        % 检查索引有效性
        if ts_idx < 1 || ts_idx > size(ts_coords, 1)
            fprintf('错误：中转站索引 %d 超出ts_coords范围 [1, %d]\n', ...
                    ts_idx, size(ts_coords, 1));
            continue;
        end
        
        color = colors(mod(i-1, length(colors)) + 1);
        
        plot(ts_coords(ts_idx, 1), ts_coords(ts_idx, 2), '^', ...
             'MarkerSize', 12, 'MarkerFaceColor', color, ...
             'MarkerEdgeColor', 'k', 'LineWidth', 2);
        text(ts_coords(ts_idx, 1)+1, ts_coords(ts_idx, 2)+1, ...
             sprintf('中转站%d', station_id + 30), 'FontSize', 10, 'FontWeight', 'bold');
    end
    
    % 绘制未选中的中转站
    all_stations = 1:size(ts_coords, 1);  % 所有候选中转站索引 1-5
    unselected = setdiff(all_stations, selected_stations);
    for i = 1:length(unselected)
        ts_idx = unselected(i);
        plot(ts_coords(ts_idx, 1), ts_coords(ts_idx, 2), '^', ...
             'MarkerSize', 10, 'MarkerFaceColor', 'w', ...
             'MarkerEdgeColor', 'k', 'LineWidth', 1);
    end
    
    % 绘制分配连线
    for i = 1:length(selected_stations)
        station_id = selected_stations(i);
        
        % 调试信息
        fprintf('处理中转站: station_id = %d\n', station_id);
        
        % 获取分配的收集点
        if isKey(allocation_plan, station_id)
            assigned_points = allocation_plan(station_id);
            fprintf('  分配的收集点: [');
            for p = 1:length(assigned_points)
                fprintf('%d ', assigned_points(p));
            end
            fprintf(']\n');
        else
            fprintf('警告：未找到中转站 %d 的分配信息\n', station_id);
            continue;
        end
        
        ts_idx = station_id;  % 直接使用station_id作为索引
        
        % 检查中转站索引有效性
        if ts_idx < 1 || ts_idx > size(ts_coords, 1)
            fprintf('错误：中转站索引 %d 超出ts_coords范围 [1, %d]\n', ts_idx, size(ts_coords, 1));
            continue;
        end
        
        color = colors(mod(i-1, length(colors)) + 1);
        
        for j = 1:length(assigned_points)
            point_idx = assigned_points(j);
            
            % 详细调试信息
            fprintf('  处理收集点: point_idx = %d\n', point_idx);
            fprintf('  coords大小: [%d, %d]\n', size(coords, 1), size(coords, 2));
            fprintf('  计算的coords索引: %d\n', point_idx+1);
            
            % 检查收集点索引有效性
            % 修改后：
if point_idx < 0 || point_idx > 29  % 明确限制在0-29范围
coords_idx = point_idx + 1;
if coords_idx > size(coords, 1)
                fprintf('错误：收集点索引计算错误 - point_idx=%d, point_idx+1=%d, coords大小=%d\n', ...
                        point_idx, point_idx+1, size(coords, 1));
                continue;
            end
            
            % 检查ts_coords索引
            if ts_idx > size(ts_coords, 1)
                fprintf('错误：ts_coords索引 %d 超出范围 %d\n', ts_idx, size(ts_coords, 1));
                continue;
            end
            
            fprintf('  绘制连线: coords(%d,:) 到 ts_coords(%d,:)\n', point_idx+1, ts_idx);
            
            plot([coords(point_idx+1, 1), ts_coords(ts_idx, 1)], ...
                 [coords(point_idx+1, 2), ts_coords(ts_idx, 2)], ...
                 '--', 'Color', color, 'LineWidth', 1);
        end
        
        % 绘制中转站到处理厂的连线
        plot([ts_coords(ts_idx, 1), coords(1, 1)], ...
             [ts_coords(ts_idx, 2), coords(1, 2)], ...
             '-', 'Color', color, 'LineWidth', 2);
    end
    
    grid on;
    xlabel('X坐标 (公里)', 'FontSize', 12);
    ylabel('Y坐标 (公里)', 'FontSize', 12);
    title('中转站选址与收集点分配结果', 'FontSize', 14);
    axis equal;
    
  
   
    % 添加总标题
    sgtitle(sprintf('中转站选址-路径优化综合结果\n总成本: %.0f元/年, 总排放: %.0f kg/年', ...
                   total_cost, total_emissions), 'FontSize', 16);
    
    % 保存图片
    saveas(gcf, 'LRP_Comprehensive_Solution.png');
    fprintf('\n结果图已保存为 LRP_Comprehensive_Solution.png\n');
end
