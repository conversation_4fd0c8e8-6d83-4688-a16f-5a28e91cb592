% VRP_CLARKE_WRIGHT_VIS.M
% 单一车辆类型（Q=5吨）下的CVRP求解示例
% 使用Clarke-Wright节约算法并配合2-opt局部优化，并绘制可视化结果
% 1. 计算距离矩阵 D，元素 D(i,j)=欧氏距离 = sqrt((x_i-x_j)^2+(y_i-y_j)^2)。
% 2. 初始解：每个收集点 i 单独由一辆车往返，路径为 0->i->0，载重 = w_i。
% 3. 计算节约矩阵 S：
%    S(i,j) = D(0,i) + D(0,j) - D(i,j)，
%    值越大，合并 i 和 j 到同一路径能降低的距离越多。
% 4. 节约值排序：按 S(i,j) 降序处理合并候选对 (i,j)。
% 5. 路径合并规则：
%    - 若 i 在路径 A 末端，j 在路径 B 起始且合并后总载重 <= Q，则合并成 A连接B；
%    - 同理若 j 在 B 末端，i 在 A 起始亦可合并。
% 6. 2-opt 局部优化：对每条路径，枚举交换边段 (i,j)，当逆序交换后路径距离减少时，立即更新，直至无改进。
%    距离计算公式：Dist(route) = sum_{t=1..m-1} D(route(t),route(t+1)).
%% 输入数据
% 收集点坐标和需求
data = [ ...
    0,   0,  0;   % 0: 处理厂
    12,  8,  1.2;
    5,   15, 2.3;
    20,  30, 1.8;
    25,  10, 3.1;
    35,  22, 2.7;
    18,   5, 1.5;
    30,  35, 2.9;
    10,  25, 1.1;
    22,  18, 2.4;
    38,  15, 3.0;
    5,    8, 1.7;
    15,  32, 2.1;
    28,   5, 3.2;
    30,  12, 2.6;
    10,  10, 1.9;
    20,  20, 2.5;
    35,  30, 3.3;
    8,   22, 1.3;
    25,  25, 2.8;
    32,   8, 3.4;
    15,   5, 1.6;
    28,  20, 2.2;
    38,  25, 3.5;
    10,  30, 1.4;
    20,  10, 2.0;
    30,  18, 3.6;
    5,   25, 1.0;
    18,  30, 2.3;
    35,  10, 3.7;
    22,  35, 1.9
];
coords = data(:,1:2);
w = data(:,3);
Q = 5;  % 车辆载重上限
n = size(coords,1) - 1;  % 客户数量
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 计算距离矩阵
D = squareform(pdist(coords));

%% Clarke-Wright 节约算法初始化
routes = cell(n,1);
routeLoad = zeros(n,1);
for i = 1:n
    routes{i} = [0, i, 0];
    routeLoad(i) = w(i+1);
end
S = zeros(n);
for i = 1:n
    for j = i+1:n
        n1 = i+1; n2 = j+1;
        S(i,j) = D(1,n1) + D(1,n2) - D(n1,n2);
        S(j,i) = S(i,j);
    end
end
[idx_i, idx_j] = find(triu(S));
vals = S(sub2ind(size(S), idx_i, idx_j));
[~, order] = sort(vals, 'descend');
pairs = [idx_i(order), idx_j(order)];
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 合并路径
for k = 1:size(pairs,1)
    i = pairs(k,1); j = pairs(k,2);
    [ri, ~] = findRoute(routes, i);
    [rj, ~] = findRoute(routes, j);
    if ri~=rj && routeLoad(ri)+routeLoad(rj) <= Q
        if routes{ri}(end-1)==i && routes{rj}(2)==j
            routes{ri} = [routes{ri}(1:end-1), routes{rj}(2:end)];
            routeLoad(ri) = routeLoad(ri)+routeLoad(rj);
            routes{rj} = [];
        elseif routes{rj}(end-1)==j && routes{ri}(2)==i
            routes{rj} = [routes{rj}(1:end-1), routes{ri}(2:end)];
            routeLoad(rj) = routeLoad(ri)+routeLoad(rj);
            routes{ri} = [];
        end
    end
end
routes = routes(~cellfun('isempty',routes));

%% 2-opt 优化
for k = 1:length(routes)
    routes{k} = twoOptImprove(routes{k}, D);
end

%% 计算统计信息
numVeh = length(routes);
distances = zeros(numVeh,1);%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
loads = zeros(numVeh,1);
for k = 1:numVeh
    distances(k) = pathDistance(routes{k}, D);
    loads(k) = sum(w(routes{k}+1));
end
totalDist = sum(distances);

%% 输出文本结果
fprintf('车辆数: %d, 总行驶距离: %.2f km\n', numVeh, totalDist);
for k = 1:numVeh
    fprintf('车 %d: 路线 %s | 载重 %.2f 吨 | 距离 %.2f km\n', k, mat2str(routes{k}), loads(k), distances(k));
end

%% 可视化 1: 路线地图
figure('Name','路径分布','NumberTitle','off');
grid on; hold on;
% 绘制所有点
scatter(coords(1,1),coords(1,2),100,'ks','filled');
text(coords(1,1),coords(1,2),' 0','FontWeight','bold');
scatter(coords(2:end,1),coords(2:end,2),50,'bo');
for i=2:size(coords,1)
    text(coords(i,1),coords(i,2),[' ', num2str(i-1)]);
end
% 不同车辆不同颜色
colors = lines(numVeh);
for k=1:numVeh
    route = routes{k}+1;
    plot(coords(route,1), coords(route,2), '-o', 'Color', colors(k,:), 'LineWidth',1.5);
end
title('各车辆收集路径示意图'); xlabel('X (km)'); ylabel('Y (km)'); legendEntries = arrayfun(@(k)sprintf('车%d',k),1:numVeh,'UniformOutput',false);
legend(['厂区', '收集点', legendEntries],'Location','bestoutside');

%% 可视化 2: 载重与距离柱状图
figure('Name','载重与距离','NumberTitle','off');
subplot(2,1,1);
bar(loads);
title('每车载重量分布'); ylabel('载重 (吨)'); xlabel('车辆编号');
subplot(2,1,2);
bar(distances);
title('每车行驶距离分布'); ylabel('距离 (km)'); xlabel('车辆编号');

%% 可视化 3: 饼图-距离占比
figure('Name','距离占比','NumberTitle','off');
pie(distances, arrayfun(@(k)sprintf('V%d',k),1:numVeh,'UniformOutput',false));
title('各车行驶距离占比');


