import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
import random
import copy
from itertools import combinations

# --- 1. 基本数据 ---
coords = np.array([
    [ 0,  0],[12,  8],[ 5, 15],[20, 30],[25, 10],[35, 22],[18,  5],[30, 35],
    [10, 25],[22, 18],[38, 15],[ 5,  8],[15, 32],[28,  5],[30, 12],[10, 10],
    [20, 20],[35, 30],[ 8, 22],[25, 25],[32,  8],[15,  5],[28, 20],[38, 25],
    [10, 30],[20, 10],[30, 18],[ 5, 25],[18, 30],[35, 10],[22, 35]
])
w_all = np.array([
    [0,0,0,0],
    [0.72,0.12,0.06,0.30],[1.38,0.23,0.05,0.64],[1.08,0.18,0.04,0.50],
    [1.55,0.31,0.06,1.18],[1.62,0.27,0.05,0.76],[1.76,0.384,0.096,0.96],
    [0.77,0.168,0.042,0.42],[1.02,0.238,0.068,0.374],[1.32,0.176,0.044,0.66],
    [1.45,0.30,0.075,0.675],[1.35,0.27,0.108,0.972],[1.87,0.51,0.068,0.952],
    [2.58,0.516,0.129,1.075],[1.134,0.21,0.063,0.693],[0.78,0.13,0.065,0.325],
    [0.768,0.192,0.080,0.56],[0.72,0.27,0.090,0.72],[1.595,0.348,0.087,0.87],
    [1.50,0.36,0.090,1.05],[1.08,0.18,0.090,0.45],[0.912,0.19,0.038,0.76],
    [0.90,0.195,0.075,0.33],[0.99,0.27,0.072,0.468],[1.44,0.24,0.048,0.672],
    [1.74,0.319,0.116,0.725],[1.17,0.39,0.130,0.91],[1.70,0.34,0.170,1.19],
    [2.64,0.66,0.044,1.056],[0.864,0.216,0.072,0.648],[0.986,0.204,0.085,0.425]
])#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
st_coords = np.array([[12,5],[7,28],[20,8],[30,15],[25,10]])  # 中转站31–35

n, m = 30, 5
Tj = 10000              # 建站成本
Q = [8,6,3,10]          # 载重
C = [2.5,2.0,5.0,1.8]    # 单位距离成本

# --- 2. 距离矩阵（含非对称） ---
all_coords = np.vstack((coords, st_coords))
D = squareform(pdist(all_coords))
# 非对称修改
D[4, 31] = 18; D[31, 4] = 15
D[27, 28] = 14; D[28, 27] = 18
D[23, 0] = 45; D[0, 23] = 40
D[9, 16] = 8; D[16, 9] = 10

# --- 3. 阶段一：P-中心选址 + 分配 ---
best_cost = float('inf')#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
best_y = None
best_assign = None

for p in range(1, m+1):
    for comb in combinations(range(m), p):
        y = set(comb)
        assign = np.zeros((n,4), dtype=int)
        cost_assign = 0.0
        for i in range(n):
            # 不分类型，同站收集
            ds = [D[i+1, 31+s] for s in y]
            s_min = comb[np.argmin(ds)]
            assign[i,:] = s_min
            cost_assign += min(ds)
        total = p*Tj + cost_assign
        if total < best_cost:
            best_cost = total
            best_y = y.copy()
            best_assign = assign.copy()

# --- 4. 阶段二：LNS路径优化 ---
def greedy_cvrp(nodes, demands, Qk):
    routes = []
    unserved = nodes.copy()
    while unserved:
        route, load, curr = [0], 0, 0
        while True:
            cand = [i for i in unserved if load+demands[i]<=Qk]
            if not cand: break
            nxt = min(cand, key=lambda x: D[curr,x])
            route.append(nxt)
            load += demands[nxt]
            curr = nxt
            unserved.remove(nxt)
        route.append(0)
        routes.append(route)
    return routes

# 初始解
base_routes = {}
base_cost = 0.0
for s in best_y:
    for k in range(4):
        pts = [i+1 for i in range(n) if best_assign[i,k]==s]
        if not pts: continue
        demands = {i: w_all[i,k] for i in pts}
        rs = greedy_cvrp(pts, demands, Q[k])
        base_routes[(s,k)] = rs
        for r in rs:
            for u,v in zip(r, r[1:]):
                base_cost += C[k]*D[u,v]

best_routes = copy.deepcopy(base_routes)
best_trans = base_cost

max_iter = 2000
remove_ratio = 0.15

for _ in range(max_iter):
    curr_routes = copy.deepcopy(best_routes)
    assign_p = best_assign.copy()

    # 4.1 破坏
    for k in range(4):
        pts = [i for i in range(n) if assign_p[i,k] in best_y]
        R = max(1, int(remove_ratio*len(pts)))
        rem = random.sample(pts, R)
        for i in rem:
            s0 = assign_p[i,k]
            for r in curr_routes.get((s0,k), []):
                if i+1 in r: r.remove(i+1)
            assign_p[i,k] = -1
    # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    # 4.2 修复
    for k in range(4):
        for s0 in best_y:
            key = (s0,k)
            routes_k = curr_routes.get(key, [])
            un = [i for i in range(n) if best_assign[i,k]==s0 and assign_p[i,k]==-1]
            for i in un:
                best_inc, choice = float('inf'), None
                for idx,r in enumerate(routes_k):
                    for pos in range(1,len(r)):
                        inc = (D[r[pos-1],i+1] + D[i+1,r[pos]] - D[r[pos-1],r[pos]])*C[k]
                        if inc < best_inc:
                            best_inc = inc; choice = (idx,pos)
                idx,pos = choice
                routes_k[idx] = r[:pos]+[i+1]+r[pos:]
                assign_p[i,k] = s0
            curr_routes[key] = routes_k

    # 4.3 评估
    new_cost = 0.0
    for (s,k), rs in curr_routes.items():
        for r in rs:
            for u,v in zip(r, r[1:]):
                new_cost += C[k]*D[u,v]
    # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    if new_cost < best_trans:
        best_trans = new_cost
        best_routes = copy.deepcopy(curr_routes)

final_cost = best_trans + len(best_y)*Tj

# --- 5. 输出 ---
print("选址中转站:", sorted(31+s for s in best_y))
print(f"最小总成本: {final_cost:.2f} 元")
for (s,k), rs in best_routes.items():
    for idx,r in enumerate(rs,1):
        print(f"站{31+s} 类别{k+1} 车{idx}: {r}")

# --- 6. 精美可视化 ---
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# 全局概览
plt.figure(figsize=(8,6))
plt.scatter(all_coords[0,0],all_coords[0,1],c='k',s=120,marker='s',label='厂区')
plt.scatter(all_coords[1:31,0],all_coords[1:31,1],c='gray',label='客户')
sel = [31+s for s in best_y]
plt.scatter(all_coords[sel,0],all_coords[sel,1],c='r',s=120,marker='s',label='中转站')
linestyles = ['-','--',':','-.']
colors = plt.cm.tab10(np.arange(m))
for (s,k), rs in best_routes.items():
    for r in rs:
        xs,ys = zip(*[all_coords[u] for u in r])
        plt.plot(xs, ys, linestyle=linestyles[k], color=colors[s], linewidth=1.5)
plt.legend(loc='center left', bbox_to_anchor=(1,0.5))
plt.title('全局运输路线总览'); plt.xlabel('X (km)'); plt.ylabel('Y (km)'); plt.grid()

# 各站详图
for s in best_y:
    j = 31+s
    plt.figure(figsize=(6,6))#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    plt.scatter(all_coords[j,0],all_coords[j,1],c='r',s=120,marker='s',label=f'中转站{j}')
    for k in range(4):
        pts = [i+1 for i in range(n) if best_assign[i,k]==s]
        plt.scatter(all_coords[pts,0],all_coords[pts,1],c='b',label=f'类型{k+1}')
        for r in best_routes.get((s,k),[]):
            xs,ys = zip(*[all_coords[u] for u in r])
            plt.plot(xs, ys, '-o')
    plt.title(f'中转站{j} 运输详细'); plt.legend(); plt.grid()
    plt.xlabel('X (km)'); plt.ylabel('Y (km)')

plt.tight_layout()
plt.show()
