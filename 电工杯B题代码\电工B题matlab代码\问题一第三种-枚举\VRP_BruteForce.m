%% 辅助函数：暴力枚举法主函数
function [routes, bestDist] = VRP_BruteForce(coords, w, Q)
    n = size(coords,1) - 1;
    % 1) 子集及最短回路预计算
    subsets = {};
    for mask = 1:(2^n-1)
        S = find(bitget(mask,1:n));
        if sum(w(S+1)) > Q, continue; end
        bestD = inf; bestR = [];
        for perm = perms(S)'      % 枚举所有顺序
            route = [0, perm', 0];
            d = routeDist(route, coords);
            if d < bestD, bestD = d; bestR = route; end
        end
        subsets{end+1,1} = S;
        subsets{end,2}   = bestR;
        subsets{end,3}   = bestD;
    end

    % 2) DFS 覆盖搜索
    fullMask = 2^n - 1;
    bestDist = inf; bestSol = [];
    function dfs(chosen, covered, curD)
        if covered == fullMask
            if curD < bestDist
                bestDist = curD; bestSol = chosen;
            end
            return;
        end
        if curD >= bestDist, return; end
        firstUn = find(~bitget(covered,1:n),1);
        for s = 1:size(subsets,1)
            S = subsets{s,1};
            if ~ismember(firstUn,S), continue; end
            maskS = sum(2.^(S-1));
            if bitand(covered,maskS), continue; end
            dfs([chosen, s], covered+maskS, curD+subsets{s,3});
        end
    end
    dfs([],0,0);

    % 3) 构建最优路线集
    routes = cell(length(bestSol),1);
    for k = 1:length(bestSol)
        routes{k} = subsets{bestSol(k),2};
    end
end
