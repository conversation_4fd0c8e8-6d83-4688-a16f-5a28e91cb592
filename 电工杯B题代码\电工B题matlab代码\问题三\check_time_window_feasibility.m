%% 时间窗口约束检查
function feasible = check_time_window_feasibility(...
    route, time_windows, depot_hours, vehicle_speed, service_time)
    
    feasible = true;
    current_time = depot_hours(1); % 从处理厂工作时间开始
    
    for i = 1:length(route)-1
        % 计算到下一个点的行驶时间
        travel_time = 10 / vehicle_speed; % 简化：假设段距离10km
        current_time = current_time + travel_time;
        
        next_node = route(i+1);
        
        % 检查时间窗约束
        if next_node > 30 % 中转站
            ts_idx = next_node - 30;
            if current_time < time_windows(ts_idx, 1) || ...
               current_time > time_windows(ts_idx, 2)
                feasible = false;
                return;
            end
        end
        
        % 添加服务时间
        current_time = current_time + service_time;
        
        % 检查是否超过处理厂工作时间
        if current_time > depot_hours(2)
            feasible = false;
            return;
        end
    end
end
