%% VRP_BRUTEFORCE_FULL.M

clear; clc; close all;

%% 一、输入数据
% 0号为处理厂，其余编号为1..n
data = [ ...
    0,   0,  0;    % 0: 处理厂
    12,  8,  1.2;
    5,   15, 2.3;
    20,  30, 1.8;
    25,  10, 3.1;
    35,  22, 2.7;
    18,   5, 1.5;
    30,  35, 2.9;  
];

coords = data(:,1:2);
w      = data(:,3);
Q      = 5;       % 车辆载重上限
n      = size(coords,1)-1;

%% 二、暴力枚举求解函数调用
[ routes, bestDist ] = VRP_BruteForce(coords, w, Q);

fprintf('\n最优总距离: %.2f km\n', bestDist);

%% 三、可视化结果

% 1) 路线地图
figure('Name','暴力枚举路径分布','NumberTitle','off'); 
hold on; grid on;
% 绘厂区
scatter(coords(1,1), coords(1,2), 120, 'ks', 'filled');
text(coords(1,1), coords(1,2), ' 0', 'FontSize',12, 'FontWeight','bold');
% 绘客户点
scatter(coords(2:end,1), coords(2:end,2), 80, 'bo');
for i = 2:n+1
    text(coords(i,1), coords(i,2), [' ',num2str(i-1)], 'FontSize',10);
end
% 路线
colors = lines(length(routes));
for k = 1:length(routes)
    rt = routes{k}+1;  % MATLAB 索引
    plot(coords(rt,1), coords(rt,2), '-o', 'LineWidth',1.5, 'Color', colors(k,:));
end
title('各车路线分布'); xlabel('X (km)'); ylabel('Y (km)');
legend(['厂区', arrayfun(@(k)sprintf('车%d',k),1:length(routes),'uni',false)], ...
       'Location','bestoutside');

% 2) 每车距离柱状图
distances = cellfun(@(r)routeDist(r,coords), routes);
figure('Name','每车行驶距离','NumberTitle','off');
bar(distances);
xlabel('车辆编号'); ylabel('距离 (km)');
title('每车行驶距离分布');

% 3) 各车距离占比饼图
figure('Name','距离占比','NumberTitle','off');
pie(distances, arrayfun(@(k)sprintf('V%d',k),1:length(routes),'uni',false));
title('各车行驶距离占比');


