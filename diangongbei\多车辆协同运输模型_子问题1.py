"""
解决第二问问题二子问题1：建立以最小化每日总运输成本为目标的多车辆协同运输模型

现实中，垃圾分类运输需区分不同垃圾类型（本题中仅考虑4类垃圾，即厨余垃圾、可回收物、有害垃圾、其他垃圾），
每类垃圾需由专用车辆运输（车辆类型1,2,3,4k = 分别对应上述4类垃圾）。
每类车辆的载重限制kQ 、容积限制kV 、单位距离运输成本kC 不同（参数见附件2），
且每个收集点可能产生多种类型的垃圾（各类型垃圾量, 0i k w ，满足i k i k w w = = ）。
车辆从处理厂出发，完成同类型垃圾收集后返回处理厂，不同类型车辆可独立调度。

1）建立以最小化每日总运输成本为目标的多车辆协同运输模型。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import time
import matplotlib

# 设置中文字体，解决中文显示为白框的问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 解决保存图像时负号'-'显示为方块的问题

# 1. 数据读取与预处理
# 读取收集点数据
points_data = pd.read_excel('B题/附件1.xlsx', skiprows=2)
points_data.columns = ['收集点编号', 'x', 'y', 'w', '备注']

# 读取附件3中的4类垃圾数据
garbage_data = pd.read_excel('B题/附件3.xlsx', skiprows=1)
garbage_data.columns = ['收集点编号', '厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

# 读取附件2中的车辆参数
vehicle_params = pd.read_excel('B题/附件2.xlsx', skiprows=1)
vehicle_params.columns = ['车辆类型k', '垃圾类型', '载重', '容积', '距离成本', '碳排放系数1', '碳排放系数2']

# 提取坐标
points = points_data[['x', 'y']].values
n = len(points)

# 提取各类垃圾重量
garbage_weights = {}
garbage_volumes = {}
# 垃圾类型映射
garbage_types = {1: '厨余垃圾', 2: '可回收物', 3: '有害垃圾', 4: '其他垃圾'}

for k in range(1, 5):  # 4类垃圾
    garbage_weights[k] = garbage_data[garbage_types[k]].values
    # 假设体积与重量有一定比例关系，可根据实际情况调整
    garbage_volumes[k] = garbage_weights[k] * 1.2  # 假设体积是重量的1.2倍

# 提取车辆参数
Q = {}  # 载重限制
V = {}  # 容积限制
C = {}  # 单位距离成本
for k in range(1, 5):
    Q[k] = vehicle_params.loc[k-1, '载重']
    V[k] = vehicle_params.loc[k-1, '容积']
    C[k] = vehicle_params.loc[k-1, '距离成本']

# 垃圾处理厂坐标
depot = np.array([0, 0])

# 计算距离矩阵
all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 2. 数学模型
"""
多车辆协同运输模型

决策变量:
x_{i,j,k,v} = 1 表示车辆v(类型k)从点i到点j行驶，否则为0
y_{i,k,v} = 1 表示点i由车辆v(类型k)服务，否则为0

目标函数:
min Z = ∑_{k=1}^4 ∑_{v=1}^{V_k} ∑_{i=0}^n ∑_{j=0,j≠i}^n C_k * d_{i,j} * x_{i,j,k,v}

约束条件:
1. 每个收集点的每类垃圾只能由一辆对应类型的车辆服务:
   ∑_{v=1}^{V_k} y_{i,k,v} = 1, ∀i∈{1,2,...,n}, ∀k∈{1,2,3,4} 且 w_{i,k} > 0

2. 车辆载重约束:
   ∑_{i=1}^n w_{i,k} * y_{i,k,v} ≤ Q_k, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

3. 车辆容积约束:
   ∑_{i=1}^n v_{i,k} * y_{i,k,v} ≤ V_k, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

4. 流量守恒约束:
   ∑_{j=0,j≠i}^n x_{i,j,k,v} = ∑_{j=0,j≠i}^n x_{j,i,k,v} = y_{i,k,v},
   ∀i∈{1,2,...,n}, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

5. 子回路消除约束:
   ∑_{i,j∈S} x_{i,j,k,v} ≤ |S| - 1, ∀S⊂{1,2,...,n}, |S|≥2, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

6. 车辆类型约束:
   每类垃圾只能由对应类型的车辆运输
"""

# 3. 节约算法求解每类垃圾的路径
def savings_algorithm(garbage_type):
    """使用节约算法求解车辆路径规划问题"""
    weights = garbage_weights[garbage_type]
    volumes = garbage_volumes[garbage_type]
    max_weight = Q[garbage_type]
    max_volume = V[garbage_type]

    # 初始化：每个点一个单独路径
    valid_points = [i+1 for i in range(n) if weights[i] > 0]  # 只考虑有该类垃圾的点
    routes = [[i] for i in valid_points]
    route_weights = [weights[i-1] for i in valid_points]
    route_volumes = [volumes[i-1] for i in valid_points]

    # 计算节约值
    savings = []
    for i in valid_points:
        for j in valid_points:
            if i != j:
                s = dist_matrix[i, 0] + dist_matrix[j, 0] - dist_matrix[i, j]
                savings.append((i, j, s))

    # 按节约值降序排序
    savings.sort(key=lambda x: x[2], reverse=True)

    # 合并路径
    def find_route(routes, node):
        for idx, route in enumerate(routes):
            if node in route:
                return idx
        return None

    for i, j, _ in savings:
        ri = find_route(routes, i)
        rj = find_route(routes, j)

        if ri is not None and rj is not None and ri != rj:
            route_i = routes[ri]
            route_j = routes[rj]

            # 检查是否可以合并（端点相连且不超过载重和容积限制）
            merged_weight = route_weights[ri] + route_weights[rj]
            merged_volume = route_volumes[ri] + route_volumes[rj]

            if merged_weight <= max_weight and merged_volume <= max_volume:
                # 检查端点连接情况
                if route_i[-1] == i and route_j[0] == j:
                    routes[ri] = route_i + route_j
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[0] == i and route_j[-1] == j:
                    routes[ri] = route_j + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[0] == i and route_j[0] == j:
                    routes[ri] = route_j[::-1] + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[-1] == i and route_j[-1] == j:
                    routes[ri] = route_i + route_j[::-1]
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]

    return routes, route_weights, route_volumes

# 4. 计算路径成本
def calculate_route_cost(route, garbage_type):
    """计算路径成本"""
    cost = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        cost += dist_matrix[prev, node] * C[garbage_type]
        prev = node
    # 返回垃圾处理厂
    cost += dist_matrix[prev, 0] * C[garbage_type]
    return cost

# 5. 计算路径距离
def calculate_route_distance(route):
    """计算路径总距离"""
    distance = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        distance += dist_matrix[prev, node]
        prev = node
    # 返回垃圾处理厂
    distance += dist_matrix[prev, 0]
    return distance

# 6. 可视化路径
def visualize_routes(all_routes, title, total_cost, total_distance, total_vehicles):
    """可视化路径规划结果"""
    plt.figure(figsize=(15, 12))

    # 设置背景色和网格
    plt.gca().set_facecolor('#f8f9fa')
    plt.grid(color='white', linestyle='-', linewidth=1, alpha=0.7)

    # 绘制收集点
    plt.scatter(points[:, 0], points[:, 1], c='#3498db', s=100, edgecolor='white', linewidth=1.5, label='收集点')

    # 绘制垃圾处理厂
    plt.scatter([0], [0], c='#e74c3c', s=300, marker='*', edgecolor='white', linewidth=1.5, label='垃圾处理厂')

    # 为每个收集点添加编号
    for i in range(n):
        plt.text(points[i, 0]+0.2, points[i, 1]+0.2, f'{i+1}', fontsize=10, color='black')

    # 垃圾类型和对应颜色
    colors = ['#2ecc71', '#9b59b6', '#f1c40f', '#1abc9c']
    markers = ['o', 's', '^', 'd']

    # 为每种垃圾类型创建一个图例条目
    for k in range(1, 5):
        plt.scatter([], [], c=colors[k-1], marker=markers[k-1], s=100,
                   label=f'{garbage_types[k]}路径')

    # 绘制路径
    for k in range(1, 5):
        routes = all_routes[k]
        for i, route in enumerate(routes):
            route_with_depot = [0] + route + [0]  # 添加处理厂作为起点和终点
            route_x = [all_points[j, 0] for j in route_with_depot]
            route_y = [all_points[j, 1] for j in route_with_depot]

            # 绘制路径线条
            plt.plot(route_x, route_y, c=colors[k-1], linewidth=2.5, alpha=0.8)

            # 添加路径箭头
            for j in range(len(route_with_depot)-1):
                dx = route_x[j+1] - route_x[j]
                dy = route_y[j+1] - route_y[j]
                plt.arrow(route_x[j], route_y[j], dx*0.9, dy*0.9,
                          head_width=0.3, head_length=0.5, fc=colors[k-1], ec=colors[k-1], alpha=0.8)

            # 在路径中间位置添加路径编号
            mid_idx = len(route) // 2
            if len(route) > 0:
                mid_point = route[mid_idx]
                plt.text(all_points[mid_point, 0], all_points[mid_point, 1]+0.5,
                         f'路径{k}-{i+1}', fontsize=12, color=colors[k-1],
                         bbox=dict(facecolor='white', alpha=0.7, edgecolor=colors[k-1]))

    # 设置标题和轴标签
    plt.title(title, fontsize=18, pad=20)
    plt.xlabel('X坐标', fontsize=14)
    plt.ylabel('Y坐标', fontsize=14)

    # 添加总成本、总距离和车辆数量信息
    info_text = f"总运输成本: {total_cost:.2f}\n总距离: {total_distance:.2f}\n总车辆数: {total_vehicles}"
    plt.figtext(0.5, 0.01, info_text, ha="center", fontsize=14,
               bbox=dict(facecolor='#f8f9fa', edgecolor='gray', boxstyle='round,pad=0.5'))

    # 添加图例
    plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=3, fontsize=12,
               frameon=True, facecolor='white', edgecolor='gray')

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(f"{title}.png", dpi=300, bbox_inches='tight')
    plt.show()

# 7. 主函数
if __name__ == "__main__":
    print("解决第二问问题二子问题1：建立以最小化每日总运输成本为目标的多车辆协同运输模型")
    start_time = time.time()

    # 求解所有垃圾类型的路径
    all_routes = {}
    all_costs = {}
    all_distances = {}
    total_cost = 0
    total_distance = 0
    total_vehicles = 0

    for k in range(1, 5):
        print(f"\n处理第{k}类垃圾 ({garbage_types[k]})...")
        routes, weights, volumes = savings_algorithm(k)

        # 计算该类垃圾的总成本和总距离
        type_cost = 0
        type_distance = 0

        for route in routes:
            # 计算成本
            route_cost = calculate_route_cost(route, k)
            type_cost += route_cost

            # 计算距离
            route_distance = calculate_route_distance(route)
            type_distance += route_distance

            # 打印每条路径的详细信息
            route_str = ' -> '.join(str(r) for r in route)
            print(f"路径: 0 -> {route_str} -> 0")
            print(f"  距离: {route_distance:.2f}, 成本: {route_cost:.2f}")
            print(f"  载重: {weights[routes.index(route)]:.2f}/{Q[k]}, 容积: {volumes[routes.index(route)]:.2f}/{V[k]}")

        all_routes[k] = routes
        all_costs[k] = type_cost
        all_distances[k] = type_distance
        total_cost += type_cost
        total_distance += type_distance
        total_vehicles += len(routes)

        print(f"第{k}类垃圾共需{len(routes)}辆车")
        print(f"  总距离: {type_distance:.2f}")
        print(f"  总成本: {type_cost:.2f}")

    print(f"\n总运输成本: {total_cost:.2f}")
    print(f"总距离: {total_distance:.2f}")
    print(f"总车辆数: {total_vehicles}")
    print(f"求解时间: {time.time() - start_time:.2f}秒")

    # 可视化路径
    visualize_routes(all_routes, "最小化每日总运输成本的多车辆协同路径",
                    total_cost, total_distance, total_vehicles)

    # 输出详细的路径规划表格
    print("\n详细路径规划表格:")
    print("垃圾类型 | 路径编号 | 路径 | 距离 | 成本 | 载重 | 容积")
    print("-" * 80)

    for k in range(1, 5):
        routes = all_routes[k]
        weights = [sum(garbage_weights[k][node-1] for node in route) for route in routes]
        volumes = [sum(garbage_volumes[k][node-1] for node in route) for route in routes]

        for i, route in enumerate(routes):
            route_str = '0 -> ' + ' -> '.join(str(r) for r in route) + ' -> 0'
            distance = calculate_route_distance(route)
            cost = calculate_route_cost(route, k)
            weight = weights[i]
            volume = volumes[i]

            print(f"{garbage_types[k]} | 路径{k}-{i+1} | {route_str} | {distance:.2f} | {cost:.2f} | {weight:.2f}/{Q[k]} | {volume:.2f}/{V[k]}")
