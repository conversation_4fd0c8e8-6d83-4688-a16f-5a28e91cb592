%% 非对称路径优化
function [routing_solution, total_cost, total_emissions] = solve_asymmetric_routing(...
    selected_stations, allocation_plan, coords, ts_coords, demands, ...
    dist_matrix, time_matrix, Q, C, alpha, beta, ts_time_windows, ...
    depot_hours, vehicle_speed)
    
    routing_solution = containers.Map('KeyType', 'int32', 'ValueType', 'any');
    total_cost = 0;
    total_emissions = 0;
    
    for i = 1:length(selected_stations)
        station_id = selected_stations(i);
        
        if isKey(allocation_plan, station_id)
            assigned_points = allocation_plan(station_id);
        else
            continue;
        end
        
        fprintf('  优化中转站 %d 的非对称路径 (服务 %d 个收集点)...\n', ...
                station_id + 31, length(assigned_points));
        
        [station_routes, station_cost, station_emissions] = ...
            solve_asymmetric_station_routing(station_id, assigned_points, ...
            coords, ts_coords, demands, dist_matrix, time_matrix, ...
            Q, C, alpha, beta, vehicle_speed);
        
        routing_solution(station_id) = station_routes;
        total_cost = total_cost + station_cost;
        total_emissions = total_emissions + station_emissions;
        
        fprintf('    中转站 %d: 成本 %.2f元, 碳排放 %.2f kg\n', ...
                station_id + 31, station_cost, station_emissions);
    end
end
