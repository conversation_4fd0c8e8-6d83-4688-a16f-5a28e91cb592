import pandas as pd
import numpy as np

# 读取数据，路径请根据实际修改
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
data = pd.read_excel('B题\附件1.xlsx', skiprows=2, names=col_names)

# 获取坐标
points = data[['收集点编号', 'x', 'y']].set_index('收集点编号').to_dict(orient='index')

# 打印所有收集点的坐标
print("收集点坐标:", points)

# 计算两点之间的距离
def calculate_distance(point1, point2):
    return np.sqrt((point1['x'] - point2['x']) ** 2 + (point1['y'] - point2['y']) ** 2)

# 计算总距离
def calculate_total_distance(routes):
    total_distance = 0
    depot = {'x': 0, 'y': 0}  # 垃圾处理厂坐标
    for route in routes:
        prev_point = depot
        for point_id in route:
            current_point = points[point_id]
            total_distance += calculate_distance(prev_point, current_point)
            prev_point = current_point
        # 返回垃圾处理厂
        total_distance += calculate_distance(prev_point, depot)
    return total_distance

# 最佳路径
best_routes = [[10, 14], [13, 15, 21], [30, 4], [5], [26, 9], [29, 28], [6, 22], 
               [25, 18, 2], [19, 20], [16, 7], [24, 27], [23], [17, 11], [3, 12], [8], [1]]

# 计算并输出总距离
try:
    total_distance = calculate_total_distance(best_routes)
    print(f"最佳路径的总距离: {total_distance:.2f} km")
except KeyError as e:
    print(e)