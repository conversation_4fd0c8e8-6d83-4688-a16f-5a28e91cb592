# 完整 Clark & Wright Savings + 2-opt 路径优化代码，用于问题一调度

import pandas as pd
import numpy as np
import random

# ---------- Step 1: 数据读取 ----------
file_path = "附件1.xlsx"
xls = pd.ExcelFile(file_path)
df = xls.parse('附件130个垃圾分类收集点坐标及总垃圾量')

df_clean = df.iloc[2:, :4]
df_clean.columns = ['ID', 'X', 'Y', 'Weight']
df_clean = df_clean.reset_index(drop=True)
df_clean['ID'] = df_clean['ID'].astype(int)
df_clean['X'] = df_clean['X'].astype(float)
df_clean['Y'] = df_clean['Y'].astype(float)
df_clean['Weight'] = df_clean['Weight'].astype(float)

depot = pd.DataFrame({'ID': [0], 'X': [0.0], 'Y': [0.0], 'Weight': [0.0]})
df_full = pd.concat([depot, df_clean], ignore_index=True)

coords = df_full[['X', 'Y']].values
dist_matrix = np.linalg.norm(coords[:, None, :] - coords[None, :, :], axis=2)
demands = df_full['Weight'].values
Q = 5.0

# ---------- 路径距离与2-opt ----------
def route_distance(route, dist_matrix):
    return sum(dist_matrix[route[i]][route[i+1]] for i in range(len(route)-1))

def two_opt(route, dist_matrix):
    best = route
    improved = True
    while improved:
        improved = False
        for i in range(1, len(best) - 2):
            for j in range(i + 1, len(best) - 1):
                if j - i == 1:
                    continue
                new_route = best[:i] + best[i:j][::-1] + best[j:]
                if route_distance(new_route, dist_matrix) < route_distance(best, dist_matrix):
                    best = new_route
                    improved = True
        route = best
    return best

# ---------- Clark & Wright Savings 算法 ----------
node_count = len(df_full)
depot = 0
savings_list = []

for i in range(1, node_count):
    for j in range(i + 1, node_count):
        s_ij = dist_matrix[0][i] + dist_matrix[0][j] - dist_matrix[i][j]
        savings_list.append((s_ij, i, j))

savings_list.sort(reverse=True)

routes = {i: [0, i, 0] for i in range(1, node_count)}
route_loads = {i: demands[i] for i in range(1, node_count)}
point_route = {i: i for i in range(1, node_count)}

for s_ij, i, j in savings_list:
    ri = point_route[i]
    rj = point_route[j]
    if ri == rj:
        continue

    route_i = routes[ri]
    route_j = routes[rj]
    load_i = route_loads[ri]
    load_j = route_loads[rj]

    if load_i + load_j > Q:
        continue

    if route_i[-2] == i and route_j[1] == j:
        new_route = route_i[:-1] + route_j[1:]
    elif route_i[1] == i and route_j[-2] == j:
        new_route = list(reversed(route_i[:-1])) + list(reversed(route_j[1:]))
    elif route_i[1] == i and route_j[1] == j:
        new_route = list(reversed(route_i[:-1])) + route_j[1:]
    elif route_i[-2] == i and route_j[-2] == j:
        new_route = route_i[:-1] + list(reversed(route_j[1:]))
    else:
        continue

    new_id = min(ri, rj)
    routes[new_id] = new_route + [0]
    route_loads[new_id] = load_i + load_j

    for p in new_route:
        if p != 0:
            point_route[p] = new_id

    del routes[max(ri, rj)]
    del route_loads[max(ri, rj)]

# ---------- 2-opt 优化每条路径 ----------
final_routes = []
total_distance = 0
final_data = []

for idx, route in enumerate(routes.values()):
    if route[-1] == 0:
        route = route[:-1]
    optimized = two_opt(route + [0], dist_matrix)
    dist = route_distance(optimized, dist_matrix)
    total_distance += dist
    points = [i for i in optimized if i != 0]
    total_weight = sum(demands[i] for i in points)

    final_data.append({
        "运输车编号": f"Vehicle-{idx+1}",
        "运输路径": " -> ".join(map(str, optimized)),
        "收集点任务": ", ".join(map(str, points)),
        "运输垃圾总量（吨）": round(total_weight, 2),
        "运输距离（公里）": round(dist, 2)
    })

final_df = pd.DataFrame(final_data)

total_distance
# final_df
