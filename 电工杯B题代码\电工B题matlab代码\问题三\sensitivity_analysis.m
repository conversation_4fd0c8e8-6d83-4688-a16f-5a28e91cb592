%% 敏感性分析
function sensitivity_analysis(selected_stations, total_cost, total_emissions, ts_costs)
    
    fprintf('中转站建设成本敏感性:\n');
    
    % 建设成本变化对选址的影响
    cost_multipliers = [0.5, 0.8, 1.0, 1.2, 1.5];
    
    for i = 1:length(cost_multipliers)
        multiplier = cost_multipliers(i);
        adjusted_costs = ts_costs * multiplier;
        
        % 重新评估选址（简化分析）
        if multiplier < 0.8
            potential_stations = length(selected_stations) + 1;
        elseif multiplier > 1.2
            potential_stations = max(1, length(selected_stations) - 1);
        else
            potential_stations = length(selected_stations);
        end
        
        estimated_cost = sum(adjusted_costs(selected_stations - 30)) + ...
                        total_cost - sum(ts_costs(selected_stations - 30));
        
        fprintf('  成本系数 %.1f: 预计选择 %d 个中转站, 总成本 %.0f 元\n', ...
                multiplier, potential_stations, estimated_cost);
    end
    
    fprintf('\n碳排放约束敏感性:\n');
    emission_limits = [total_emissions * 0.8, total_emissions * 0.9, ...
                      total_emissions, total_emissions * 1.1];
    
    for i = 1:length(emission_limits)
        limit = emission_limits(i);
        if limit < total_emissions
            fprintf('  排放限制 %.0f kg: 需要优化路径或增加中转站\n', limit);
        else
            fprintf('  排放限制 %.0f kg: 当前方案可行\n', limit);
        end
    end
    
    fprintf('\n时间窗口敏感性:\n');
    fprintf('  时间窗口缩短会影响:\n');
    fprintf('    - 中转站的可达性和服务效率\n');
    fprintf('    - 车辆调度的灵活性\n');
    fprintf('    - 可能需要增加车辆数量\n');
    
    fprintf('  时间窗口延长的好处:\n');
    fprintf('    - 提高车辆利用率\n');
    fprintf('    - 减少车辆数量需求\n');
    fprintf('    - 降低总体运输成本\n');
end
