import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
from itertools import combinations

# --- 1. 输入数据 ---
coords = np.array([
    [ 0,  0],[12,  8],[ 5, 15],[20, 30],[25, 10],[35, 22],[18,  5],[30, 35],
    [10, 25],[22, 18],[38, 15],[ 5,  8],[15, 32],[28,  5],[30, 12],[10, 10],
    [20, 20],[35, 30],[ 8, 22],[25, 25],[32,  8],[15,  5],[28, 20],[38, 25],
    [10, 30],[20, 10],[30, 18],[ 5, 25],[18, 30],[35, 10],[22, 35]
])
# 0=厂区, 1-30=收集点#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
w_all = np.array([
    [0,0,0,0],
    [0.72,0.12,0.06,0.30],[1.38,0.23,0.05,0.64],[1.08,0.18,0.04,0.50],
    [1.55,0.31,0.06,1.18],[1.62,0.27,0.05,0.76],[1.76,0.384,0.096,0.96],
    [0.77,0.168,0.042,0.42],[1.02,0.238,0.068,0.374],[1.32,0.176,0.044,0.66],
    [1.45,0.30,0.075,0.675],[1.35,0.27,0.108,0.972],[1.87,0.51,0.068,0.952],
    [2.58,0.516,0.129,1.075],[1.134,0.21,0.063,0.693],[0.78,0.13,0.065,0.325],
    [0.768,0.192,0.080,0.56],[0.72,0.27,0.090,0.72],[1.595,0.348,0.087,0.87],
    [1.50,0.36,0.090,1.05],[1.08,0.18,0.090,0.45],[0.912,0.19,0.038,0.76],
    [0.90,0.195,0.075,0.33],[0.99,0.27,0.072,0.468],[1.44,0.24,0.048,0.672],
    [1.74,0.319,0.116,0.725],[1.17,0.39,0.130,0.91],[1.70,0.34,0.170,1.19],
    [2.64,0.66,0.044,1.056],[0.864,0.216,0.072,0.648],[0.986,0.204,0.085,0.425]
])
st_coords = np.array([[12,5],[7,28],[20,8],[30,15],[25,10]])  # stations 31–35

n, m = 30, 5
Tj = 10000
Q = [8,6,3,10]
C = [2.5,2.0,5.0,1.8]

# --- 2. 距离矩阵 ---
all_coords = np.vstack((coords, st_coords))
D = squareform(pdist(all_coords))
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
# --- 3. P-中心选址 + 分配 ---
best_cost = np.inf
best_y = None
best_assign = None

for p in range(1, m+1):
    for comb in combinations(range(m), p):
        y = set(comb)
        # 分配：每点到最近启用站
        assign = np.zeros((n,4), dtype=int)
        assign_cost = 0.0
        for i in range(n):
            dists = [D[i+1, 31+s] for s in y]
            s_min = comb[np.argmin(dists)]
            assign[i,:] = s_min
            assign_cost += min(dists)
        total = p*Tj + assign_cost
        if total < best_cost:
            best_cost = total#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
            best_y = y.copy()
            best_assign = assign.copy()

# --- 4. 路径优化：最近邻 CVRP ---
def greedy_cvrp(point_indices, demands, Qk):
    unserved = point_indices.copy()
    routes = []
    while unserved:
        route, load, curr = [0], 0, 0
        while True:
            cand = [i for i in unserved if load+demands[i]>Qk]
            cand = [i for i in unserved if load+demands[i]<=Qk]
            if not cand: break
            nxt = min(cand, key=lambda x: D[curr, x])
            route.append(nxt)
            load += demands[nxt]
            curr = nxt
            unserved.remove(nxt)
        route.append(0)
        routes.append(route)
    return routes

best_routes = {}
trans_cost = 0.0

for s in best_y:
    j = 31 + s
    for k in range(4):
        pts = [i+1 for i in range(n) if best_assign[i,k]==s]
        if not pts: continue
        demands = {idx: w_all[idx,k] for idx in pts}#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        routes = greedy_cvrp(pts, demands, Q[k])
        best_routes[(s,k)] = routes
        for r in routes:
            for u,v in zip(r, r[1:]):
                trans_cost += C[k]*D[u,v]

best_total_cost = trans_cost + len(best_y)*Tj

# --- 5. 输出 ---
print("选址中转站:", sorted(31+s for s in best_y))
print(f"最小总成本: {best_total_cost:.2f} 元")
for (s,k), routes in best_routes.items():
    for idx, r in enumerate(routes,1):
        print(f"站{31+s} 类别{k+1} 车{idx}: {r}")

# --- 6. 可视化 ---
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# 全局概览
plt.figure(figsize=(8,6))
plt.scatter(all_coords[0,0], all_coords[0,1], c='k', s=120, marker='s', label='厂区')
plt.scatter(all_coords[1:31,0], all_coords[1:31,1], c='gray', label='客户')
sel_st = [31+s for s in best_y]
plt.scatter(all_coords[sel_st,0], all_coords[sel_st,1], c='r', s=120, marker='s', label='中转站')
linestyles = ['-','--',':','-.']
colors = plt.cm.tab10(np.arange(m))
for (s,k), routes in best_routes.items():
    for r in routes:
        xs, ys = zip(*[all_coords[u] for u in r])
        plt.plot(xs, ys, linestyle=linestyles[k], color=colors[s], linewidth=1.5)#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
plt.legend(loc='center left', bbox_to_anchor=(1,0.5))
plt.title('全局运输路线概览'); plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.grid(True)

# 各站详图
for s in best_y:
    j = 31 + s
    plt.figure(figsize=(6,6))
    plt.scatter(all_coords[j,0], all_coords[j,1], c='r', s=120, marker='s', label=f'中转站{j}')
    for k in range(4):
        pts = [i+1 for i in range(n) if best_assign[i,k]==s]
        plt.scatter(all_coords[pts,0], all_coords[pts,1], c='b', label=f'类型{k+1}')
        for r in best_routes.get((s,k), []):
            xs, ys = zip(*[all_coords[u] for u in r])
            plt.plot(xs, ys, '-o')
    plt.title(f'中转站{j} 运输详细'); plt.legend(); plt.grid()
    plt.xlabel('X (km)'); plt.ylabel('Y (km)')

plt.tight_layout()
plt.show()
