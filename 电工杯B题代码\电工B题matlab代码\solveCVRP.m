%% 3. 局部 CVRP 求解函数
function [routes, transCost] = solveCVRP(y, assign, n, m, D, Q, C, w_all)
    routes = cell(m,4);
    transCost = 0;
    for s0 = 1:m
        if ~y(s0), continue; end
        j_idx = n + s0 + 1;  % 全局索引
        for k0 = 1:4
            pts = find(assign(:,k0)==s0)';  % 客户编号
            unserved = pts;
            while ~isempty(unserved)
                curr = j_idx; load = 0; route = curr;
                while true
                    bestD = inf; sel = 0;
                    for ii = unserved
                        idx = ii + 1;
                        if load + w_all(ii+1,k0) <= Q(k0)
                            dval = D(curr, idx);
                            if dval < bestD
                                bestD = dval; sel = ii;
                            end
                        end
                    end
                    if sel==0, break; end
                    idx = sel + 1;
                    route(end+1) = idx;
                    load = load + w_all(sel+1,k0);
                    curr = idx;
                    unserved(unserved==sel) = [];
                end
                route(end+1) = j_idx;
                for t = 1:length(route)-1
                    transCost = transCost + C(k0)*D(route(t),route(t+1));
                end
                routes{s0,k0}{end+1} = route;
            end
        end
    end
end

