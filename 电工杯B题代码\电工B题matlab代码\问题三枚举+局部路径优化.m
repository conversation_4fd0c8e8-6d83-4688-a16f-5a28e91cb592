%% PROBLEM3_TWO_STAGE_OPTIMIZATION.M
% 问题三两阶段启发式：中转站选址 + 分配 + 路径优化
% 选址通过枚举候选站子集，最小化建设成本+运输成本
% 适用于 m=5 小规模站点
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
clear; clc; close all;

%% 1. 基本数据
% 收集点 0=厂区,1..30
pt_data = [...
    0,0; 12,8; 5,15; 20,30; 25,10; 35,22; 18,5; 30,35; 10,25; 22,18; 
    38,15;5,8;15,32;28,5;30,12;10,10;20,20;35,30;8,22;25,25;
    32,8;15,5;28,20;38,25;10,30;20,10;30,18;5,25;18,30;35,10;22,35];
coords = pt_data;  n=30;
% 四类垃圾需求
w_all = [... % 31x4
    zeros(1,4);
    0.72,0.12,0.06,0.30;
    1.38,0.23,0.05,0.64;
    1.08,0.18,0.04,0.50;
    1.55,0.31,0.06,1.18;
    1.62,0.27,0.05,0.76;
    1.76,0.384,0.096,0.96;
    0.77,0.168,0.042,0.42;
    1.02,0.238,0.068,0.374;
    1.32,0.176,0.044,0.66;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    1.45,0.30,0.075,0.675;
    1.35,0.27,0.108,0.972;
    1.87,0.51,0.068,0.952;
    2.58,0.516,0.129,1.075;
    1.134,0.21,0.063,0.693;
    0.78,0.13,0.065,0.325;
    0.768,0.192,0.080,0.56;
    0.72,0.27,0.090,0.72;
    1.595,0.348,0.087,0.87;
    1.50,0.36,0.090,1.05;
    1.08,0.18,0.090,0.45;
    0.912,0.19,0.038,0.76;
    0.90,0.195,0.075,0.33;
    0.99,0.27,0.072,0.468;
    1.44,0.24,0.048,0.672;
    1.74,0.319,0.116,0.725;
    1.17,0.39,0.13,0.91;
    1.70,0.34,0.17,1.19;
    2.64,0.66,0.044,1.056;
    0.864,0.216,0.072,0.648;
    0.986,0.204,0.085,0.425];
% 候选中转站 31..35 坐标
st_coords = [12,5;7,28;20,8;30,15;25,10];
Tj = 10000;      % 建设成本(元/天)

% 车辆参数
Q = [8,6,3,10]; C = [2.5,2.0,5.0,1.8];
v=40;

%% 2. 计算全局距离矩阵
all_coords = [coords; st_coords];  % 31+5=36 x2
D = squareform(pdist(all_coords));

%% 3. 枚举选址子集, 计算总成本
bestCost = inf; bestY=[]; bestAssign=[]; bestRoutes=[];
m=5;
for mask=1:(2^m-1)
    y = bitget(mask,1:m);
    buildCost = sum(y)*Tj;
    % 分配至最近启用站
    assign = zeros(n,4);
    for i=1:n
        for k=1:4
            minD=inf; sel=0;
            for s=1:m
                if y(s)
                    j=30+s;
                    dij=D(i+1,j+1);
                    if dij<minD, minD=dij; sel=s; end
                end
            end
            assign(i,k)=sel;
        end
    end
    % 路径成本%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    transCost=0;
    routes=cell(m,4);
    for s=1:m
        j=30+s;
        for k=1:4
            pts=find(assign(:,k)==s)';
            if isempty(pts), continue; end
            % 本地最近邻
            local=[j, pts+1]; % global idx
            unserved=pts;
            while ~isempty(unserved)
                curr=j; load=0; route=[j];
                while true
                    bD=inf; bi=0;
                    for i=unserved
                        if load+w_all(i+1,k)<=Q(k)
                            d=D(curr+1,i+1);
                            if d<bD, bD=d; bi=i; end
                        end
                    end
                    if bi==0, break; end
                    route=[route, bi]; load=load+w_all(bi+1,k); curr=bi;
                    unserved(unserved==bi)=[];
                end
                route=[route,j];
                % 累加成本
                for t=1:length(route)-1
                    transCost=transCost + C(k)*D(route(t)+1,route(t+1)+1);
                end
                routes{s,k}{end+1}=route;
            end
        end
    end
    totalCost=buildCost+transCost;
    if totalCost<bestCost
        bestCost=totalCost; bestY=y;
        bestAssign=assign; bestRoutes=routes;
    end
end

%% 4. 输出最优选址 & 路线
fprintf('最优建设中转站: ');
for s=1:m, if bestY(s), fprintf('%d ',30+s); end; end
fprintf('\n');
fprintf('最小总成本: %.2f 元\n', bestCost);
for s=1:m
    if ~bestY(s), continue; end
    j=30+s;
    fprintf('--- 中转站 %d 路线 ---\n',j);
    for k=1:4
        rs=bestRoutes{s,k};
        for v=1:length(rs)
            fprintf('类型%d 车%d: %s\n',k,v,mat2str(rs{v}));
        end
    end
end

%% 5. 可视化: 基于bestY,bestAssign,bestRoutes**
%% 6. 可视化
% 全局概览
figure('Name','全局运输路线','NumberTitle','off'); hold on; grid on;
scatter(all_coords(1,1),all_coords(1,2),100,'kd','filled'); text(all_coords(1,1),all_coords(1,2),' 处理厂');
scatter(all_coords(2:31,1),all_coords(2:31,2),40,'ko'); for i=1:30, text(all_coords(i+1,1),all_coords(i+1,2),[' 点',num2str(i)]); end
scatter(all_coords(32:36,1),all_coords(32:36,2),100,'rs','filled'); for s=1:m, if bestY(s), text(all_coords(n+s+1,1),all_coords(n+s+1,2),[' 中转',num2str(n+s)]); end; end
colors = lines(m); lstyles={'-','--',':','-.'};
for s=1:m
    if ~bestY(s), continue; end; j_idx=n+s+1;
    for kk=1:4
        for idx=1:numel(bestRoutes{s,kk})
            route = bestRoutes{s,kk}{idx};
            plot(all_coords(route,1),all_coords(route,2),'Color',colors(s,:),'LineStyle',lstyles{kk},'LineWidth',1.5);
        end
    end
end
title('全局运输路线概览'); xlabel('X (km)'); ylabel('Y (km)'); legend({'厂区','客户','中转站','路线'},'Location','eastoutside');

% 各站详图
for s=1:m
    if ~bestY(s), continue; end; j_idx=n+s+1;
    figure('Name',sprintf('中转站%d 运输详细',n+s),'NumberTitle','off'); hold on; grid on;
    scatter(all_coords(j_idx,1),all_coords(j_idx,2),100,'rs','filled'); text(all_coords(j_idx,1),all_coords(j_idx,2),[' 中转',num2str(n+s)]);
    for kk=1:4
        pts=find(bestAssign(:,kk)==s);
        scatter(all_coords(pts+1,1),all_coords(pts+1,2),50,'bo'); for i=pts', text(all_coords(i+1,1),all_coords(i+1,2),[' 点',num2str(i)]); end
        for idx=1:numel(bestRoutes{s,kk})
            route=bestRoutes{s,kk}{idx}; plot(all_coords(route,1),all_coords(route,2),'LineWidth',1.5);
        end%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    end
title(sprintf('中转站%d 运输详细',n+s)); xlabel('X'); ylabel('Y');
end

