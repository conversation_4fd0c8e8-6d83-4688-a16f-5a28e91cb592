import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
import random

# --- 数据输入 ---
coords = np.array([
    [0,0],
    [12,8],[5,15],[20,30],[25,10],[35,22],[18,5],[30,35],[10,25],[22,18],
    [38,15],[5,8],[15,32],[28,5],[30,12],[10,10],[20,20],[35,30],[8,22],[25,25],
    [32,8],[15,5],[28,20],[38,25],[10,30],[20,10],[30,18],[5,25],[18,30],[35,10],[22,35]
])
w = np.array([0,
    1.2,2.3,1.8,3.1,2.7,1.5,2.9,1.1,2.4,
    3.0,1.7,2.1,3.2,2.6,1.9,2.5,3.3,1.3,2.8,
    3.4,1.6,2.2,3.5,1.4,2.0,3.6,1.0,2.3,3.7,1.9
])  # 吨
Q = 5.0#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
n = len(coords)-1

# 距离矩阵
d = squareform(pdist(coords))

def cost(routes):
    total = 0
    for route in routes:
        for i in range(len(route)-1):
            total += d[route[i], route[i+1]]
    return total

# --- 初始解：最近邻贪心 ---
unserved = list(range(1, n+1))
routes = []
while unserved:
    load = 0; curr = 0; route = [0]
    while True:
        # 找最近可装点
        best = None; bd = np.inf
        for i in unserved:
            if load + w[i] <= Q and d[curr,i] < bd:
                bd = d[curr,i]; best = i
        if best is None: break
        route.append(best)#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        load += w[best]; curr = best; unserved.remove(best)
    route.append(0)
    routes.append(route)

curr_routes = routes.copy()
curr_cost = cost(curr_routes)
best_routes = curr_routes.copy()
best_cost = curr_cost

# --- 模拟退火参数 ---
T = 100.0
T_min = 1e-3
alpha = 0.99

# --- SA 主循环 ---
while T > T_min:
    # 生成新解：随机交换两路径中一点
    new_routes = [r.copy() for r in curr_routes]
    v1 = random.randrange(len(new_routes))
    v2 = random.randrange(len(new_routes))
    if len(new_routes[v1])>2 and len(new_routes[v2])>2:
        p1 = random.randrange(1, len(new_routes[v1])-1)
        p2 = random.randrange(1, len(new_routes[v2])-1)
        # 交换
        new_routes[v1][p1], new_routes[v2][p2] = new_routes[v2][p2], new_routes[v1][p1]
        # 检查载重
        def route_load(rt): return sum(w[i] for i in rt[1:-1])
        if route_load(new_routes[v1])<=Q and route_load(new_routes[v2])<=Q:
            new_cost = cost(new_routes)
            if new_cost < curr_cost or random.random() < np.exp((curr_cost-new_cost)/T):
                curr_routes, curr_cost = new_routes, new_cost
                if curr_cost < best_cost:
                    best_routes, best_cost = curr_routes.copy(), curr_cost
    T *= alpha

# --- 输出 ---
print(f"SA 最优车辆数={len(best_routes)}, 总距离={best_cost:.2f} km")
for idx, rt in enumerate(best_routes,1):
    dist = sum(d[rt[i],rt[i+1]] for i in range(len(rt)-1))
    print(f"  车{idx}: 路线{rt} 距离={dist:.2f} km")
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# --- 可视化 ---
plt.figure(figsize=(8,6))
for rt in best_routes:
    xs, ys = zip(*(coords[rt]))
    plt.plot(xs, ys, '-o')
plt.scatter(coords[0,0], coords[0,1], c='k', s=100, label='厂区')
plt.scatter(coords[1:,0],coords[1:,1], c='b', s=50, label='客户')
plt.title('SA 优化后的 CVRP 路径')
plt.xlabel('X'); plt.ylabel('Y'); plt.legend(); plt.grid(True)
plt.show()
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
import random

# --- CVRP 模拟退火解算函数 ---
def solve_sa(coords, w, Q, T0, alpha, Tf, seed=0):
    random.seed(seed); np.random.seed(seed)
    n = len(coords)-1
    d = squareform(pdist(coords))

    # 最近邻初始化
    unserved = list(range(1,n+1))
    routes = []
    while unserved:
        load, curr = 0, 0
        route = [0]
        while True:
            cand = [(i,d[curr,i]) for i in unserved if load+w[i]<=Q]
            if not cand: break
            i,_ = min(cand, key=lambda x: x[1])
            route.append(i); load+=w[i]; curr=i; unserved.remove(i)
        route.append(0); routes.append(route)

    def cost(rts):
        return sum(d[r[i],r[i+1]] for r in rts for i in range(len(r)-1))

    curr_routes, curr_cost = routes, cost(routes)
    best_routes, best_cost = list(curr_routes), curr_cost
    T = T0

    while T > Tf:
        new_routes = [r.copy() for r in curr_routes]
        # 随机交换两辆车上各一个点
        v1, v2 = random.randrange(len(new_routes)), random.randrange(len(new_routes))
        if len(new_routes[v1])>2 and len(new_routes[v2])>2:
            p1 = random.randrange(1,len(new_routes[v1])-1)
            p2 = random.randrange(1,len(new_routes[v2])-1)
            new_routes[v1][p1], new_routes[v2][p2] = new_routes[v2][p2], new_routes[v1][p1]
            # 检查载重
            def load(rt): return sum(w[i] for i in rt[1:-1])
            if load(new_routes[v1])<=Q and load(new_routes[v2])<=Q:
                new_cost = cost(new_routes)
                if new_cost<curr_cost or random.random()<np.exp((curr_cost-new_cost)/T):
                    curr_routes, curr_cost = new_routes, new_cost
                    if curr_cost<best_cost:
                        best_routes, best_cost = list(curr_routes), curr_cost
        T *= alpha

    return best_cost, best_routes

# --- 数据输入 ---
coords = np.array([
    [0,0],[12,8],[5,15],[20,30],[25,10],[35,22],[18,5],[30,35],[10,25],[22,18],
    [38,15],[5,8],[15,32],[28,5],[30,12],[10,10],[20,20],[35,30],[8,22],[25,25],
    [32,8],[15,5],[28,20],[38,25],[10,30],[20,10],[30,18],[5,25],[18,30],[35,10],[22,35]
])
w = np.array([0,1.2,2.3,1.8,3.1,2.7,1.5,2.9,1.1,2.4,3.0,1.7,2.1,3.2,2.6,1.9,2.5,3.3,1.3,2.8,3.4,1.6,2.2,3.5,1.4,2.0,3.6,1.0,2.3,3.7,1.9])
Q = 5.0

# --- 灵敏度分析：退火率 alpha ---
alphas = np.linspace(0.90, 0.995, 10)
results_alpha = []
for a in alphas:
    c, _ = solve_sa(coords, w, Q, T0=100.0, alpha=a, Tf=1e-3, seed=123)#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    results_alpha.append(c)

plt.figure(figsize=(6,4))
plt.plot(alphas, results_alpha, '-o')
plt.xlabel('降温率 α')
plt.ylabel('最优总距离 (km)')
plt.title('退火率 α 对结果的灵敏度')
plt.grid(True)
plt.show()

# --- 灵敏度分析：初始温度 T0 ---
T0s = [10,50,100,200,500]
results_T0 = []
for T0 in T0s:
    c, _ = solve_sa(coords, w, Q, T0=T0, alpha=0.99, Tf=1e-3, seed=123)
    results_T0.append(c)

plt.figure(figsize=(6,4))
plt.plot(T0s, results_T0, '-s', color='r')
plt.xlabel('初始温度 T0')
plt.ylabel('最优总距离 (km)')
plt.title('初始温度 T0 对结果的灵敏度')
plt.grid(True)
plt.show()
