%% 非对称最近邻算法
function route = asymmetric_nearest_neighbor(nodes, dist_matrix)
    if length(nodes) <= 2
        route = nodes;
        return;
    end
    
    route = [nodes(1)]; % 从起点开始
    unvisited = nodes(2:end-1); % 中间节点
    current = nodes(1);
    
    while ~isempty(unvisited)
        min_dist = inf;
        next_node = -1;
        next_idx = -1;
        
        for i = 1:length(unvisited)
            node = unvisited(i);
            if current + 1 <= size(dist_matrix, 1) && node + 1 <= size(dist_matrix, 2)
                dist = dist_matrix(current + 1, node + 1);
                if dist < min_dist
                    min_dist = dist;
                    next_node = node;
                    next_idx = i;
                end
            end
        end
        
        if next_node >= 0
            route = [route, next_node];
            current = next_node;
            unvisited(next_idx) = [];
        else
            break;
        end
    end
    
    route = [route, nodes(end)]; % 添加终点
end
