%% 简单聚类（非对称版本）
function clusters = simple_clustering_asymmetric(points, demands, capacity)
    clusters = {};
    remaining_points = points;
    remaining_demands = demands;
    
    while ~isempty(remaining_points)
        current_cluster_points = [];
        current_cluster_demands = [];
        current_load = 0;
        
        i = 1;
        while i <= length(remaining_points)
            if current_load + remaining_demands(i) <= capacity
                current_cluster_points = [current_cluster_points, remaining_points(i)];
                current_cluster_demands = [current_cluster_demands, remaining_demands(i)];
                current_load = current_load + remaining_demands(i);
                remaining_points(i) = [];
                remaining_demands(i) = [];
            else
                i = i + 1;
            end
        end
        
        if isempty(current_cluster_points) && ~isempty(remaining_points)
            current_cluster_points = [remaining_points(1)];
            current_cluster_demands = [remaining_demands(1)];
            remaining_points(1) = [];
            remaining_demands(1) = [];
        end
        
        if ~isempty(current_cluster_points)
            clusters{end+1} = {current_cluster_points, current_cluster_demands};
        end
    end
end
