import pandas as pd
import numpy as np
import random
import matplotlib.pyplot as plt

# 1. 读取数据
# 读取收集点数据
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
points_data = pd.read_excel('B题/附件1.xlsx', skiprows=2, names=col_names)

# 读取附件3中的4类垃圾数据
garbage_data = pd.read_excel('B题/附件3.xlsx')

# 读取附件2中的车辆参数
vehicle_params = pd.read_excel('B题/附件2.xlsx')

# 2. 数据预处理
# 提取坐标
points = points_data[['x', 'y']].values
n = len(points)

# 提取各类垃圾重量和体积
garbage_weights = {}
garbage_volumes = {}
for k in range(1, 5):  # 4类垃圾
    garbage_weights[k] = garbage_data[f'w_{k}'].values
    # 假设体积与重量有一定比例关系，可根据实际情况调整
    garbage_volumes[k] = garbage_data[f'v_{k}'].values if f'v_{k}' in garbage_data.columns else garbage_weights[k] * 1.2

# 提取车辆参数
Q = {}  # 载重限制
V = {}  # 容积限制
C = {}  # 单位距离成本
for k in range(1, 5):
    Q[k] = vehicle_params.loc[k-1, 'Q_k']
    V[k] = vehicle_params.loc[k-1, 'V_k']
    C[k] = vehicle_params.loc[k-1, 'C_k']

# 垃圾处理厂坐标
depot = np.array([0, 0])

# 计算距离矩阵
all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 3. 节约算法求解每类垃圾的路径
def savings_algorithm(garbage_type):
    weights = garbage_weights[garbage_type]
    volumes = garbage_volumes[garbage_type]
    max_weight = Q[garbage_type]
    max_volume = V[garbage_type]
    
    # 初始化：每个点一个单独路径
    valid_points = [i+1 for i in range(n) if weights[i] > 0]  # 只考虑有该类垃圾的点
    routes = [[i] for i in valid_points]
    route_weights = [weights[i-1] for i in valid_points]
    route_volumes = [volumes[i-1] for i in valid_points]
    
    # 计算节约值
    savings = []
    for i in valid_points:
        for j in valid_points:
            if i != j:
                s = dist_matrix[i, 0] + dist_matrix[j, 0] - dist_matrix[i, j]
                savings.append((i, j, s))
    
    # 按节约值降序排序
    savings.sort(key=lambda x: x[2], reverse=True)
    
    # 合并路径
    def find_route(routes, node):
        for idx, route in enumerate(routes):
            if node in route:
                return idx
        return None
    
    for i, j, s in savings:
        ri = find_route(routes, i)
        rj = find_route(routes, j)
        
        if ri is not None and rj is not None and ri != rj:
            route_i = routes[ri]
            route_j = routes[rj]
            
            # 检查是否可以合并（端点相连且不超过载重和容积限制）
            can_merge = False
            merged_weight = route_weights[ri] + route_weights[rj]
            merged_volume = route_volumes[ri] + route_volumes[rj]
            
            if merged_weight <= max_weight and merged_volume <= max_volume:
                # 检查端点连接情况
                if route_i[-1] == i and route_j[0] == j:
                    routes[ri] = route_i + route_j
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                    can_merge = True
                elif route_i[0] == i and route_j[-1] == j:
                    routes[ri] = route_j + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                    can_merge = True
                elif route_i[0] == i and route_j[0] == j:
                    routes[ri] = route_j[::-1] + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                    can_merge = True
                elif route_i[-1] == i and route_j[-1] == j:
                    routes[ri] = route_i + route_j[::-1]
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                    can_merge = True
    
    return routes, route_weights, route_volumes

# 4. 计算路径成本
def calculate_route_cost(route, garbage_type):
    cost = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        cost += dist_matrix[prev, node] * C[garbage_type]
        prev = node
    # 返回垃圾处理厂
    cost += dist_matrix[prev, 0] * C[garbage_type]
    return cost

# 5. 求解所有垃圾类型的路径
all_routes = {}
all_costs = {}
total_cost = 0

for k in range(1, 5):
    print(f"\n处理第{k}类垃圾...")
    routes, weights, volumes = savings_algorithm(k)
    
    # 计算该类垃圾的总成本
    type_cost = 0
    for route in routes:
        route_cost = calculate_route_cost(route, k)
        type_cost += route_cost
    
    all_routes[k] = routes
    all_costs[k] = type_cost
    total_cost += type_cost
    
    print(f"第{k}类垃圾共需{len(routes)}辆车，总成本: {type_cost:.2f}")

print(f"\n总运输成本: {total_cost:.2f}")

# 6. 考虑时间约束的模型修改
def calculate_route_time(route, speed=40):  # 速度单位：km/h
    distance = 0
    prev = 0
    for node in route:
        distance += dist_matrix[prev, node]
        prev = node
    distance += dist_matrix[prev, 0]  # 返回垃圾处理厂
    
    # 假设每个点停留时间为10分钟(0.167小时)
    stop_time = len(route) * 0.167
    
    # 总时间 = 行驶时间 + 停留时间
    total_time = distance / speed + stop_time
    return total_time

# 考虑时间约束的路径规划
def time_constrained_routing(garbage_type, max_time=8):  # 最大行驶时间8小时
    routes, weights, volumes = savings_algorithm(garbage_type)
    new_routes = []
    new_weights = []
    new_volumes = []
    
    for i, route in enumerate(routes):
        route_time = calculate_route_time(route)
        
        if route_time <= max_time:
            # 如果路径时间不超过限制，直接添加
            new_routes.append(route)
            new_weights.append(weights[i])
            new_volumes.append(volumes[i])
        else:
            # 如果超过时间限制，需要拆分路径
            current_route = []
            current_weight = 0
            current_volume = 0
            current_time = 0
            
            for node in route:
                # 计算添加当前节点后的时间
                temp_route = current_route + [node]
                temp_time = calculate_route_time(temp_route)
                
                if temp_time <= max_time:
                    # 可以添加当前节点
                    current_route.append(node)
                    current_weight += garbage_weights[garbage_type][node-1]
                    current_volume += garbage_volumes[garbage_type][node-1]
                    current_time = temp_time
                else:
                    # 需要开始新的路径
                    if current_route:
                        new_routes.append(current_route)
                        new_weights.append(current_weight)
                        new_volumes.append(current_volume)
                    
                    # 开始新路径
                    current_route = [node]
                    current_weight = garbage_weights[garbage_type][node-1]
                    current_volume = garbage_volumes[garbage_type][node-1]
                    current_time = calculate_route_time(current_route)
            
            # 添加最后一个路径
            if current_route:
                new_routes.append(current_route)
                new_weights.append(current_weight)
                new_volumes.append(current_volume)
    
    return new_routes, new_weights, new_volumes

# 7. 考虑时间约束的总成本计算
print("\n考虑时间约束(8小时)后的结果:")
time_constrained_routes = {}
time_constrained_costs = {}
time_constrained_total_cost = 0

for k in range(1, 5):
    routes, weights, volumes = time_constrained_routing(k, max_time=8)
    
    # 计算该类垃圾的总成本
    type_cost = 0
    for route in routes:
        route_cost = calculate_route_cost(route, k)
        type_cost += route_cost
    
    time_constrained_routes[k] = routes
    time_constrained_costs[k] = type_cost
    time_constrained_total_cost += type_cost
    
    print(f"第{k}类垃圾共需{len(routes)}辆车，总成本: {type_cost:.2f}")

print(f"\n考虑时间约束后的总运输成本: {time_constrained_total_cost:.2f}")

# 8. 结果可视化
def plot_routes(routes_dict, title):
    plt.figure(figsize=(12, 10))
    plt.scatter(0, 0, c='red', s=200, marker='*', label='垃圾处理厂')
    
    # 绘制收集点
    for i in range(n):
        plt.scatter(points[i, 0], points[i, 1], c='blue', s=100)
        plt.text(points[i, 0]+0.1, points[i, 1]+0.1, f'{i+1}', fontsize=12)
    
    # 绘制路径
    colors = ['g', 'm', 'c', 'y']
    for k in range(1, 5):
        for route in routes_dict[k]:
            prev = 0  # 从垃圾处理厂出发
            for node in route:
                plt.plot([all_points[prev, 0], all_points[node, 0]], 
                         [all_points[prev, 1], all_points[node, 1]], 
                         c=colors[k-1], linewidth=2)
                prev = node
            # 返回垃圾处理厂
            plt.plot([all_points[prev, 0], all_points[0, 0]], 
                     [all_points[prev, 1], all_points[0, 1]], 
                     c=colors[k-1], linewidth=2)
    
    plt.title(title)
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.grid(True)
    plt.legend()
    plt.savefig(f'{title}.png', dpi=300)
    plt.show()

# 绘制不考虑时间约束的路径
plot_routes(all_routes, '不考虑时间约束的多车辆协同路径')

# 绘制考虑时间约束的路径
plot_routes(time_constrained_routes, '考虑时间约束的多车辆协同路径')

# 9. 输出详细结果
print("\n详细路径规划结果:")
for k in range(1, 5):
    print(f"\n第{k}类垃圾路径规划:")
    for i, route in enumerate(all_routes[k]):
        route_str = ' -> '.join(str(r) for r in route)
        route_cost = calculate_route_cost(route, k)
        route_time = calculate_route_time(route)
        print(f"路径{i+1}: 0 -> {route_str} -> 0")
        print(f"  成本: {route_cost:.2f}, 时间: {route_time:.2f}小时")

print("\n考虑时间约束后的详细路径规划结果:")
for k in range(1, 5):
    print(f"\n第{k}类垃圾路径规划(时间约束):")
    for i, route in enumerate(time_constrained_routes[k]):
        route_str = ' -> '.join(str(r) for r in route)
        route_cost = calculate_route_cost(route, k)
        route_time = calculate_route_time(route)
        print(f"路径{i+1}: 0 -> {route_str} -> 0")
        print(f"  成本: {route_cost:.2f}, 时间: {route_time:.2f}小时")