import numpy as np
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt

# Multi-Type Waste Transport CVRP - Greedy Nearest Neighbor + Time Constraint
# 8h/day @ 40 km/h, detailed output and visualization
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
# 1. Input data and parameters
# Columns: x, y, type1, type2, type3, type4 demands
data = np.array([
    [0, 0, 0,     0,     0,     0],
    [12, 8, 0.72,  0.12,  0.06,  0.30],
    [5, 15, 1.38,  0.23,  0.05,  0.64],
    [20,30,1.08,  0.18,  0.04,  0.50],
    [25,10,1.55,  0.31,  0.06,  1.18],
    [35,22,1.62,  0.27,  0.05,  0.76],
    [18,5, 1.76,  0.384, 0.096, 0.96],
    [30,35,0.77,  0.168, 0.042, 0.42],
    [10,25,1.02,  0.238, 0.068, 0.374],
    [22,18,1.32,  0.176, 0.044, 0.66],
    [38,15,1.45,  0.30,  0.075, 0.675],
    [5, 8, 1.35,  0.27,  0.108, 0.972],
    [15,32,1.87,  0.51,  0.068, 0.952],
    [28,5, 2.58,  0.516, 0.129, 1.075],
    [30,12,1.134, 0.21,  0.063, 0.693],
    [10,10,0.78,  0.13,  0.065, 0.325],
    [20,20,0.768, 0.192, 0.080, 0.56],
    [35,30,0.72,  0.27,  0.090, 0.72],
    [8, 22,1.595, 0.348, 0.087, 0.87],
    [25,25,1.50,  0.36,  0.090, 1.05],
    [32,8, 1.08,  0.18,  0.090, 0.45],
    [15,5, 0.912, 0.19,  0.038, 0.76],#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    [28,20,0.90,  0.195, 0.075, 0.33],
    [38,25,0.99,  0.27,  0.072, 0.468],
    [10,30,1.44,  0.24,  0.048, 0.672],
    [20,10,1.74,  0.319, 0.116, 0.725],
    [30,18,1.17,  0.39,  0.130, 0.91],
    [5, 25,1.70,  0.34,  0.170, 1.19],
    [18,30,2.64,  0.66,  0.044, 1.056],
    [35,10,0.864, 0.216, 0.072, 0.648],
    [22,35,0.986, 0.204, 0.085, 0.425]
])
coords = data[:, :2]
w_all = data[:, 2:]
n = coords.shape[0] - 1

Q = np.array([8, 6, 3, 10])      # capacity limits
C = np.array([2.5, 2.0, 5.0, 1.8])  # cost per km
dayspeed = 40                # km/h
Tmax = 4# hours per day
Dmax = dayspeed * Tmax      # max distance per vehicle

# Distance matrix
D = squareform(pdist(coords))

# Containers for all types
all_routes = []
all_loads = []
all_dists = []
all_times = []
all_costs = []

# 2. Greedy assignment with time constraint
for k in range(4):
    print(f"\n=== Type {k+1} (Q={Q[k]} t, C={C[k]} per km) ===")
    unserved = [i for i in range(1, n+1) if w_all[i, k] > 0]
    routes_k, loads_k, dists_k, times_k = [], [], [], []

    while unserved:
        curr_node, curr_load, cum_dist = 0, 0.0, 0.0
        path = [0]
        # load until no more feasible points
        while True:
            best_d, best_i = np.inf, None
            for i in unserved:
                demand = w_all[i, k]
                if curr_load + demand <= Q[k]:
                    d_to_i = D[curr_node, i]
                    new_dist = cum_dist + d_to_i + D[i, 0]
                    if new_dist <= Dmax and d_to_i < best_d:
                        best_d, best_i = d_to_i, i
            if best_i is None:
                break
            path.append(best_i)
            curr_load += w_all[best_i, k]
            cum_dist += best_d
            curr_node = best_i
            unserved.remove(best_i)
        # return to depot
        path.append(0)
        cum_dist += D[curr_node, 0]#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        cum_time = cum_dist / dayspeed

        routes_k.append(path)
        loads_k.append(curr_load)
        dists_k.append(cum_dist)
        times_k.append(cum_time)
        print(f"  Vehicle {len(routes_k):2d}: Path {path}, Load {curr_load:.2f} t, Distance {cum_dist:.2f} km, Time {cum_time:.2f} h")

    total_cost = sum(dists_k) * C[k]
    print(f"Type {k+1}: Vehicles={len(routes_k)}, Total Dist={sum(dists_k):.2f} km, Total Time={sum(times_k):.2f} h, Cost={total_cost:.2f}")

    all_routes.append(routes_k)
    all_loads.append(loads_k)
    all_dists.append(dists_k)
    all_times.append(times_k)
    all_costs.append(total_cost)

print(f"\n== All Types Total Cost: {sum(all_costs):.2f} ==")

# 3. Visualization: combined routes
plt.figure(figsize=(8, 6))
plt.scatter(coords[0,0], coords[0,1], marker='s', s=120, label='Depot')
plt.scatter(coords[1:,0], coords[1:,1], marker='o', s=60, label='Customer')
for i in range(1, n+1):
    plt.text(coords[i,0], coords[i,1], str(i))
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
styles = ['-', '--', ':', '-.']
colors = plt.cm.tab10.colors
for k, routes_k in enumerate(all_routes):
    for path in routes_k:
        pts = np.array([coords[i] for i in path])
        plt.plot(pts[:,0], pts[:,1], styles[k], linewidth=1.8, label=f'Type {k+1}' if path==routes_k[0] else "")

plt.title('Multi-Type Routes with 8h/day Constraint')
plt.xlabel('X (km)')
plt.ylabel('Y (km)')
plt.legend(loc='best')
plt.grid(True)
plt.show()

# 4. Visualization: summary bar and pie charts
fig, axs = plt.subplots(4, 1, figsize=(6, 10))
axs[0].bar(range(1,5), [sum(l) for l in all_loads])
axs[0].set_title('Total Load by Type (t)')
axs[1].bar(range(1,5), [sum(d) for d in all_dists])
axs[1].set_title('Total Distance by Type (km)')
axs[2].bar(range(1,5), [sum(t) for t in all_times])
axs[2].set_title('Total Time by Type (h)')
axs[3].bar(range(1,5), all_costs)
axs[3].set_title('Total Cost by Type')
for ax in axs: ax.set_xlabel('Type')
plt.tight_layout()
plt.show()
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
plt.figure(figsize=(6, 6))
plt.pie(all_costs, labels=[f'Type {i}' for i in range(1,5)], autopct='%1.1f%%')
plt.title('Cost Share by Type')
plt.show()
