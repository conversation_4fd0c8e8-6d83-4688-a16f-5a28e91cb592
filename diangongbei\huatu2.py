import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns

sns.set(style="white", font_scale=1.3)
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['pdf.fonttype'] = 42

file_path = "B题/附件1.xlsx"
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
data = pd.read_excel(file_path, skiprows=2, names=col_names)

x = data['x'].values
y = data['y'].values
z = data['w'].values

# 归一化用于气泡大小和颜色
normed_z = (z - z.min()) / (z.max() - z.min())
cmap = sns.color_palette("mako", as_cmap=True)
colors = cmap(normed_z)
sizes = 80 + 220 * normed_z  # 气泡大小随垃圾量变化

fig = plt.figure(figsize=(9, 7))
ax = fig.add_subplot(111, projection='3d')

# 背景美化
ax.set_facecolor('#F7F7F7')
ax.w_xaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
ax.w_yaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
ax.w_zaxis.set_pane_color((1.0, 1.0, 1.0, 1.0))
ax.w_xaxis.line.set_linewidth(1.5)
ax.w_yaxis.line.set_linewidth(1.5)
ax.w_zaxis.line.set_linewidth(1.5)
ax.w_xaxis._axinfo['grid']['color'] =  (1,1,1,0)
ax.w_yaxis._axinfo['grid']['color'] =  (1,1,1,0)
ax.w_zaxis._axinfo['grid']['color'] =  (1,1,1,0)

# 三维气泡
sc = ax.scatter(x, y, z, c=colors, s=sizes, alpha=0.85, edgecolor='white', linewidth=1.2, zorder=10)

# 坐标轴
ax.set_xlabel('X坐标', labelpad=14, fontsize=14, fontweight='bold')
ax.set_ylabel('Y坐标', labelpad=14, fontsize=14, fontweight='bold')
ax.set_zlabel('垃圾量', labelpad=18, fontsize=14, fontweight='bold')
ax.set_title('垃圾分类收集点三维气泡图', fontsize=16, fontweight='bold', pad=20)

# 色条
import matplotlib as mpl
m = mpl.cm.ScalarMappable(cmap=cmap)
m.set_array(z)
cbar = fig.colorbar(m, ax=ax, shrink=0.5, aspect=40, pad=0.15, location='right')
cbar.set_label('垃圾量', fontsize=13, fontweight='bold', labelpad=15, loc='top')
cbar.ax.tick_params(labelsize=12)

ax.view_init(elev=28, azim=120)
plt.tight_layout(pad=2.5)
plt.savefig('垃圾分类点三维气泡图.pdf', dpi=600, bbox_inches='tight')
plt.show()
