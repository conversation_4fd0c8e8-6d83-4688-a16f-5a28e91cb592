"""
城市垃圾分类运输路径优化问题求解代码
问题: 单一车辆类型下的基础路径优化与调度（CVRP）
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import math
from typing import List, Tuple, Dict
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

class CVRPSolver:
    def __init__(self):
        self.coords = None
        self.demands = None
        self.dist_matrix = None
        self.Q = None  # 车辆最大载重量
        self.n = None  # 收集点数量

    # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    def load_data(self):
        """加载数据"""
        # 收集点坐标和垃圾产生量数据
        data = np.array([
            [0, 0, 0, 0],  # 处理厂(编号0)
            [1, 12, 8, 1.2],  # 收集点1
            [2, 5, 15, 2.3],  # 收集点2
            [3, 20, 30, 1.8],  # 收集点3
            [4, 25, 10, 3.1],  # 收集点4
            [5, 35, 22, 2.7],  # 收集点5
            [6, 18, 5, 1.5],  # 收集点6
            [7, 30, 35, 2.9],  # 收集点7
            [8, 10, 25, 1.1],  # 收集点8
            [9, 22, 18, 2.4],  # 收集点9
            [10, 38, 15, 3.0],  # 收集点10
            [11, 5, 8, 1.7],  # 收集点11
            [12, 15, 32, 2.1],  # 收集点12
            [13, 28, 5, 3.2],  # 收集点13
            [14, 30, 12, 2.6],  # 收集点14
            [15, 10, 10, 1.9],  # 收集点15
            [16, 20, 20, 2.5],  # 收集点16#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
            [17, 35, 30, 3.3],  # 收集点17
            [18, 8, 22, 1.3],  # 收集点18
            [19, 25, 25, 2.8],  # 收集点19
            [20, 32, 8, 3.4],  # 收集点20
            [21, 15, 5, 1.6],  # 收集点21
            [22, 28, 20, 2.2],  # 收集点22
            [23, 38, 25, 3.5],  # 收集点23
            [24, 10, 30, 1.4],  # 收集点24
            [25, 20, 10, 2.0],  # 收集点25
            [26, 30, 18, 3.6],  # 收集点26
            [27, 5, 25, 1.0],  # 收集点27
            [28, 18, 30, 2.3],  # 收集点28
            [29, 35, 10, 3.7],  # 收集点29
            [30, 22, 35, 1.9],  # 收集点30
        ])

        # 提取坐标和需求量
        self.coords = data[:, 1:3]  # 坐标 [x, y]
        self.demands = data[:, 3]  # 垃圾产生量
        self.n = len(self.coords) - 1  # 收集点数量（不包括处理厂）
        self.Q = 5  # 车辆最大载重量（吨）

        print(f'问题规模: {self.n}个收集点, 车辆载重: {self.Q:.1f}吨')
        print(f'总垃圾量: {np.sum(self.demands[1:]):.1f}吨')
        print(f'理论最少车辆数: {math.ceil(np.sum(self.demands[1:]) / self.Q)}辆')

    def calculate_distance_matrix(self):
        """计算距离矩阵"""
        size = self.n + 1
        self.dist_matrix = np.zeros((size, size))

        for i in range(size):
            for j in range(size):
                if i != j:
                    dx = self.coords[i, 0] - self.coords[j, 0]
                    dy = self.coords[i, 1] - self.coords[j, 1]
                    self.dist_matrix[i, j] = np.sqrt(dx ** 2 + dy ** 2)

        print('距离矩阵计算完成')

    def sweep_algorithm(self) -> List[List[int]]:
        """扫描算法进行聚类"""
        depot = self.coords[0]  # 处理厂坐标

        # 计算每个收集点相对于处理厂的极角
        angles = []
        for i in range(1, self.n + 1):
            dx = self.coords[i, 0] - depot[0]
            dy = self.coords[i, 1] - depot[1]
            angle = math.atan2(dy, dx)
            angles.append((angle, i - 1))  # i-1是收集点编号(0-based)

        # 按极角排序
        angles.sort(key=lambda x: x[0])
        sorted_points = [point for _, point in angles]

        # 按顺序分配到车辆
        clusters = []
        current_cluster = []
        current_load = 0

        for point in sorted_points:
            point_demand = self.demands[point + 1]  # +1因为处理厂是索引0

            if current_load + point_demand <= self.Q:
                current_cluster.append(point)
                current_load += point_demand
            else:
                # 当前聚类已满，开始新聚类
                if current_cluster:
                    clusters.append(current_cluster)
                current_cluster = [point]
                current_load = point_demand

        # 添加最后一个聚类
        if current_cluster:
            clusters.append(current_cluster)

        print(f'  聚类完成: {len(clusters)}个聚类')
        return clusters

    def nearest_neighbor_tsp(self, points: List[int]) -> List[int]:
        """最近邻算法"""
        if not points:
            return [0, 0]

        if len(points) == 1:
            return [0, points[0], 0]

        unvisited = points.copy()
        route = [0]  # 从处理厂开始
        current = 0  # 处理厂的索引是0

        while unvisited:
            min_dist = float('inf')
            next_point = -1
            next_idx = -1

            for i, point in enumerate(unvisited):
                dist = self.dist_matrix[current, point + 1]  # +1因为收集点索引偏移
                if dist < min_dist:
                    min_dist = dist
                    next_point = point
                    next_idx = i

            route.append(next_point)
            current = next_point + 1  # +1因为距离矩阵索引
            unvisited.pop(next_idx)

        route.append(0)  # 返回处理厂
        return route

    def two_opt_improvement(self, route: List[int]) -> List[int]:
        """2-opt改进算法"""
        improved_route = route.copy()
        n = len(route)
        improved = True
        max_iterations = 100
        iteration = 0

        while improved and iteration < max_iterations:
            improved = False
            iteration += 1

            for i in range(1, n - 2):  # 不包括起点和终点
                for j in range(i + 1, n - 1):
                    # 计算当前距离
                    current_dist = (self.dist_matrix[improved_route[i - 1] + (1 if improved_route[i - 1] != 0 else 0),
                                                     improved_route[i] + (1 if improved_route[i] != 0 else 0)] +
                                    self.dist_matrix[improved_route[j] + (1 if improved_route[j] != 0 else 0),
                                                     improved_route[j + 1] + (1 if improved_route[j + 1] != 0 else 0)])

                    # 计算交换后的距离
                    new_dist = (self.dist_matrix[improved_route[i - 1] + (1 if improved_route[i - 1] != 0 else 0),
                                                 improved_route[j] + (1 if improved_route[j] != 0 else 0)] +
                                self.dist_matrix[improved_route[i] + (1 if improved_route[i] != 0 else 0),
                                                 improved_route[j + 1] + (1 if improved_route[j + 1] != 0 else 0)])

                    if new_dist < current_dist:
                        # 执行2-opt交换
                        improved_route[i:j + 1] = improved_route[i:j + 1][::-1]
                        improved = True

        return improved_route

    def optimize_route(self, cluster: List[int]) -> List[int]:
        """路径优化函数"""
        if len(cluster) == 1:
            return [0, cluster[0], 0]

        # 最近邻算法构造初始路径
        initial_route = self.nearest_neighbor_tsp(cluster)

        # 2-opt局部搜索改进
        optimized_route = self.two_opt_improvement(initial_route)
        return optimized_route

    def calculate_route_distance(self, route: List[int]) -> float:
        """计算路径距离"""
        distance = 0
        for i in range(len(route) - 1):
            from_idx = route[i] + (1 if route[i] != 0 else 0)  # 处理厂索引转换
            to_idx = route[i + 1] + (1 if route[i + 1] != 0 else 0)
            distance += self.dist_matrix[from_idx, to_idx]
        return distance

    def solve_cvrp(self) -> Tuple[List[List[int]], float, int]:
        """主求解函数"""
        # 第一阶段: 使用扫描算法进行聚类分组
        print('\n第一阶段: 扫描算法聚类分组')
        clusters = self.sweep_algorithm()

        # 第二阶段: 对每个聚类进行路径优化
        print('第二阶段: 路径优化')
        best_routes = []
        total_distance = 0

        for i, cluster in enumerate(clusters):
            print(f'  优化车辆{i + 1}的路径 (包含{len(cluster)}个收集点)...', end='')

            # 使用最近邻算法 + 2-opt改进
            optimized_route = self.optimize_route(cluster)
            best_routes.append(optimized_route)

            route_distance = self.calculate_route_distance(optimized_route)
            total_distance += route_distance

            print(f' 完成，距离: {route_distance:.2f}公里')

        return best_routes, total_distance, len(clusters)

    def visualize_solution(self, routes: List[List[int]], total_distance: float):
        """可视化函数"""
        plt.figure(figsize=(12, 10))

        # 颜色设置
        colors = ['r', 'b', 'g', 'm', 'c', 'k', 'y', 'orange', 'purple', 'brown']
        if len(routes) > len(colors):
            colors = colors * ((len(routes) // len(colors)) + 1)

        # 绘制处理厂
        plt.plot(self.coords[0, 0], self.coords[0, 1], 'ks', markersize=15, markerfacecolor='k')
        plt.text(self.coords[0, 0] + 0.5, self.coords[0, 1] + 0.5, '处理厂',
                 fontsize=12, fontweight='bold')

        # 绘制收集点
        for i in range(1, len(self.coords)):
            plt.plot(self.coords[i, 0], self.coords[i, 1], 'o', markersize=8,
                     markerfacecolor='w', markeredgecolor='k', linewidth=1.5)
            plt.text(self.coords[i, 0] + 0.5, self.coords[i, 1] + 0.5, str(i - 1), fontsize=10)

        # 绘制每辆车的路径
        for v, route in enumerate(routes):
            color = colors[v % len(colors)]
            plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
            plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
            for i in range(len(route) - 1):
                from_idx = route[i] + (1 if route[i] != 0 else 0)
                to_idx = route[i + 1] + (1 if route[i + 1] != 0 else 0)

                # 绘制路径线
                plt.plot([self.coords[from_idx, 0], self.coords[to_idx, 0]],
                         [self.coords[from_idx, 1], self.coords[to_idx, 1]],
                         color, linewidth=2, label=f'车辆{v + 1}' if i == 0 else "")

                # 添加箭头指示方向
                dx = self.coords[to_idx, 0] - self.coords[from_idx, 0]
                dy = self.coords[to_idx, 1] - self.coords[from_idx, 1]
                mid_x = self.coords[from_idx, 0] + 0.7 * dx
                mid_y = self.coords[from_idx, 1] + 0.7 * dy

                # 简化的箭头
                arrow_length = 0.8
                angle = math.atan2(dy, dx)
                plt.annotate('', xy=(mid_x, mid_y),
                             xytext=(mid_x - arrow_length * math.cos(angle),
                                     mid_y - arrow_length * math.sin(angle)),
                             arrowprops=dict(arrowstyle='->', color=color, lw=1))

        # 图形设置
        plt.grid(True, alpha=0.3)
        plt.xlabel('X坐标 (公里)', fontsize=12)
        plt.ylabel('Y坐标 (公里)', fontsize=12)
        plt.title(f'车辆路径规划结果\n车辆数量: {len(routes)}辆, 总距离: {total_distance:.2f}公里',
                  fontsize=14)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.axis('equal')
        plt.tight_layout()

        # 保存图片
        plt.savefig('CVRP_solution.png', dpi=300, bbox_inches='tight')
        print('\n结果图已保存为 CVRP_solution.png')
        plt.show()

    def run(self):
        """运行主程序"""
        print("城市垃圾分类运输路径优化问题求解")
        print("=" * 50)

        # 1. 数据输入
        self.load_data()

        # 2. 计算距离矩阵
        self.calculate_distance_matrix()

        # 3. 主求解函数
        start_time = time.time()
        best_routes, best_distance, vehicle_count = self.solve_cvrp()
        solve_time = time.time() - start_time

        # 4. 结果输出
        print('\n' + '=' * 10 + ' 求解结果 ' + '=' * 10)
        print(f'求解时间: {solve_time:.4f}秒')
        print(f'使用车辆数量: {vehicle_count}辆')
        print(f'总行驶距离: {best_distance:.2f}公里')
        print(f'平均每辆车行驶距离: {best_distance / vehicle_count:.2f}公里')

        # 输出每辆车的路径详情
        for v, route in enumerate(best_routes):
            route_demand = sum(self.demands[point + 1] for point in route[1:-1])  # 排除起点终点
            route_distance = self.calculate_route_distance(route)

            route_str = ' -> '.join(str(point) for point in route)
            print(f'\n车辆{v + 1}路径: {route_str}')
            print(f'  载重: {route_demand:.1f}/{self.Q:.1f}吨, 行驶距离: {route_distance:.2f}公里')

        # 5. 可视化结果
        self.visualize_solution(best_routes, best_distance)

        # 6. 模型复杂度分析
        print('\n' + '=' * 10 + ' 复杂度分析 ' + '=' * 10)
        print(f'时间复杂度: O(n²) = O({self.n}²) = O({self.n ** 2})')
        print(f'空间复杂度: O(n²) = O({self.n}²) = O({self.n ** 2})')


def main():
    """主函数"""
    solver = CVRPSolver()
    solver.run()


if __name__ == "__main__":
    main()