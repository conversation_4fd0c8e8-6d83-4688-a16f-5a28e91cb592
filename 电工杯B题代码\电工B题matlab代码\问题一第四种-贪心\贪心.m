%% VRP_GREEDY_NEAREST.M
% 基于最近邻贪心的CVRP求解（单一车辆类型，Q=5吨示例）

clear; clc; close all;

%% 一、输入数据
data = [ ...
    0,   0,  0;   % 0: 处理厂
    12,  8,  1.2;
    5,   15, 2.3;
    20,  30, 1.8;
    25,  10, 3.1;
    35,  22, 2.7;
    18,   5, 1.5;
    30,  35, 2.9;
    10,  25, 1.1;
    22,  18, 2.4;
    38,  15, 3.0;
    5,    8, 1.7;
    15,  32, 2.1;
    28,   5, 3.2;
    30,  12, 2.6;
    10,  10, 1.9;
    20,  20, 2.5;
    35,  30, 3.3;
    8,   22, 1.3;
    25,  25, 2.8;
    32,   8, 3.4;
    15,   5, 1.6;
    28,  20, 2.2;
    38,  25, 3.5;
    10,  30, 1.4;
    20,  10, 2.0;
    30,  18, 3.6;
    5,   25, 1.0;
    18,  30, 2.3;
    35,  10, 3.7;
    22,  35, 1.9
];
coords = data(:,1:2);
w      = data(:,3);
Q      = 5;                   % 车辆最大载重
n      = size(coords,1) - 1;  % 客户数量

%% 二、预处理：距离矩阵
D = squareform(pdist(coords));   % (n+1)x(n+1)

%% 三、最近邻贪心构造解
unserved = 1:n;   % 待服务客户编号（1..n 对应 coords 行 2..n+1）
routes   = {};
while ~isempty(unserved)
    currLoad = 0;
    route    = [0];  % 从厂区出发
    currNode = 0;
    % 尽可能装点
    while true
        % 在未服务点中找距离 currNode 最近的且装载后<=Q
        bestD = inf; bestIdx = -1;
        for i = unserved
            if currLoad + w(i+1) <= Q
                d = D(currNode+1, i+1);
                if d < bestD
                    bestD = d;
                    bestIdx = i;
                end
            end
        end
        if bestIdx < 0
            break;  % 没有可装的新点
        end
        % 选择 bestIdx
        route    = [route, bestIdx];
        currLoad = currLoad + w(bestIdx+1);
        currNode = bestIdx;
        unserved(unserved==bestIdx) = [];  % 标记已服务
    end
    route = [route, 0];  % 返回厂区
    routes{end+1} = route;
end

%% 四、计算各车距离与总距离
numVeh   = numel(routes);
distances = zeros(numVeh,1);
loads     = zeros(numVeh,1);
for k = 1:numVeh
    rt = routes{k};
    % 计算路径长度
    d = 0;
    for t = 1:length(rt)-1
        d = d + D(rt(t)+1, rt(t+1)+1);
    end
    distances(k) = d;
    % 计算载重
    loads(k) = sum(w(rt(2:end-1)+1));
end
totalDist = sum(distances);

%% 五、输出结果
fprintf('贪心最近邻法 车辆数=%d, 总行驶距离=%.2f km\n', numVeh, totalDist);
for k = 1:numVeh
    fprintf('  车%d: 路线 %s | 载重 %.2f 吨 | 距离 %.2f km\n', ...
             k, mat2str(routes{k}), loads(k), distances(k));
end

%% 六、可视化
% 1) 路线分布图
figure('Name','Greedy 路线分布','NumberTitle','off'); hold on; grid on;
scatter(coords(1,1),coords(1,2),100,'ks','filled'); text(coords(1,1),coords(1,2),' 0');
scatter(coords(2:end,1),coords(2:end,2),50,'bo');
for i=2:n+1, text(coords(i,1),coords(i,2),[' ',num2str(i-1)]); end
cols = lines(numVeh);
for k=1:numVeh
    rt = routes{k}+1;
    plot(coords(rt,1),coords(rt,2),'-o','Color',cols(k,:),'LineWidth',1.5);
end
title('各车贪心路径'); xlabel('X (km)'); ylabel('Y (km)');
legend(['厂区', arrayfun(@(k)sprintf('车%d',k),1:numVeh,'uni',false)], ...
       'Location','bestoutside');

% 2) 每车载重 & 距离柱状图
figure('Name','载重 & 距离','NumberTitle','off');
subplot(2,1,1); bar(loads); title('每车载重'); ylabel('吨');
subplot(2,1,2); bar(distances); title('每车行驶距离'); ylabel('km');

% 3) 距离占比饼图
figure('Name','距离占比','NumberTitle','off');
pie(distances, arrayfun(@(k)sprintf('V%d',k),1:numVeh,'uni',false));
title('各车里程占比');
