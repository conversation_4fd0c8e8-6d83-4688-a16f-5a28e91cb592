%% VRP_GREEDY_MULTI_TRIP_TIMEBOUND.M
% 最近邻贪心生成 trips + 基于日里程上限的车辆数计算 + First-Fit 装桶
clear; clc; close all;

%% 1. 输入数据
data = [ ...
    0,   0,  0;   % 0: 处理厂
    12,  8,  1.2;
    5,   15, 2.3;
    20,  30, 1.8;
    25,  10, 3.1;
    35,  22, 2.7;
    18,   5, 1.5;
    30,  35, 2.9;
    10,  25, 1.1;
    22,  18, 2.4;
    38,  15, 3.0;
    5,    8, 1.7;
    15,  32, 2.1;
    28,   5, 3.2;
    30,  12, 2.6;
    10,  10, 1.9;
    20,  20, 2.5;
    35,  30, 3.3;
    8,   22, 1.3;
    25,  25, 2.8;
    32,   8, 3.4;
    15,   5, 1.6;
    28,  20, 2.2;
    38,  25, 3.5;
    10,  30, 1.4;
    20,  10, 2.0;
    30,  18, 3.6;
    5,   25, 1.0;
    18,  30, 2.3;
    35,  10, 3.7;
    22,  35, 1.9
];
coords = data(:,1:2);
w      = data(:,3);
Q      = 5;                   % 车辆最大载重
n      = size(coords,1) - 1;  % 客户数量

%% 2. 距离矩阵
D = squareform(pdist(coords));   % (n+1)x(n+1)

%% 3. 生成所有“单趟” trips
unserved = 1:n;
trips = {}; loads = []; dists = [];
while ~isempty(unserved)
    currLoad = 0;
    currNode = 0;
    trip = 0;  % 从厂区出发
    while true
        bestD = inf; bestI = -1;
        for i = unserved
            if currLoad + w(i+1) <= Q
                dij = D(currNode+1, i+1);
                if dij < bestD
                    bestD = dij; bestI = i;
                end
            end
        end
        if bestI < 0
            break;
        end
        trip      = [trip, bestI];
        currLoad  = currLoad + w(bestI+1);
        currNode  = bestI;
        unserved(unserved == bestI) = [];
    end
    trip = [trip, 0];  % 返回厂区
    td = 0;
    for t = 1:length(trip)-1
        td = td + D(trip(t)+1, trip(t+1)+1);
    end
    trips{end+1} = trip;
    loads(end+1) = currLoad;
    dists(end+1) = td;
end

%% 4. 按日里程上限计算最少车辆数 K
v    = 40;      % 速度 km/h
Tmax = 8;       % 每日最大时长 h
Dmax = v * Tmax; % 每车日最大里程
totalTripDist = sum(dists);
K = ceil(totalTripDist / Dmax);
fprintf('总行程 %.1f km, 每车日上限 %.1f km => 需车辆 %d 辆\n', totalTripDist, Dmax, K);

%% 5. First-Fit 分配 trips 给 K 辆车
vehRemain = Dmax * ones(1,K);
vehicles  = cell(1,K);
vehLoad   = cell(1,K);
vehDist   = cell(1,K);

for t = 1:numel(trips)
    assigned = false;
    for vid = 1:K
        if vehRemain(vid) >= dists(t)
            vehicles{vid}{end+1} = trips{t};
            vehLoad{vid}(end+1)  = loads(t);
            vehDist{vid}(end+1)  = dists(t);
            vehRemain(vid) = vehRemain(vid) - dists(t);
            assigned = true;
            break;
        end
    end
    if ~assigned
        error('First-Fit 分配失败：车辆数不足');
    end
end

%% 6. 输出结果
for vid = 1:K
    fprintf('\n=== 车辆 %d (剩余 %.1f km) ===\n', vid, vehRemain(vid));
    for r = 1:numel(vehicles{vid})
        fprintf(' 趟 %2d: 路径 %s | 载重 %.2f 吨 | 距离 %.2f km\n', ...
            r, mat2str(vehicles{vid}{r}), vehLoad{vid}(r), vehDist{vid}(r));
    end
    fprintf(' 该车累计载重 %.2f 吨, 行程 %.2f km\n', sum(vehLoad{vid}), sum(vehDist{vid}));
end

%% 7. 可视化
figure('Name','车辆多趟路径分配','NumberTitle','off'); hold on; grid on;
scatter(coords(1,1), coords(1,2), 120, 'ks', 'filled');
text(coords(1,1), coords(1,2), ' 0', 'FontWeight','bold');
scatter(coords(2:end,1), coords(2:end,2), 50, 'bo');
cols = lines(K);
for vid = 1:K
    for r = 1:numel(vehicles{vid})
        rt = vehicles{vid}{r} + 1;  % 转为 MATLAB 索引
        plot(coords(rt,1), coords(rt,2), '-o', 'Color', cols(vid,:), 'LineWidth',1.5);
    end
end
title('基于日里程上限的多趟路径分配');
xlabel('X (km)'); ylabel('Y (km)');
legend(['厂区', arrayfun(@(v)sprintf('车%d',v),1:K,'UniformOutput',false)], 'Location','bestoutside');
