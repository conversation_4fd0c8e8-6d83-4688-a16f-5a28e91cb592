%% 第一阶段：设施选址求解函数
function [selected_stations, allocation_plan, facility_cost] = solve_facility_location(...
    coords, ts_coords, ts_costs, ts_capacities, demands, dist_matrix, C)
    
    n = size(coords, 1) - 1; % 收集点数量
    m = size(ts_coords, 1);  % 候选中转站数量
    total_demands = sum(demands(2:end, :), 1); % 各类垃圾总需求
    
    fprintf('  使用贪心启发式算法求解设施选址...\n');
    
    % 贪心选址算法
    selected_stations = [];
    remaining_demand = total_demands;
    facility_cost = 0;
    allocation_plan = containers.Map('KeyType', 'int32', 'ValueType', 'any');
    
    % 计算每个收集点到每个中转站的距离成本
    transport_costs = zeros(n, m);
    for i = 1:n
        for j = 1:m
            % 收集点i到中转站j再到处理厂的距离
            dist_to_ts = dist_matrix(i+1, n+1+j); % 收集点到中转站
            dist_ts_to_depot = dist_matrix(n+1+j, 1); % 中转站到处理厂
            transport_costs(i, j) = dist_to_ts + dist_ts_to_depot;
        end
    end
    
    % 逐步选择中转站
    while any(remaining_demand > 0) && length(selected_stations) < m
        best_station = -1;
        best_benefit = -inf;
        best_allocation = [];
        
        % 评估每个未选中的中转站
        for j = 1:m
            if ismember(j, selected_stations)
                continue;
            end
            
            % 计算选择中转站j的收益
            [benefit, temp_allocation] = evaluate_station_benefit(...
                j, coords, ts_capacities(j, :), remaining_demand, ...
                transport_costs(:, j), ts_costs(j));
            
            if benefit > best_benefit
                best_benefit = benefit;
                best_station = j;
                best_allocation = temp_allocation;
            end
        end
        
        % 选择最佳中转站
        if best_station > 0
            selected_stations = [selected_stations, best_station];
            allocation_plan(best_station) = best_allocation;
            facility_cost = facility_cost + ts_costs(best_station);
            
            % 更新剩余需求
            allocated_demand = zeros(1, 4);
            for point = best_allocation
                allocated_demand = allocated_demand + demands(point+1, :);
            end
            remaining_demand = max(0, remaining_demand - allocated_demand);
            
            fprintf('    选择中转站 %d, 服务收集点 %d 个\n', ...
                    best_station, length(best_allocation));
        else
            break;
        end
    end
    
        % 处理剩余未分配需求
        if any(remaining_demand > 0)
            fprintf('    处理剩余未分配需求...\n');
            % 找出所有未分配的收集点
            all_allocated = [];
            map_keys = keys(allocation_plan);
            for k = 1:length(map_keys)
                key = map_keys{k};
                assigned = allocation_plan(key);
                all_allocated = [all_allocated, assigned];
            end
            
            for i = 1:n
                if ~ismember(i-1, all_allocated)  % i-1 因为收集点从0开始编号
                    % 分配给距离最近的中转站
                    min_cost = inf;
                    best_station = selected_stations(1);
                    
                    for j = 1:length(selected_stations)
                        station_idx = selected_stations(j);
                        cost = transport_costs(i, station_idx-30);  % station_idx-30转换为数组索引
                        if cost < min_cost
                            min_cost = cost;
                            best_station = station_idx;
                        end
                    end
                    
                    % 更新分配
                    current_allocation = allocation_plan(best_station);
                    allocation_plan(best_station) = [current_allocation, i-1];
                end
            end
        end
end