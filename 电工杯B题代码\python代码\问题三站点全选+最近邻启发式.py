import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform

# --- 1. 输入数据 ---
# 点 0=厂区，1–30=收集点，31–35=中转站#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
pt_data = np.array([
    [ 0,   0,   0,      0,    0,    0,    0],   # 0: 处理厂
    [12,   8,   0.72, 0.12, 0.06, 0.30, 0],
    [ 5,  15,   1.38, 0.23, 0.05, 0.64, 0],
    [20,  30,   1.08, 0.18, 0.04, 0.50, 0],
    [25,  10,   1.55, 0.31, 0.06, 1.18, 0],
    [35,  22,   1.62, 0.27, 0.05, 0.76, 0],
    [18,   5,   1.76, 0.384,0.096,0.96,0],
    [30,  35,   0.77, 0.168,0.042,0.42,0],
    [10,  25,   1.02, 0.238,0.068,0.374,0],
    [22,  18,   1.32, 0.176,0.044,0.66, 0],
    [38,  15,   1.45, 0.30, 0.075,0.675,0],
    [ 5,   8,   1.35, 0.27, 0.108,0.972,0],
    [15,  32,   1.87, 0.51, 0.068,0.952,0],
    [28,   5,   2.58, 0.516,0.129,1.075,0],
    [30,  12,   1.134,0.21, 0.063,0.693,0],
    [10,  10,   0.78, 0.13, 0.065,0.325,0],
    [20,  20,   0.768,0.192,0.080,0.56, 0],
    [35,  30,   0.72, 0.27, 0.09, 0.72, 0],
    [ 8,  22,   1.595,0.348,0.087,0.87, 0],
    [25,  25,   1.50, 0.36, 0.09, 1.05, 0],
    [32,   8,   1.08, 0.18, 0.09, 0.45, 0],
    [15,   5,   0.912,0.19, 0.038,0.76,0],
    [28,  20,   0.90, 0.195,0.075,0.33, 0],
    [38,  25,   0.99, 0.27, 0.072,0.468,0],
    [10,  30,   1.44, 0.24, 0.048,0.672,0],
    [20,  10,   1.74, 0.319,0.116,0.725,0],
    [30,  18,   1.17, 0.39, 0.13, 0.91, 0],
    [ 5,  25,   1.70, 0.34, 0.17, 1.19, 0],
    [18,  30,   2.64, 0.66, 0.044,1.056,0],
    [35,  10,   0.864,0.216,0.072,0.648,0],#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    [22,  35,   0.986,0.204,0.085,0.425,0],
])
coords = pt_data[:, :2]
w_all  = pt_data[:, 2:6]   # (31×4)
n = 30

# --- 2. 候选中转站 ---
st_data = np.array([
    [31, 5, 20, 6],   # id, S1,S2,S3,S4
    [32,10,25,8],
    [33, 8,15,7],
    [34,12,30,9],
    [35, 7,18,5]
])
# 坐标示例（可按实际替换）
st_coords = np.array([
    [25,15],
    [7,28],
    [20,8],
    [30,15],
    [15,10]
])
all_coords = np.vstack((coords, st_coords))

# --- 3. 参数 ---
Q = [8,6,3,10]
C = [2.5,2.0,5.0,1.8]
a = [0.8,0.6,1.2,0.7]
beta = [0.3,0.2,0.5,0.25]

# --- 4. 距离矩阵 ---
D = squareform(pdist(all_coords))

# --- 5. 阶段一：全选中转站 + 最近站分配 ---
y = np.ones(5, dtype=bool)
assign = np.zeros((n,4), dtype=int)
for i in range(n):
    for k in range(4):
        dists = D[i+1, 31:36]
        assign[i,k] = np.argmin(dists)  # 对应站索引 0–4

# --- 6. 阶段二：对每站每类型最近邻CVRP ---
all_routes = [[[] for _ in range(4)] for __ in range(5)]
all_dist   = np.zeros((5,4))
all_E      = np.zeros((5,4))

for s in range(5):
    j = 31 + s
    for k in range(4):
        pts = np.where(assign[:,k]==s)[0]
        if pts.size==0: continue
        # 本地坐标与需求
        local_coords = np.vstack((all_coords[j], all_coords[pts+1]))
        local_w      = w_all[pts+1, k]
        Dloc = squareform(pdist(local_coords))
        unserved = list(range(1,len(local_coords)))
        routes = []
        # 贪心最近邻
        while unserved:
            curr, load, route = 0, 0, [0]
            while True:
                cand = [i for i in unserved if load+local_w[i-1]<=Q[k]]
                if not cand: break
                next_i = min(cand, key=lambda i: Dloc[curr,i])
                route.append(next_i)
                load += local_w[next_i-1]
                curr = next_i
                unserved.remove(next_i)
            route.append(0)
            routes.append(route)
        # 计算距离与排放
        dist, E = 0, 0
        for r in routes:
            seq_w = sum(local_w[np.array(r[1:-1])-1])
            for t in range(len(r)-1):#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
                duv = Dloc[r[t], r[t+1]]
                dist += duv
                E    += duv*a[k] + beta[k]*seq_w
        all_routes[s][k] = routes
        all_dist[s,k]    = dist
        all_E[s,k]       = E

# --- 7. 输出 & 可视化 ---
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
print("阶段1: 选址 y =", y.astype(int))
print("\n阶段2 结果:")
for s in range(5):
    for k in range(4):
        routes = all_routes[s][k]
        if not routes: continue
        print(f"  站{31+s} 类别{k+1}: {len(routes)}趟, 距离{all_dist[s,k]:.1f}, 排放{all_E[s,k]:.1f}")

# 7.1 单站点-单类型示例
s, k = 0, 0  # 站31, 类型1
routes = all_routes[s][k]
plt.figure(figsize=(6,6))
plt.scatter(all_coords[0,0],all_coords[0,1],c='k',s=100,marker='s',label='厂区')
pts_glob = np.where(assign[:,k]==s)[0]+1
plt.scatter(all_coords[pts_glob,0], all_coords[pts_glob,1], c='b', label='客户')
plt.scatter(all_coords[30+s,0], all_coords[30+s,1], c='r', marker='s', label='中转站31')
for r in routes:
    xs, ys = zip(*[all_coords[30+s] if i==0 else all_coords[i] for i in r])
    plt.plot(xs, ys, '-o')
plt.title('中转站31 厨余垃圾示例路径')
plt.legend(); plt.grid(); plt.xlabel('X (km)'); plt.ylabel('Y (km)')

# 7.2 单站点-多类型子图
plt.figure(figsize=(10,8))
for k in range(4):
    plt.subplot(2,2,k+1)
    plt.scatter(all_coords[30+s,0], all_coords[30+s,1], c='r', s=100, marker='s')#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    pts_glob = np.where(assign[:,k]==s)[0]+1
    plt.scatter(all_coords[pts_glob,0], all_coords[pts_glob,1], c='b')
    for r in all_routes[s][k]:
        xs, ys = zip(*[all_coords[30+s] if i==0 else all_coords[i] for i in r])
        plt.plot(xs, ys, '-o')
    plt.title(f'中转站31 类型{k+1}路径')
    plt.grid(); plt.xlabel('X'); plt.ylabel('Y')
plt.tight_layout()

# 7.3 全局可视化
plt.figure(figsize=(8,6))
plt.scatter(all_coords[0,0],all_coords[0,1],c='k',s=100,marker='s',label='厂区')
plt.scatter(all_coords[1:31,0],all_coords[1:31,1],c='gray',label='客户')
plt.scatter(all_coords[31:36,0],all_coords[31:36,1],c='r',marker='s',label='中转站')
linestyles = ['-','--',':','-.']
for s in range(5):
    for k in range(4):
        for r in all_routes[s][k]:
            xs, ys = zip(*[all_coords[31+s] if i==0 else all_coords[i] for i in r])
            plt.plot(xs, ys, linestyle=linestyles[k], label=f'S{s+31}T{k+1}')
plt.title('全局运输路线概览')
plt.legend(loc='center left', bbox_to_anchor=(1,0.5)); plt.grid()
plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.tight_layout()
plt.show()
