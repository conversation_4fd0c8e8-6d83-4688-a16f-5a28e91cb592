function bestCost = sa_cvrp(T0, alpha, Tf, seed, coords, w, Q)
    rng(seed);
    % 从传入的coords自动获取客户数
    n = size(coords,1) - 1;
    D = squareform(pdist(coords));

    % --- 最近邻构造初解 ---
    unserved = 1:n;
    routes = {};
    while ~isempty(unserved)
        load = 0; curr = 1;   % MATLAB索引 1 对应“厂区”
        route = 1;
        while true
            cand = [];
            for i = unserved
                if load + w(i+1) <= Q
                    cand(end+1,:) = [i, D(curr,i+1)]; %#ok<AGROW>
                end
            end
            if isempty(cand), break; end
            [~, idx] = min(cand(:,2));
            next = cand(idx,1);
            route(end+1) = next+1;
            load = load + w(next+1);
            curr = next+1;
            unserved(unserved==next) = [];
        end
        route(end+1) = 1;
        routes{end+1} = route; %#ok<AGROW>
    end

    costFcn = @(R) sum(cellfun(@(r) sum(arrayfun(@(k) D(r(k),r(k+1)), 1:numel(r)-1)), R));

    currRoutes = routes;
    currCost   = costFcn(routes);
    bestCost   = currCost;
    T = T0;

    % --- 模拟退火主循环 ---
    while T > Tf
        newRoutes = currRoutes;
        v1 = randi(numel(newRoutes)); 
        v2 = randi(numel(newRoutes));
        if numel(newRoutes{v1})>2 && numel(newRoutes{v2})>2
            p1 = randi([2, numel(newRoutes{v1})-1]);
            p2 = randi([2, numel(newRoutes{v2})-1]);
            tmp = newRoutes{v1}(p1);
            newRoutes{v1}(p1) = newRoutes{v2}(p2);
            newRoutes{v2}(p2) = tmp;
            % 检查载重
            load1 = sum(w(newRoutes{v1}(2:end-1)));
            load2 = sum(w(newRoutes{v2}(2:end-1)));
            if load1<=Q && load2<=Q
                newCost = costFcn(newRoutes);
                if newCost < currCost || rand < exp((currCost-newCost)/T)
                    currRoutes = newRoutes; 
                    currCost   = newCost;
                    bestCost   = min(bestCost, currCost);
                end
            end
        end
        T = T * alpha;
    end
end
