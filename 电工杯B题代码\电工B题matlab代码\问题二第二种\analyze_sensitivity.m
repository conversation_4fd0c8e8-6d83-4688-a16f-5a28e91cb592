%% 敏感性分析
function analyze_sensitivity(total_demands, Q, C, all_costs, all_vehicle_counts)
    
    fprintf('车辆容量敏感性:\n');
    for k = 1:4
        if all_vehicle_counts(k) > 0
            % 容量利用率
            utilization = total_demands(k) / (all_vehicle_counts(k) * Q(k)) * 100;
            fprintf('  %s: 容量利用率 %.1f%%\n', ...
                    ['厨余垃圾','可回收物','有害垃圾','其他垃圾'], utilization);
            
            % 如果容量增加20%的影响
            new_capacity = Q(k) * 1.2;
            new_vehicle_count = ceil(total_demands(k) / new_capacity);
            potential_saving = (all_vehicle_counts(k) - new_vehicle_count) / ...
                              all_vehicle_counts(k) * 100;
            if potential_saving > 0
                fprintf('    容量增加20%%可减少车辆 %.1f%%\n', potential_saving);
            end
        end
    end
    
    fprintf('\n成本结构分析:\n');
    total_cost = sum(all_costs);
    for k = 1:4
        if all_costs(k) > 0
            cost_ratio = all_costs(k) / total_cost * 100;
            fprintf('  车型%d成本占比: %.1f%%\n', k, cost_ratio);
        end
    end
end