%% 处理未分配收集点（非对称版本）
function handle_unallocated_points_asymmetric(selected_stations, allocation_plan, ...
                                            transport_costs, n)
    
    allocated_points = [];
    station_keys = keys(allocation_plan);
    for k = 1:length(station_keys)
        key = station_keys{k};
        assigned = allocation_plan(key);
        allocated_points = [allocated_points, assigned];
    end
    
    for i = 1:n
        if ~ismember(i-1, allocated_points)
            min_cost = inf;
            best_station = selected_stations(1);
            
            for j = 1:length(selected_stations)
                station_idx = selected_stations(j) + 1; % 转为1-based数组索引
                if station_idx <= size(transport_costs, 2)
                    cost = transport_costs(i, station_idx);
                    if cost < min_cost
                        min_cost = cost;
                        best_station = selected_stations(j);
                    end
                end
            end
            
            current_allocation = allocation_plan(best_station);
            allocation_plan(best_station) = [current_allocation, i-1];
        end
    end
end


