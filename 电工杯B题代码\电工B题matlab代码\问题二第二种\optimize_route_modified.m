%% 修改后的路径优化
function optimized_route = optimize_route_modified(cluster, dist_matrix)
    if isempty(cluster)
        optimized_route = [0, 0];
        return;
    end
    
    if length(cluster) == 1
        optimized_route = [0, cluster, 0];
        return;
    end
    
    % 最近邻算法
    initial_route = nearest_neighbor_tsp_modified(cluster, dist_matrix);
    
    % 2-opt改进
    optimized_route = two_opt_improvement_modified(initial_route, dist_matrix);
end


