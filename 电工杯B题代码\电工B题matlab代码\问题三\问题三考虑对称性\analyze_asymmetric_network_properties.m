%% 非对称网络特性分析
function analyze_asymmetric_network_properties(dist_matrix, constraints)
    fprintf('\n=== 非对称网络特性分析 ===\n');
    
    n = size(dist_matrix, 1);
    
    % 计算非对称性度量
    asymmetry_ratio = 0;
    total_pairs = 0;
    
    for i = 1:n
        for j = i+1:n
            if dist_matrix(i,j) > 0 || dist_matrix(j,i) > 0
                total_pairs = total_pairs + 1;
                if dist_matrix(i,j) ~= dist_matrix(j,i)
                    asymmetry_ratio = asymmetry_ratio + 1;
                end
            end
        end
    end
    
    asymmetry_percentage = asymmetry_ratio / total_pairs * 100;
    
    fprintf('网络非对称性统计:\n');
    fprintf('  总边对数: %d\n', total_pairs);
    fprintf('  非对称边对数: %d\n', asymmetry_ratio);
    fprintf('  非对称性比例: %.1f%%\n', asymmetry_percentage);
    
    % 约束类型统计
    single_direction = sum(constraints(:,4) == -1);
    time_restricted = sum(constraints(:,4) > 0);
    
    fprintf('约束类型分析:\n');
    fprintf('  单向道路约束: %d条\n', single_direction);
    fprintf('  时间禁行约束: %d条\n', time_restricted);
    
    % 影响评估
    if asymmetry_percentage > 20
        fprintf('网络高度非对称，建议使用专门的非对称算法\n');
    elseif asymmetry_percentage > 10
        fprintf('网络中度非对称，需要考虑非对称性影响\n');
    else
        fprintf('网络轻度非对称，对称算法可能适用\n');
    end
end
