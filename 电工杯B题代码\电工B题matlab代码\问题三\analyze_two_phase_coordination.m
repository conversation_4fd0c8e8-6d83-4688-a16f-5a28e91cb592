%% 两阶段协同机制分析
function analyze_two_phase_coordination(selected_stations, allocation_plan, ...
                                       routing_solution, phase1_cost, phase2_cost)
    
    fprintf('第一阶段对第二阶段的影响:\n');
    fprintf('  1. 选址决策影响:\n');
    fprintf('     - 中转站数量: %d个\n', length(selected_stations));
    fprintf('     - 影响路径长度和车辆调度\n');
    fprintf('  2. 分配决策影响:\n');
    
    station_keys = keys(allocation_plan);
    for i = 1:length(station_keys)
        station_id = station_keys{i};
        assigned_points = allocation_plan(station_id);
        fprintf('     - 中转站%d服务%d个收集点\n', station_id, length(assigned_points));
    end
    
    fprintf('\n第二阶段对第一阶段的反馈:\n');
    fprintf('  1. 容量约束反馈: 路径优化结果验证中转站容量充足性\n');
    fprintf('  2. 成本反馈: 实际运输成本 %.2f 元\n', phase2_cost);
    fprintf('  3. 时间窗约束: 影响中转站的可达性和服务效率\n');
    
    fprintf('\n协同优化效果:\n');
    fprintf('  - 设施成本与运输成本比例: %.1f:%.1f\n', ...
            phase1_cost/(phase1_cost+phase2_cost)*100, ...
            phase2_cost/(phase1_cost+phase2_cost)*100);
    
    % 分析如果不建中转站的成本差异
    direct_cost_estimate = estimate_direct_transport_cost();
    total_cost_with_ts = phase1_cost + phase2_cost;
    
    fprintf('  - 直接运输估算成本: %.2f 元\n', direct_cost_estimate);
    fprintf('  - 含中转站总成本: %.2f 元\n', total_cost_with_ts);
    
    if total_cost_with_ts < direct_cost_estimate
        savings = direct_cost_estimate - total_cost_with_ts;
        fprintf('  - 中转站模式节约: %.2f 元 (%.1f%%)\n', ...
                savings, savings/direct_cost_estimate*100);
    else
        extra_cost = total_cost_with_ts - direct_cost_estimate;
        fprintf('  - 中转站模式额外成本: %.2f 元 (%.1f%%)\n', ...
                extra_cost, extra_cost/direct_cost_estimate*100);
    end
end
