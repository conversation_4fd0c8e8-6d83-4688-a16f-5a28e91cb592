import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置图片清晰度
plt.rcParams['figure.dpi'] = 300

# 读取文件
excel_file = pd.ExcelFile(f'C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\附件1.xlsx')

# 获取指定工作表中的数据，设置表头行为1
df = excel_file.parse('附件130个垃圾分类收集点坐标及总垃圾量', header=1)

# 重新设置列名
df.columns = ['Collection Point ID', 'X-coordinate (km)', 'Y-coordinate (km)', 'Garbage Volume (tons)', 'Remark']

# 提取坐标数据
coordinates = df[['X-coordinate (km)', 'Y-coordinate (km)']].values

num_points = len(coordinates)
distance_matrix = np.zeros((num_points, num_points))

# 计算距离矩阵
for i in range(num_points):
    for j in range(num_points):
        distance_matrix[i, j] = np.sqrt(((coordinates[i] - coordinates[j]) ** 2).sum())

# 将距离矩阵转换为 DataFrame
distance_df = pd.DataFrame(distance_matrix)

# 保存到 Excel 文件
distance_df.to_excel('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\distance_matrix.xlsx', index=False)

# 绘制收集点
plt.figure(figsize=(10, 8))

# 处理厂（编号0）用红色标记，其他收集点用蓝色标记
for i in range(num_points):
    if df['Collection Point ID'][i] == 0:
        plt.scatter(coordinates[i, 0], coordinates[i, 1], c='r', label='Waste Treatment Plant' if i == 0 else "")
    else:
        plt.scatter(coordinates[i, 0], coordinates[i, 1], c='b', label='Collection Point' if i == 1 else "")

# 添加图标题和坐标轴标签
plt.title('Distribution of Garbage Collection Points')
plt.xlabel('X-coordinate (km)')
plt.ylabel('Y-coordinate (km)')
plt.legend()

# 保存图片
plt.savefig('C:\\Users\\<USER>\\PyCharmMiscProject\\电工杯\\collection_points_distribution.png')

# 显示图形
plt.show()