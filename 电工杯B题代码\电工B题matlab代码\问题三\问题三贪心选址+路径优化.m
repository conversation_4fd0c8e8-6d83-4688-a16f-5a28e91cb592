%% 问题三：含中转站选址与时间窗口的综合优化求解代码


clear; clc; close all;

%% 1. 数据输入
fprintf('=== 问题三：含中转站选址的综合优化 ===\n');

% 收集点坐标和各类垃圾产生量数据
collection_data = [
    0,  0,  0,   0,     0,     0,     0;      % 处理厂
    1,  12, 8,   0.72,  0.12,  0.06,  0.3;   % 收集点1
    2,  5,  15,  1.38,  0.23,  0.05,  0.64;  % 收集点2
    3,  20, 30,  1.08,  0.18,  0.04,  0.5;   % 收集点3
    4,  25, 10,  1.55,  0.31,  0.06,  1.18;  % 收集点4
    5,  35, 22,  1.62,  0.27,  0.05,  0.76;  % 收集点5
    6,  18, 5,   1.76,  0.384, 0.096, 0.96;  % 收集点6
    7,  30, 35,  0.77,  0.168, 0.042, 0.42;  % 收集点7
    8,  10, 25,  1.02,  0.238, 0.068, 0.374; % 收集点8
    9,  22, 18,  1.32,  0.176, 0.044, 0.66;  % 收集点9
    10, 38, 15,  1.45,  0.3,   0.075, 0.675; % 收集点10
    11, 5,  8,   1.35,  0.27,  0.108, 0.972; % 收集点11
    12, 15, 32,  1.87,  0.51,  0.068, 0.952; % 收集点12
    13, 28, 5,   2.58,  0.516, 0.129, 1.075; % 收集点13
    14, 30, 12,  1.134, 0.21,  0.063, 0.693; % 收集点14
    15, 10, 10,  0.78,  0.13,  0.065, 0.325; % 收集点15
    16, 20, 20,  0.768, 0.192, 0.08,  0.56;  % 收集点16
    17, 35, 30,  0.72,  0.27,  0.09,  0.72;  % 收集点17
    18, 8,  22,  1.595, 0.348, 0.087, 0.87;  % 收集点18
    19, 25, 25,  1.5,   0.36,  0.09,  1.05;  % 收集点19
    20, 32, 8,   1.08,  0.18,  0.09,  0.45;  % 收集点20
    21, 15, 5,   0.912, 0.19,  0.038, 0.76;  % 收集点21
    22, 28, 20,  0.9,   0.195, 0.075, 0.33;  % 收集点22
    23, 38, 25,  0.99,  0.27,  0.072, 0.468; % 收集点23
    24, 10, 30,  1.44,  0.24,  0.048, 0.672; % 收集点24
    25, 20, 10,  1.74,  0.319, 0.116, 0.725; % 收集点25
    26, 30, 18,  1.17,  0.39,  0.13,  0.91;  % 收集点26
    27, 5,  25,  1.7,   0.34,  0.17,  1.19;  % 收集点27
    28, 18, 30,  2.64,  0.66,  0.044, 1.056; % 收集点28
    29, 35, 10,  0.864, 0.216, 0.072, 0.648; % 收集点29
    30, 22, 35,  0.986, 0.204, 0.085, 0.425; % 收集点30
];

% 候选中转站数据 [编号, x坐标, y坐标, 建设成本, 时间窗开始, 时间窗结束, 存储容量1-4]
transfer_station_data = [
    31, 15, 15, 50000, 6,  18, 15, 10, 5,  20;  % 中转站1
    32, 25, 20, 45000, 7,  17, 12, 8,  4,  18;  % 中转站2
    33, 10, 25, 40000, 8,  16, 10, 6,  3,  15;  % 中转站3
    34, 30, 10, 55000, 6,  18, 18, 12, 6,  25;  % 中转站4
    35, 20, 30, 48000, 7,  17, 14, 9,  4,  20;  % 中转站5
];

% 提取基础数据
coords = collection_data(:, 2:3);  % 收集点坐标
demands = collection_data(:, 4:7); % 各类垃圾需求矩阵
n = size(coords, 1) - 1; % 收集点数量

% 中转站数据
ts_coords = transfer_station_data(:, 2:3); % 中转站坐标
ts_costs = transfer_station_data(:, 4) / 10; % 年化建设成本（10年摊销）
ts_time_windows = transfer_station_data(:, 5:6); % 时间窗
ts_capacities = transfer_station_data(:, 7:10); % 各类垃圾存储容量
m = size(ts_coords, 1); % 候选中转站数量

% 合并所有位置坐标（处理厂 + 收集点 + 中转站）
all_coords = [coords; ts_coords];
total_nodes = n + 1 + m; % 总节点数

% 车辆参数
vehicle_params = [
    8,  20, 2.5, 0.8,  0.3;    % 厨余垃圾车 [载重, 容积, 成本, α, β]
    6,  25, 2.0, 0.6,  0.2;    % 可回收物车
    3,  10, 5.0, 1.2,  0.5;    % 有害垃圾车
    10, 18, 1.8, 0.7,  0.25;   % 其他垃圾车
];

Q = vehicle_params(:, 1);      % 载重限制
V = vehicle_params(:, 2);      % 容积限制
C = vehicle_params(:, 3);      % 单位距离成本
alpha = vehicle_params(:, 4);  % 碳排放系数α
beta = vehicle_params(:, 5);   % 碳排放系数β

% 时间参数
vehicle_speed = 40;      % 车辆速度 km/h
service_time = 0.25;     % 服务时间 h
depot_hours = [6, 18];   % 处理厂工作时间

% 垃圾类型名称
garbage_types = {'厨余垃圾', '可回收物', '有害垃圾', '其他垃圾'};

fprintf('收集点数量: %d个\n', n);
fprintf('候选中转站数量: %d个\n', m);
fprintf('车辆类型数: %d种\n', length(garbage_types));

%% 2. 计算扩展距离矩阵
fprintf('\n=== 计算扩展距离矩阵 ===\n');
dist_matrix = zeros(total_nodes, total_nodes);

for i = 1:total_nodes
    for j = 1:total_nodes
        if i ~= j
            dist_matrix(i,j) = sqrt((all_coords(i,1) - all_coords(j,1))^2 + ...
                                  (all_coords(i,2) - all_coords(j,2))^2);
        end
    end
end

fprintf('扩展距离矩阵计算完成 (%dx%d)\n', total_nodes, total_nodes);

%% 3. 两阶段求解算法
fprintf('\n=== 开始两阶段综合优化求解 ===\n');
tic;

% 第一阶段：中转站选址与分配
fprintf('\n--- 第一阶段：中转站选址与收集点分配 ---\n');
[selected_stations, allocation_plan, phase1_cost] = solve_facility_location(...
    coords, ts_coords, ts_costs, ts_capacities, demands, dist_matrix, C);

fprintf('第一阶段完成:\n');
fprintf('  选择中转站数量: %d个\n', length(selected_stations));
fprintf('  中转站编号: ');
for i = 1:length(selected_stations)
    fprintf('%d ', selected_stations(i));
end
fprintf('\n');
fprintf('  设施建设成本: %.2f元/年\n', phase1_cost);

% 第二阶段：针对每个中转站的路径优化
fprintf('\n--- 第二阶段：路径优化与碳排放控制 ---\n');
[routing_solution, phase2_cost, total_emissions] = solve_routing_with_emissions(...
    selected_stations, allocation_plan, coords, ts_coords, demands, ...
    dist_matrix, Q, C, alpha, beta, ts_time_windows, depot_hours, vehicle_speed);

total_time = toc;

%% 4. 结果汇总与输出
total_cost = phase1_cost + phase2_cost;

fprintf('\n========== 综合优化求解结果 ==========\n');
fprintf('总求解时间: %.4f秒\n', total_time);
fprintf('选中的中转站: %d个\n', length(selected_stations));
fprintf('中转站建设成本: %.2f元/年\n', phase1_cost);
fprintf('运输成本: %.2f元/年\n', phase2_cost);
fprintf('总成本: %.2f元/年\n', total_cost);
fprintf('总碳排放: %.2f kg/年\n', total_emissions);
fprintf('单位成本碳排放: %.3f kg/元\n', total_emissions/total_cost);

% 详细输出每个中转站的路径方案
fprintf('\n=== 各中转站详细运输方案 ===\n');
output_detailed_routes(routing_solution, selected_stations, allocation_plan, ...
                      garbage_types, Q, C);

%% 5. 分析两阶段协同机制
fprintf('\n=== 两阶段协同机制分析 ===\n');
analyze_two_phase_coordination(selected_stations, allocation_plan, ...
                              routing_solution, phase1_cost, phase2_cost);

%% 6. 可视化结果
visualize_lrp_solution(coords, ts_coords, selected_stations, allocation_plan, ...
                      routing_solution, total_cost, total_emissions);




%% 主函数最后添加复杂度分析调用
fprintf('\n=== 模型复杂度分析 ===\n');
analyze_model_complexity(n, m);



% 调用时间窗口影响演示
demonstrate_time_window_impact();