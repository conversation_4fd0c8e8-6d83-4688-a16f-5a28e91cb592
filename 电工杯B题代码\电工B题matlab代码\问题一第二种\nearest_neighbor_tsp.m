%% 最近邻算法
function route = nearest_neighbor_tsp(points, dist_matrix)
    n = length(points);
    if n == 0
        route = [0, 0];%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
        return;
    end
    
    unvisited = points;
    route = [0]; % 从处理厂开始
    current = 1; % 处理厂的索引是1（MATLAB索引从1开始）
    
    while ~isempty(unvisited)
        min_dist = inf;
        next_point = -1;
        next_idx = -1;
        
        for i = 1:length(unvisited)
            point = unvisited(i);
            dist = dist_matrix(current, point + 1); % +1因为MATLAB索引
            if dist < min_dist
                min_dist = dist;
                next_point = point;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
                next_idx = i;
            end
        end
        
        route = [route, next_point];
        current = next_point + 1; % +1因为MATLAB索引
        unvisited(next_idx) = [];
    end%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    
    route = [route, 0]; % 返回处理厂
end

