%% 评估中转站收益函数
function [benefit, allocation] = evaluate_station_benefit(...
    station_id, coords, capacities, remaining_demand, transport_costs, build_cost)
    
    n = size(coords, 1) - 1;
    
    % 简化收益计算：基于运输成本节约 - 建设成本
    transport_savings = 0;
    allocation = [];
    
    % 选择运输成本最低的收集点，直到容量限制
    [sorted_costs, sorted_indices] = sort(transport_costs);
    current_capacity = capacities; % 各类垃圾的容量限制
    
    for i = 1:length(sorted_indices)
        point_idx = sorted_indices(i);
        point_demands = [0.72, 0.12, 0.06, 0.3]; % 简化：使用平均需求
        
        % 检查容量约束
        if all(point_demands <= current_capacity)
            allocation = [allocation, point_idx];
            current_capacity = current_capacity - point_demands;
            transport_savings = transport_savings + (50 - sorted_costs(i)); % 基准距离50km
        end
    end
    
    benefit = transport_savings - build_cost;
end
