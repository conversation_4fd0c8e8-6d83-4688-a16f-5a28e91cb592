import numpy as np
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt

# Multi-Type Waste Transport CVRP - Greedy Nearest Neighbor + Detailed Output + Visualization

# 1. Input Data
# Columns: x, y, demand_type1, demand_type2, demand_type3, demand_type4
raw_data = np.array([
    [0,   0,    0.00,  0.00,   0.00,  0.00],  # Depot
    [12,  8,    0.72,  0.12,   0.06,  0.30],
    [5,   15,   1.38,  0.23,   0.05,  0.64],
    [20,  30,   1.08,  0.18,   0.04,  0.50],
    [25,  10,   1.55,  0.31,   0.06,  1.18],
    [35,  22,   1.62,  0.27,   0.05,  0.76],
    [18,  5,    1.76,  0.384,  0.096, 0.96],
    [30,  35,   0.77,  0.168,  0.042, 0.42],
    [10,  25,   1.02,  0.238,  0.068, 0.374],
    [22,  18,   1.32,  0.176,  0.044, 0.66],
    [38,  15,   1.45,  0.30,   0.075, 0.675],
    [5,   8,    1.35,  0.27,   0.108,0.972],
    [15,  32,   1.87,  0.51,   0.068,0.952],
    [28,  5,    2.58,  0.516,  0.129,1.075],
    [30,  12,   1.134, 0.21,   0.063,0.693],
    [10,  10,   0.78,  0.13,   0.065,0.325],
    [20,  20,   0.768, 0.192,  0.080,0.56],
    [35,  30,   0.72,  0.27,   0.090,0.72],
    [8,   22,   1.595, 0.348,  0.087,0.87],
    [25,  25,   1.50,  0.36,   0.090,1.05],
    [32,  8,    1.08,  0.18,   0.090,0.45],
    [15,  5,    0.912, 0.19,   0.038,0.76],
    [28,  20,   0.90,  0.195,  0.075,0.33],
    [38,  25,   0.99,  0.27,   0.072,0.468],
    [10,  30,   1.44,  0.24,   0.048,0.672],
    [20,  10,   1.74,  0.319,  0.116,0.725],
    [30,  18,   1.17,  0.39,   0.130,0.91],
    [5,   25,   1.70,  0.34,   0.170,1.19],
    [18,  30,   2.64,  0.66,   0.044,1.056],
    [35,  10,   0.864, 0.216,  0.072,0.648],
    [22,  35,   0.986, 0.204,  0.085,0.425]
])
coords = raw_data[:, :2]
demands = raw_data[:, 2:]
num_customers = coords.shape[0] - 1

# Vehicle parameters
capacities = [8, 6, 3, 10]   # Q for each type
costs = [2.5, 2.0, 5.0, 1.8]   # cost per km for each type

# Distance matrix
dist_matrix = squareform(pdist(coords))

# Storage for results
all_routes = []
all_loads = []
all_dists = []
all_costs = []

# 2. Nearest Neighbor for each type
for t in range(4):
    print(f"\n=== Waste Type {t+1} (Q={capacities[t]} t, Cost={costs[t]} per km) ===")
    # indices of customers with positive demand of this type
    unserved = [i for i in range(1, num_customers+1) if demands[i, t] > 0]
    routes_t, loads_t, dists_t = [], [], []
    vehicle_count = 0

    while unserved:
        vehicle_count += 1
        curr_node, curr_load = 0, 0.0
        route = [0]
        # load until no feasible next customer
        while True:
            best_dist = np.inf
            best_cust = None
            for i in unserved:
                if curr_load + demands[i, t] <= capacities[t]:
                    d = dist_matrix[curr_node, i]
                    if d < best_dist:
                        best_dist = d
                        best_cust = i
            if best_cust is None:
                break
            route.append(best_cust)
            curr_load += demands[best_cust, t]
            curr_node = best_cust
            unserved.remove(best_cust)
        # return to depot
        route.append(0)
        # compute total dist
        total_d = sum(dist_matrix[route[i], route[i+1]] for i in range(len(route)-1))
        # store
        routes_t.append(route)
        loads_t.append(curr_load)
        dists_t.append(total_d)
        print(f"  Vehicle {vehicle_count}: Route {route}, Load={curr_load:.2f} t, Distance={total_d:.2f} km")

    type_cost = sum(dists_t) * costs[t]
    print(f"Type {t+1} Summary: Vehicles={vehicle_count}, Total Dist={sum(dists_t):.2f} km, Cost={type_cost:.2f}")

    all_routes.append(routes_t)
    all_loads.append(loads_t)
    all_dists.append(dists_t)
    all_costs.append(type_cost)

print(f"\n== Overall Total Cost: {sum(all_costs):.2f} ==")

# 3. Visualization: Routes overview
plt.figure(figsize=(8,6))
plt.scatter(coords[0,0], coords[0,1], marker='s', s=100, label='Depot')
plt.scatter(coords[1:,0], coords[1:,1], marker='o', s=50, label='Customers')
for i in range(1, num_customers+1):
    plt.text(coords[i,0], coords[i,1], str(i))

linestyles = ['-', '--', ':', '-.']
colors = plt.cm.tab10.colors
for t in range(4):
    for idx, route in enumerate(all_routes[t]):
        pts = np.array([coords[i] for i in route])
        label = f"Type {t+1}" if idx==0 else None
        plt.plot(pts[:,0], pts[:,1], linestyle=linestyles[t], linewidth=1.8,
                 color=colors[t], label=label)

plt.title('Greedy Multi-Type Routes')
plt.xlabel('X (km)')
plt.ylabel('Y (km)')
plt.legend(loc='best')
plt.grid(True)
plt.show()

# 4. Visualization: load/dist/cost statistics
fig, axs = plt.subplots(3,1, figsize=(6,9))
axs[0].bar(range(1,5), [sum(l) for l in all_loads])
axs[0].set_title('Total Load by Type (t)')
axs[1].bar(range(1,5), [sum(d) for d in all_dists])
axs[1].set_title('Total Distance by Type (km)')
axs[2].bar(range(1,5), all_costs)
axs[2].set_title('Total Cost by Type')
for ax in axs: ax.set_xlabel('Type')
plt.tight_layout()
plt.show()

plt.figure(figsize=(6,6))
plt.pie(all_costs, labels=[f'Type {i}' for i in range(1,5)], autopct='%1.1f%%')
plt.title('Cost Share by Type')
plt.show()
