%% 简单聚类算法
function clusters = simple_clustering(points, demands, capacity)
    clusters = {};
    remaining_points = points;
    remaining_demands = demands;
    
    while ~isempty(remaining_points)
        current_cluster = [];
        current_load = 0;
        
        % 贪心添加点到当前聚类
        i = 1;
        while i <= length(remaining_points) && current_load + remaining_demands(i) <= capacity
            current_cluster = [current_cluster, remaining_points(i)];
            current_load = current_load + remaining_demands(i);
            remaining_points(i) = [];
            remaining_demands(i) = [];
        end
        
        if isempty(current_cluster) && ~isempty(remaining_points)
            % 强制添加一个点（即使超载）
            current_cluster = [remaining_points(1)];
            remaining_points(1) = [];
            remaining_demands(1) = [];
        end
        
        if ~isempty(current_cluster)
            clusters{end+1} = current_cluster;
        end
    end
end
