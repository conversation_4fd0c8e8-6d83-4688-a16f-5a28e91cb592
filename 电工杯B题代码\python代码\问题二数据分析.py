import numpy as np
import matplotlib.pyplot as plt
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
# --- 1. 数据输入 ---
data = np.array([
    [12,  8,   0.72, 0.12, 0.06, 0.30],
    [5,   15,  1.38, 0.23, 0.05, 0.64],
    [20,  30,  1.08, 0.18, 0.04, 0.50],
    [25,  10,  1.55, 0.31, 0.06, 1.18],
    [35,  22,  1.62, 0.27, 0.05, 0.76],
    [18,   5,  1.76, 0.384,0.096,0.96],
    [30,  35,  0.77, 0.168,0.042,0.42],
    [10,  25,  1.02, 0.238,0.068,0.374],
    [22,  18,  1.32, 0.176,0.044,0.66],
    [38,  15,  1.45, 0.30, 0.075,0.675],
    [5,    8,  1.35, 0.27, 0.108,0.972],
    [15,  32,  1.87, 0.51, 0.068,0.952],
    [28,   5,  2.58, 0.516,0.129,1.075],
    [30,  12,  1.134,0.21, 0.063,0.693],
    [10,  10,  0.78, 0.13, 0.065,0.325],
    [20,  20,  0.768,0.192,0.080,0.56],
    [35,  30,  0.72, 0.27, 0.090,0.72],
    [8,   22,  1.595,0.348,0.087,0.87],
    [25,  25,  1.50, 0.36, 0.090,1.05],
    [32,   8,  1.08, 0.18, 0.090,0.45],
    [15,   5,  0.912,0.19, 0.038,0.76],
    [28,  20,  0.90, 0.195,0.075,0.33],
    [38,  25,  0.99, 0.27, 0.072,0.468],
    [10,  30,  1.44, 0.24, 0.048,0.672],
    [20,  10,  1.74, 0.319,0.116,0.725],
    [30,  18,  1.17, 0.39, 0.130,0.91],
    [5,   25,  1.70, 0.34, 0.170,1.19],
    [18,  30,  2.64, 0.66, 0.044,1.056],
    [35,  10,  0.864,0.216,0.072,0.648],
    [22,  35,  0.986,0.204,0.085,0.425]
])#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
coords = data[:, :2]
w_all = data[:, 2:]
types = ['厨余','可回收','有害','其他']
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用SimHei字体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
# --- 2. 车辆参数 ---
Q = np.array([8,6,3,10])
V = np.array([20,25,10,18])
C = np.array([2.5,2.0,5.0,1.8])

# --- 3. 可视化一：空间分布气泡图 ---
plt.figure(figsize=(8,6))
for k in range(4):
    # 气泡大小归一到 [50,300]
    sizes = 50 + 250 * (w_all[:,k] / w_all[:,k].max())
    plt.scatter(coords[:,0], coords[:,1], s=sizes, alpha=0.6, label=types[k])
plt.title('各收集点四类垃圾量气泡图')
plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.legend()
plt.grid(True)

# --- 4. 可视化二：类型累计产量柱状图 ---
total_w = w_all.sum(axis=0)
plt.figure(figsize=(6,4))#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
plt.bar(types, total_w, color='skyblue')
plt.title('各类垃圾累计产量对比')
plt.ylabel('总产量 (吨)')
plt.grid(axis='y')

# --- 5. 可视化三：车辆参数雷达图 ---
# 归一化参数
P = np.vstack([Q, V, C]).T  # shape (4,3)
P_norm = P / P.max(axis=0)
angles = np.linspace(0, 2*np.pi, 3, endpoint=False).tolist()
angles += angles[:1]
labels = ['载重 Q','容积 V','距离成本 C']
plt.figure(figsize=(6,6))
ax = plt.subplot(111, polar=True)
for i in range(4):
    vals = P_norm[i].tolist()
    vals += vals[:1]
    ax.plot(angles, vals, '-o', label=types[i], linewidth=1.5)
ax.set_xticks(angles[:-1])
ax.set_xticklabels(labels)
ax.set_title('车辆参数雷达图（归一化）', y=1.08)
ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1))

# --- 6. 可视化四：成本与载重双轴图 ---
fig, ax1 = plt.subplots(figsize=(6,4))
ax1.bar(types, Q, color='lightgreen', width=0.5)
ax1.set_ylabel('载重上限 Q_k (吨)')
ax1.set_xlabel('车辆类型')
ax1.grid(axis='y')
ax2 = ax1.twinx()
ax2.plot(types, C, '-s', color='salmon', linewidth=2)
ax2.set_ylabel('单位距离成本 C_k (元/km)')
plt.title('各类型车辆载重与运输成本')
plt.show()#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
