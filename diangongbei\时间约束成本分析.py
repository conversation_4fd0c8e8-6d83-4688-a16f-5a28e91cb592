"""
时间约束成本分析

本文件分析不同时间约束对各类垃圾运输成本的影响，并创建可视化图表展示时间约束越短成本增加越显著的现象。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import time
import matplotlib

# 设置中文字体，解决中文显示为白框的问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 解决保存图像时负号'-'显示为方块的问题

# 创建目录函数
def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
    return directory

# 读取数据
try:
    # 尝试读取附件3中的4类垃圾数据
    garbage_data = pd.read_excel('B题/附件3.xlsx', skiprows=1)
    if len(garbage_data.columns) == 5:
        garbage_data.columns = ['收集点编号', '厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']
    else:
        print(f"警告: 附件3.xlsx 的列数为 {len(garbage_data.columns)}，与预期不符")
        print("列名:", garbage_data.columns.tolist())

    # 读取附件2中的车辆参数
    vehicle_params = pd.read_excel('B题/附件2.xlsx', skiprows=1)
    if len(vehicle_params.columns) == 7:
        vehicle_params.columns = ['车辆类型k', '垃圾类型', '载重', '容积', '距离成本', '碳排放系数1', '碳排放系数2']
    else:
        print(f"警告: 附件2.xlsx 的列数为 {len(vehicle_params.columns)}，与预期不符")
        print("列名:", vehicle_params.columns.tolist())

    # 读取附件1中的收集点坐标
    collection_points = pd.read_excel('B题/附件1.xlsx', skiprows=1)
    print(f"附件1.xlsx 的列数为 {len(collection_points.columns)}")
    print("列名:", collection_points.columns.tolist())

    # 根据实际列数调整列名
    if len(collection_points.columns) == 3:
        collection_points.columns = ['收集点编号', 'X坐标', 'Y坐标']
    elif len(collection_points.columns) == 5:  # 假设有5列，可能包含额外信息
        collection_points = collection_points.iloc[:, :3]  # 只取前3列
        collection_points.columns = ['收集点编号', 'X坐标', 'Y坐标']
    else:
        print("无法处理附件1.xlsx的列结构，将使用模拟数据")
        raise ValueError("列结构不匹配")
except FileNotFoundError:
    print("无法找到Excel文件，尝试使用模拟数据...")

    # 创建模拟数据
    # 模拟垃圾数据
    garbage_data = pd.DataFrame({
        '收集点编号': range(1, 31),
        '厨余垃圾': np.random.uniform(0.5, 2.0, 30),
        '可回收物': np.random.uniform(0.3, 1.5, 30),
        '有害垃圾': np.random.uniform(0.1, 0.5, 30),
        '其他垃圾': np.random.uniform(0.4, 1.8, 30)
    })

    # 模拟车辆参数
    vehicle_params = pd.DataFrame({
        '车辆类型k': [1, 2, 3, 4],
        '垃圾类型': ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾'],
        '载重': [5.0, 3.0, 1.0, 4.0],
        '容积': [10.0, 8.0, 2.0, 8.0],
        '距离成本': [1.5, 1.2, 2.0, 1.3],
        '碳排放系数1': [0.2, 0.15, 0.25, 0.18],
        '碳排放系数2': [0.1, 0.08, 0.12, 0.09]
    })

    # 模拟收集点坐标
    collection_points = pd.DataFrame({
        '收集点编号': range(1, 31),
        'X坐标': np.random.uniform(-10, 10, 30),
        'Y坐标': np.random.uniform(-10, 10, 30)
    })

# 垃圾类型映射
garbage_types = {1: '厨余垃圾', 2: '可回收物', 3: '有害垃圾', 4: '其他垃圾'}

# 提取车辆参数
Q = {k: vehicle_params.iloc[k-1]['载重'] for k in range(1, 5)}  # 载重限制
V = {k: vehicle_params.iloc[k-1]['容积'] for k in range(1, 5)}  # 容积限制
C = {k: vehicle_params.iloc[k-1]['距离成本'] for k in range(1, 5)}  # 单位距离运输成本
E1 = {k: vehicle_params.iloc[k-1]['碳排放系数1'] for k in range(1, 5)}  # 碳排放系数1
E2 = {k: vehicle_params.iloc[k-1]['碳排放系数2'] for k in range(1, 5)}  # 碳排放系数2

# 提取收集点坐标
points = collection_points[['X坐标', 'Y坐标']].values

# 提取垃圾重量和体积
garbage_weights = {}
garbage_volumes = {}

for k in range(1, 5):
    garbage_weights[k] = garbage_data[garbage_types[k]].values
    # 假设垃圾密度为0.5，即体积 = 重量 / 0.5
    garbage_volumes[k] = garbage_weights[k] / 0.5

# 垃圾处理厂坐标
depot = np.array([0, 0])

# 计算距离矩阵
all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 计算路径时间
def calculate_route_time(route, speed=40):
    """计算路径的总时间（行驶时间 + 服务时间）

    参数:
    route -- 路径，不包括起点和终点的垃圾处理厂
    speed -- 车辆平均速度，默认40 km/h

    返回:
    total_time -- 总时间（小时）
    """
    time = 0
    prev = 0  # 从垃圾处理厂出发
    service_time = 0.2  # 每个点的服务时间 h

    for node in route:
        # 行驶时间
        time += dist_matrix[prev, node] / speed
        # 服务时间
        time += service_time
        prev = node

    # 返回垃圾处理厂
    time += dist_matrix[prev, 0] / speed

    return time

# 计算路径成本
def calculate_route_cost(route, garbage_type):
    """计算路径的运输成本

    参数:
    route -- 路径，不包括起点和终点的垃圾处理厂
    garbage_type -- 垃圾类型（1-4）

    返回:
    cost -- 路径成本
    """
    cost = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        cost += dist_matrix[prev, node] * C[garbage_type]
        prev = node
    # 返回垃圾处理厂
    cost += dist_matrix[prev, 0] * C[garbage_type]
    return cost

# 节约算法
def savings_algorithm(garbage_type):
    """使用节约算法计算路径

    参数:
    garbage_type -- 垃圾类型（1-4）

    返回:
    routes -- 路径列表
    route_weights -- 每条路径的总重量
    route_volumes -- 每条路径的总体积
    """
    n = len(garbage_weights[garbage_type])

    # 初始化路径（每个点单独一条路径）
    routes = [[i+1] for i in range(n) if garbage_weights[garbage_type][i] > 0]
    route_weights = [garbage_weights[garbage_type][i] for i in range(n) if garbage_weights[garbage_type][i] > 0]
    route_volumes = [garbage_volumes[garbage_type][i] for i in range(n) if garbage_weights[garbage_type][i] > 0]

    # 计算节约值
    savings = []
    for i in range(len(routes)):
        for j in range(i+1, len(routes)):
            # 计算合并路径i和路径j的节约值
            i_last = routes[i][-1]
            j_first = routes[j][0]
            saving = dist_matrix[i_last, 0] + dist_matrix[0, j_first] - dist_matrix[i_last, j_first]
            savings.append((i, j, saving))

    # 按节约值降序排序
    savings.sort(key=lambda x: x[2], reverse=True)

    # 合并路径
    for i, j, _ in savings:
        if i < len(routes) and j < len(routes):  # 确保路径i和j仍然存在
            route_i = routes[i]
            route_j = routes[j]

            # 检查合并后是否超过载重和容积限制
            merged_weight = route_weights[i] + route_weights[j]
            merged_volume = route_volumes[i] + route_volumes[j]

            if merged_weight <= Q[garbage_type] and merged_volume <= V[garbage_type]:
                # 合并路径
                routes[i] = route_i + route_j
                route_weights[i] = merged_weight
                route_volumes[i] = merged_volume
                # 删除路径j
                del routes[j], route_weights[j], route_volumes[j]

                # 更新索引
                for k in range(len(savings)):
                    if savings[k][0] == j:
                        savings[k] = (i, savings[k][1], savings[k][2])
                    elif savings[k][1] == j:
                        savings[k] = (savings[k][0], i, savings[k][2])

    return routes, route_weights, route_volumes

# 考虑时间约束的路径拆分
def split_routes_by_time(routes, route_weights, route_volumes, garbage_type, max_time):
    """根据时间约束拆分路径

    参数:
    routes -- 路径列表
    route_weights -- 每条路径的总重量
    route_volumes -- 每条路径的总体积
    garbage_type -- 垃圾类型（1-4）
    max_time -- 最大时间约束（小时）

    返回:
    new_routes -- 拆分后的路径列表
    new_weights -- 拆分后每条路径的总重量
    new_volumes -- 拆分后每条路径的总体积
    """
    new_routes = []
    new_weights = []
    new_volumes = []

    for route, weight, volume in zip(routes, route_weights, route_volumes):
        route_time = calculate_route_time(route)

        if route_time <= max_time:
            # 如果路径时间不超过限制，直接添加
            new_routes.append(route)
            new_weights.append(weight)
            new_volumes.append(volume)
        else:
            # 如果超过时间限制，需要拆分路径
            current_route = []
            current_weight = 0
            current_volume = 0

            for node in route:
                # 计算添加当前节点后的时间
                temp_route = current_route + [node]
                temp_time = calculate_route_time(temp_route)

                if temp_time <= max_time:
                    # 可以添加当前节点
                    current_route.append(node)
                    current_weight += garbage_weights[garbage_type][node-1]
                    current_volume += garbage_volumes[garbage_type][node-1]
                else:
                    # 需要开始新的路径
                    if current_route:
                        new_routes.append(current_route)
                        new_weights.append(current_weight)
                        new_volumes.append(current_volume)

                    # 开始新路径
                    current_route = [node]
                    current_weight = garbage_weights[garbage_type][node-1]
                    current_volume = garbage_volumes[garbage_type][node-1]

            # 添加最后一条路径
            if current_route:
                new_routes.append(current_route)
                new_weights.append(current_weight)
                new_volumes.append(current_volume)

    return new_routes, new_weights, new_volumes

# 可视化路径
def visualize_routes(routes_by_type, title, save_path):
    """可视化不同垃圾类型的路径

    参数:
    routes_by_type -- 字典，键为垃圾类型，值为该类型的路径列表
    title -- 图表标题
    save_path -- 保存路径
    """
    plt.figure(figsize=(14, 12))

    # 绘制收集点
    plt.scatter(points[:, 0], points[:, 1], c='blue', s=50, label='收集点')

    # 绘制垃圾处理厂
    plt.scatter([0], [0], c='red', s=200, marker='*', label='垃圾处理厂')

    # 为每个收集点添加编号
    for i in range(len(points)):
        plt.text(points[i, 0]+0.2, points[i, 1]+0.2, f'{i+1}', fontsize=10, color='black')

    # 设置颜色和标记
    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f']
    markers = ['o', 's', '^', 'd']

    # 绘制路径
    for k in range(1, 5):
        if k in routes_by_type and routes_by_type[k]:
            routes = routes_by_type[k]
            for i, route in enumerate(routes):
                # 添加处理厂作为起点和终点
                route_with_depot = [0] + route + [0]

                # 提取坐标
                route_x = [all_points[j, 0] for j in route_with_depot]
                route_y = [all_points[j, 1] for j in route_with_depot]

                # 绘制路径
                plt.plot(route_x, route_y, c=colors[k-1], marker=markers[k-1],
                        label=f'{garbage_types[k]}路径{i+1}' if i == 0 else "")

                # 添加路径箭头
                for j in range(len(route_with_depot)-1):
                    dx = route_x[j+1] - route_x[j]
                    dy = route_y[j+1] - route_y[j]
                    plt.arrow(route_x[j], route_y[j], dx*0.9, dy*0.9,
                            head_width=0.3, head_length=0.5, fc=colors[k-1], ec=colors[k-1])

    # 设置图表标题和标签
    plt.title(title, fontsize=16)
    plt.xlabel('X坐标', fontsize=14)
    plt.ylabel('Y坐标', fontsize=14)
    plt.grid(True)

    # 添加图例
    plt.legend(loc='best', fontsize=12)

    # 保存图表
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

# 分析不同时间约束对成本的影响
def analyze_time_constraints_impact():
    """分析不同时间约束对各类垃圾运输成本的影响"""
    print("分析不同时间约束对各类垃圾运输成本的影响...")

    # 创建主目录
    main_dir = ensure_dir("时间约束成本分析")

    # 不同时间约束
    time_constraints = [6, 7, 8, 9, 10]

    # 存储不同时间约束下的成本数据
    constraint_costs = {constraint: {} for constraint in time_constraints}
    constraint_vehicle_counts = {constraint: {} for constraint in time_constraints}
    constraint_routes = {constraint: {} for constraint in time_constraints}

    # 为每个时间约束创建一个子目录
    constraint_dirs = {}
    for constraint in time_constraints:
        constraint_dirs[constraint] = ensure_dir(os.path.join(main_dir, f"{constraint}小时"))

    # 对每个时间约束进行分析
    for constraint in time_constraints:
        print(f"\n分析时间约束: {constraint}小时")
        total_cost = 0
        total_vehicles = 0

        # 对每类垃圾进行分析
        for k in range(1, 5):
            # 使用节约算法计算初始路径
            routes, weights, volumes = savings_algorithm(k)

            # 应用时间约束拆分路径
            constrained_routes, constrained_weights, constrained_volumes = split_routes_by_time(
                routes, weights, volumes, k, constraint)

            # 计算成本
            type_cost = sum(calculate_route_cost(route, k) for route in constrained_routes)
            vehicle_count = len(constrained_routes)

            # 存储结果
            constraint_costs[constraint][k] = type_cost
            constraint_vehicle_counts[constraint][k] = vehicle_count
            constraint_routes[constraint][k] = constrained_routes

            total_cost += type_cost
            total_vehicles += vehicle_count

            print(f"  {garbage_types[k]}: {vehicle_count}辆车, 成本: {type_cost:.2f}")

        print(f"  总成本: {total_cost:.2f}, 总车辆数: {total_vehicles}")

        # 创建该时间约束下的成本分析文件
        with open(os.path.join(constraint_dirs[constraint], "成本分析.txt"), "w", encoding="utf-8") as f:
            f.write(f"时间约束: {constraint}小时\n\n")
            f.write("各类垃圾成本:\n")
            for k in range(1, 5):
                cost = constraint_costs[constraint][k]
                vehicles = constraint_vehicle_counts[constraint][k]
                f.write(f"- {garbage_types[k]}: {cost:.2f}元 ({vehicles}辆车)\n")
            f.write(f"\n总成本: {total_cost:.2f}元\n")
            f.write(f"总车辆数: {total_vehicles}辆\n")

        # 为每个时间约束生成路径图
        visualize_routes(
            constraint_routes[constraint],
            f"时间约束{constraint}小时下的多车辆协同路径",
            os.path.join(constraint_dirs[constraint], f"路径规划_{constraint}小时.png")
        )

    # 返回分析结果
    return constraint_costs, constraint_vehicle_counts, constraint_routes, constraint_dirs, main_dir

# 创建不同时间约束下各类垃圾的成本对比图
def create_cost_comparison_chart(constraint_costs, main_dir):
    """创建不同时间约束下各类垃圾的成本对比图"""
    fig, ax = plt.subplots(figsize=(14, 10))

    # 准备数据
    time_constraints = sorted(constraint_costs.keys())
    waste_types = [garbage_types[k] for k in range(1, 5)]

    # 设置柱状图的宽度和位置
    bar_width = 0.15
    r = np.arange(len(waste_types))

    # 设置颜色
    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f', '#9b59b6']

    # 绘制柱状图
    for i, constraint in enumerate(time_constraints):
        costs = [constraint_costs[constraint][k] for k in range(1, 5)]
        bars = ax.bar(r + i * bar_width, costs, width=bar_width, color=colors[i],
                     label=f'{constraint}小时', edgecolor='black', linewidth=1)

        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.0f}',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3),
                       textcoords="offset points",
                       ha='center', va='bottom', fontsize=9)

    # 设置图表标题和标签
    ax.set_title('不同时间约束下各类垃圾的运输成本对比', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('运输成本 (元)', fontsize=14, fontweight='bold')
    ax.set_xticks(r + bar_width * 2)
    ax.set_xticklabels(waste_types, fontsize=12)

    # 添加图例
    ax.legend(title='时间约束', fontsize=12, title_fontsize=14,
             frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(left=False, bottom=False)
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(main_dir, '不同时间约束下各类垃圾的运输成本对比.png'), dpi=300, bbox_inches='tight')
    plt.close()

# 创建不同时间约束下的成本增长率图
def create_cost_growth_chart(constraint_costs, main_dir):
    """创建不同时间约束下的成本增长率图"""
    fig, ax = plt.subplots(figsize=(12, 8))

    # 准备数据
    time_constraints = sorted(constraint_costs.keys())
    waste_types = [garbage_types[k] for k in range(1, 5)]

    # 计算基准成本（10小时约束下的成本）
    base_costs = {k: constraint_costs[10][k] for k in range(1, 5)}

    # 计算成本增长率
    growth_rates = {}
    for constraint in time_constraints:
        growth_rates[constraint] = {}
        for k in range(1, 5):
            # 避免除以零
            if base_costs[k] > 0:
                growth_rates[constraint][k] = (constraint_costs[constraint][k] - base_costs[k]) / base_costs[k] * 100
            else:
                # 如果基准成本为零，则增长率也设为零
                growth_rates[constraint][k] = 0.0

    # 绘制折线图
    markers = ['o', 's', '^', 'd']
    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f']

    for k in range(1, 5):
        rates = [growth_rates[constraint][k] for constraint in time_constraints]
        ax.plot(time_constraints, rates, marker=markers[k-1], markersize=10,
               linewidth=2, color=colors[k-1], label=garbage_types[k])

        # 添加数据标签
        for i, rate in enumerate(rates):
            ax.annotate(f'{rate:.1f}%',
                       xy=(time_constraints[i], rate),
                       xytext=(0, 10),
                       textcoords="offset points",
                       ha='center', va='bottom', fontsize=10)

    # 设置图表标题和标签
    ax.set_title('不同时间约束下各类垃圾的成本增长率', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('时间约束（小时）', fontsize=14, fontweight='bold')
    ax.set_ylabel('成本增长率（%）', fontsize=14, fontweight='bold')
    ax.set_xticks(time_constraints)

    # 添加图例
    ax.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine()
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(main_dir, '不同时间约束下各类垃圾的成本增长率.png'), dpi=300, bbox_inches='tight')
    plt.close()

# 创建不同时间约束下的车辆数量对比图
def create_vehicle_count_chart(constraint_vehicle_counts, main_dir):
    """创建不同时间约束下的车辆数量对比图"""
    fig, ax = plt.subplots(figsize=(14, 10))

    # 准备数据
    time_constraints = sorted(constraint_vehicle_counts.keys())
    waste_types = [garbage_types[k] for k in range(1, 5)]

    # 设置柱状图的宽度和位置
    bar_width = 0.15
    r = np.arange(len(waste_types))

    # 设置颜色
    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f', '#9b59b6']

    # 绘制柱状图
    for i, constraint in enumerate(time_constraints):
        counts = [constraint_vehicle_counts[constraint][k] for k in range(1, 5)]
        bars = ax.bar(r + i * bar_width, counts, width=bar_width, color=colors[i],
                     label=f'{constraint}小时', edgecolor='black', linewidth=1)

        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{int(height)}',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3),
                       textcoords="offset points",
                       ha='center', va='bottom', fontsize=10)

    # 设置图表标题和标签
    ax.set_title('不同时间约束下各类垃圾的车辆数量对比', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('车辆数量', fontsize=14, fontweight='bold')
    ax.set_xticks(r + bar_width * 2)
    ax.set_xticklabels(waste_types, fontsize=12)

    # 添加图例
    ax.legend(title='时间约束', fontsize=12, title_fontsize=14,
             frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(left=False, bottom=False)
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(main_dir, '不同时间约束下各类垃圾的车辆数量对比.png'), dpi=300, bbox_inches='tight')
    plt.close()

# 为每个垃圾类型创建单独的成本-时间约束关系图
def create_individual_waste_type_charts(constraint_costs, constraint_dirs):
    """为每个垃圾类型创建单独的成本-时间约束关系图"""
    # 准备数据
    time_constraints = sorted(constraint_costs.keys())

    # 为每种垃圾类型创建单独的图表
    for k in range(1, 5):
        fig, ax = plt.subplots(figsize=(10, 8))

        # 提取该类垃圾在不同时间约束下的成本
        costs = [constraint_costs[constraint][k] for constraint in time_constraints]

        # 绘制折线图
        ax.plot(time_constraints, costs, marker='o', markersize=10,
               linewidth=2, color='#3498db')

        # 添加数据标签
        for i, cost in enumerate(costs):
            ax.annotate(f'{cost:.2f}',
                       xy=(time_constraints[i], cost),
                       xytext=(0, 10),
                       textcoords="offset points",
                       ha='center', va='bottom', fontsize=12)

        # 设置图表标题和标签
        ax.set_title(f'{garbage_types[k]}的时间约束-成本关系', fontsize=18, fontweight='bold', pad=20)
        ax.set_xlabel('时间约束（小时）', fontsize=14, fontweight='bold')
        ax.set_ylabel('运输成本（元）', fontsize=14, fontweight='bold')
        ax.set_xticks(time_constraints)

        # 添加网格线
        ax.grid(linestyle='--', alpha=0.7)

        # 美化图表
        sns.despine()
        plt.tight_layout()

        # 保存图表到每个时间约束的目录中
        for constraint in time_constraints:
            plt.savefig(os.path.join(constraint_dirs[constraint], f'{garbage_types[k]}_成本分析.png'),
                       dpi=300, bbox_inches='tight')

        plt.close()

# 创建总结报告
def create_summary_report(constraint_costs, constraint_vehicle_counts, main_dir):
    """创建总结报告，分析时间约束对成本的影响"""
    # 准备数据
    time_constraints = sorted(constraint_costs.keys())

    # 计算总成本和总车辆数
    total_costs = {}
    total_vehicles = {}
    for constraint in time_constraints:
        total_costs[constraint] = sum(constraint_costs[constraint][k] for k in range(1, 5))
        total_vehicles[constraint] = sum(constraint_vehicle_counts[constraint][k] for k in range(1, 5))

    # 计算成本增长率（相对于10小时约束）
    base_cost = total_costs[10]
    cost_growth_rates = {}
    for constraint in time_constraints:
        # 避免除以零
        if base_cost > 0:
            cost_growth_rates[constraint] = (total_costs[constraint] - base_cost) / base_cost * 100
        else:
            cost_growth_rates[constraint] = 0.0

    # 创建总结报告
    with open(os.path.join(main_dir, "时间约束影响总结报告.md"), "w", encoding="utf-8") as f:
        f.write("# 时间约束对垃圾运输成本的影响分析\n\n")

        f.write("## 1. 总体影响\n\n")
        f.write("不同时间约束下的总成本和车辆数量：\n\n")
        f.write("| 时间约束（小时） | 总成本（元） | 成本增长率 | 总车辆数 |\n")
        f.write("|:---------------:|:------------:|:----------:|:--------:|\n")

        for constraint in time_constraints:
            f.write(f"| {constraint} | {total_costs[constraint]:.2f} | {cost_growth_rates[constraint]:+.2f}% | {total_vehicles[constraint]} |\n")

        f.write("\n## 2. 各类垃圾的影响分析\n\n")

        for k in range(1, 5):
            f.write(f"### 2.{k} {garbage_types[k]}\n\n")

            # 计算该类垃圾的成本增长率
            base_cost_k = constraint_costs[10][k]
            growth_rates_k = {constraint: (constraint_costs[constraint][k] - base_cost_k) / base_cost_k * 100
                             for constraint in time_constraints}

            f.write("| 时间约束（小时） | 成本（元） | 成本增长率 | 车辆数 |\n")
            f.write("|:---------------:|:----------:|:----------:|:------:|\n")

            for constraint in time_constraints:
                f.write(f"| {constraint} | {constraint_costs[constraint][k]:.2f} | {growth_rates_k[constraint]:+.2f}% | {constraint_vehicle_counts[constraint][k]} |\n")

            f.write("\n")

        f.write("## 3. 结论\n\n")

        # 找出成本增长最显著的垃圾类型
        max_growth_by_type = {}
        for k in range(1, 5):
            base_cost_k = constraint_costs[10][k]
            max_growth = max((constraint_costs[constraint][k] - base_cost_k) / base_cost_k * 100
                            for constraint in time_constraints if constraint != 10)
            max_growth_by_type[k] = max_growth

        max_growth_type = max(max_growth_by_type.items(), key=lambda x: x[1])

        f.write("1. **时间约束越短，运输成本增加越显著**：从10小时减少到6小时，总成本增加了")
        f.write(f"{cost_growth_rates[6]:.2f}%。\n")

        f.write(f"2. **{garbage_types[max_growth_type[0]]}的成本增长最为显著**：在最严格的时间约束下，")
        f.write(f"成本增加了{max_growth_type[1]:.2f}%。\n")

        f.write("3. **时间约束导致路径拆分和车辆数量增加**：从10小时减少到6小时，车辆数量从")
        f.write(f"{total_vehicles[10]}辆增加到{total_vehicles[6]}辆。\n")

        # 分析各类垃圾对时间约束的敏感度
        sensitivity = {}
        for k in range(1, 5):
            base_cost_k = constraint_costs[10][k]
            # 计算6小时约束下的成本增长率
            growth_6h = (constraint_costs[6][k] - base_cost_k) / base_cost_k * 100
            sensitivity[k] = growth_6h

        # 按敏感度排序
        sorted_sensitivity = sorted(sensitivity.items(), key=lambda x: x[1], reverse=True)

        f.write("4. **各类垃圾对时间约束的敏感度排序**（从高到低）：\n")
        for i, (k, sens) in enumerate(sorted_sensitivity):
            f.write(f"   - {garbage_types[k]}：成本增长率 {sens:.2f}%\n")

        f.write("\n5. **建议**：\n")
        f.write("   - 在保证服务质量的前提下，可以考虑适当放宽时间约束，以降低运输成本。\n")
        f.write(f"   - 对于{garbage_types[max_growth_type[0]]}，应优先考虑优化路径规划，因为其对时间约束最为敏感。\n")
        f.write("   - 8小时的时间约束是一个较为合理的选择，在此约束下成本增长相对可控。\n")

    print(f"总结报告已保存到：{os.path.join(main_dir, '时间约束影响总结报告.md')}")

# 创建路径对比图
def create_route_comparison(constraint_routes, constraint_dirs, main_dir):
    """创建不同时间约束下的路径对比分析"""
    # 创建路径对比目录
    comparison_dir = ensure_dir(os.path.join(main_dir, "路径对比分析"))

    # 为每类垃圾创建路径变化分析
    for k in range(1, 5):
        # 创建该垃圾类型的路径变化说明文件
        with open(os.path.join(comparison_dir, f"{garbage_types[k]}路径变化分析.md"), "w", encoding="utf-8") as f:
            f.write(f"# {garbage_types[k]}在不同时间约束下的路径变化分析\n\n")

            # 记录不同时间约束下的路径数量
            f.write("## 1. 路径数量变化\n\n")
            f.write("| 时间约束（小时） | 路径数量 | 变化率 |\n")
            f.write("|:---------------:|:--------:|:------:|\n")

            # 以10小时约束为基准
            base_count = len(constraint_routes[10][k]) if k in constraint_routes[10] else 0

            for constraint in sorted(constraint_routes.keys()):
                count = len(constraint_routes[constraint][k]) if k in constraint_routes[constraint] else 0

                # 计算变化率
                if base_count > 0:
                    change_rate = (count - base_count) / base_count * 100
                    f.write(f"| {constraint} | {count} | {change_rate:+.1f}% |\n")
                else:
                    f.write(f"| {constraint} | {count} | - |\n")

            f.write("\n## 2. 路径详情\n\n")

            # 记录每个时间约束下的具体路径
            for constraint in sorted(constraint_routes.keys()):
                f.write(f"### 时间约束: {constraint}小时\n\n")

                if k in constraint_routes[constraint] and constraint_routes[constraint][k]:
                    for i, route in enumerate(constraint_routes[constraint][k]):
                        route_str = ' -> '.join(str(node) for node in route)
                        route_time = calculate_route_time(route)
                        route_cost = calculate_route_cost(route, k)

                        f.write(f"- 路径{i+1}: 0 -> {route_str} -> 0\n")
                        f.write(f"  - 时间: {route_time:.2f}小时\n")
                        f.write(f"  - 成本: {route_cost:.2f}元\n")
                else:
                    f.write("无路径\n")

                f.write("\n")

            # 添加结论
            f.write("## 3. 结论\n\n")

            # 计算最大变化
            max_constraint = None
            max_change = 0

            for constraint in sorted(constraint_routes.keys()):
                if constraint == 10:  # 跳过基准约束
                    continue

                count = len(constraint_routes[constraint][k]) if k in constraint_routes[constraint] else 0
                if base_count > 0:
                    change = count - base_count
                    if abs(change) > abs(max_change):
                        max_change = change
                        max_constraint = constraint

            if max_constraint is not None and base_count > 0:
                if max_change > 0:
                    f.write(f"1. 时间约束对{garbage_types[k]}的路径影响显著，当约束从10小时减少到{max_constraint}小时时，路径数量增加了{max_change}条。\n")
                    f.write(f"2. 这表明在较短的时间约束下，原有的长路径被拆分为多条短路径，导致需要更多的车辆。\n")
                elif max_change < 0:
                    f.write(f"1. 时间约束对{garbage_types[k]}的路径影响显著，当约束从10小时减少到{max_constraint}小时时，路径数量减少了{abs(max_change)}条。\n")
                    f.write(f"2. 这可能是因为在较短的时间约束下，某些收集点无法被服务，导致路径减少。\n")
                else:
                    f.write(f"1. 时间约束对{garbage_types[k]}的路径影响不明显，路径数量保持不变。\n")
            else:
                f.write(f"1. 无法分析{garbage_types[k]}的路径变化，可能是因为没有相关路径。\n")

    # 创建总体路径变化分析文件
    with open(os.path.join(comparison_dir, "总体路径变化分析.md"), "w", encoding="utf-8") as f:
        f.write("# 不同时间约束下的总体路径变化分析\n\n")

        # 记录总路径数量变化
        f.write("## 1. 总路径数量变化\n\n")
        f.write("| 时间约束（小时） | 总路径数量 | 变化率 |\n")
        f.write("|:---------------:|:----------:|:------:|\n")

        # 计算每个时间约束下的总路径数
        total_routes = {}
        for constraint in sorted(constraint_routes.keys()):
            total_routes[constraint] = sum(len(routes) for routes in constraint_routes[constraint].values())

        # 以10小时约束为基准
        base_total = total_routes[10]

        for constraint in sorted(constraint_routes.keys()):
            if base_total > 0:
                change_rate = (total_routes[constraint] - base_total) / base_total * 100
                f.write(f"| {constraint} | {total_routes[constraint]} | {change_rate:+.1f}% |\n")
            else:
                f.write(f"| {constraint} | {total_routes[constraint]} | - |\n")

        f.write("\n## 2. 结论\n\n")
        f.write("1. **时间约束对路径规划的影响**：\n")

        # 找出变化最大的约束
        max_constraint = None
        max_change = 0

        for constraint in sorted(constraint_routes.keys()):
            if constraint == 10:  # 跳过基准约束
                continue

            change = total_routes[constraint] - base_total
            if abs(change) > abs(max_change):
                max_change = change
                max_constraint = constraint

        if max_constraint is not None and base_total > 0:
            if max_change > 0:
                f.write(f"   - 当时间约束从10小时减少到{max_constraint}小时时，总路径数量增加了{max_change}条（{max_change/base_total*100:.1f}%）。\n")
                f.write("   - 这表明时间约束越短，需要的车辆数量越多，运输成本也越高。\n")
            elif max_change < 0:
                f.write(f"   - 当时间约束从10小时减少到{max_constraint}小时时，总路径数量减少了{abs(max_change)}条（{abs(max_change)/base_total*100:.1f}%）。\n")
                f.write("   - 这可能是因为在严格的时间约束下，某些收集点无法被服务。\n")
            else:
                f.write("   - 时间约束对总路径数量影响不明显，路径数量保持不变。\n")

        f.write("\n2. **各类垃圾的路径变化**：\n")

        # 分析每类垃圾的路径变化
        for k in range(1, 5):
            base_count = len(constraint_routes[10][k]) if k in constraint_routes[10] else 0
            max_constraint_k = None
            max_change_k = 0

            for constraint in sorted(constraint_routes.keys()):
                if constraint == 10:  # 跳过基准约束
                    continue

                count = len(constraint_routes[constraint][k]) if k in constraint_routes[constraint] else 0
                change = count - base_count
                if abs(change) > abs(max_change_k):
                    max_change_k = change
                    max_constraint_k = constraint

            if max_constraint_k is not None and base_count > 0:
                if max_change_k > 0:
                    f.write(f"   - {garbage_types[k]}：在{max_constraint_k}小时约束下，路径数量增加了{max_change_k}条（{max_change_k/base_count*100:.1f}%）。\n")
                elif max_change_k < 0:
                    f.write(f"   - {garbage_types[k]}：在{max_constraint_k}小时约束下，路径数量减少了{abs(max_change_k)}条（{abs(max_change_k)/base_count*100:.1f}%）。\n")
                else:
                    f.write(f"   - {garbage_types[k]}：路径数量保持不变。\n")
            else:
                f.write(f"   - {garbage_types[k]}：无法分析路径变化。\n")

        f.write("\n3. **建议**：\n")
        f.write("   - 在实际运营中，应根据服务质量要求和成本控制目标，选择合适的时间约束。\n")
        f.write("   - 8小时的时间约束可能是一个较为合理的选择，既能保证服务质量，又不会导致成本过高。\n")
        f.write("   - 对于路径变化较大的垃圾类型，可以考虑优先优化其路径规划。\n")

# 主函数
if __name__ == "__main__":
    # 分析不同时间约束的影响
    constraint_costs, constraint_vehicle_counts, constraint_routes, constraint_dirs, main_dir = analyze_time_constraints_impact()

    # 创建可视化图表
    print("\n创建可视化图表...")

    # 1. 不同时间约束下各类垃圾的成本对比图
    create_cost_comparison_chart(constraint_costs, main_dir)

    # 2. 不同时间约束下的成本增长率图
    create_cost_growth_chart(constraint_costs, main_dir)

    # 3. 不同时间约束下的车辆数量对比图
    create_vehicle_count_chart(constraint_vehicle_counts, main_dir)

    # 4. 为每个垃圾类型创建单独的成本-时间约束关系图
    create_individual_waste_type_charts(constraint_costs, constraint_dirs)

    # 5. 创建路径对比分析
    create_route_comparison(constraint_routes, constraint_dirs, main_dir)

    # 6. 创建总结报告
    create_summary_report(constraint_costs, constraint_vehicle_counts, main_dir)

    print(f"\n分析完成！结果已保存到 '{main_dir}' 目录及其子目录中。")
