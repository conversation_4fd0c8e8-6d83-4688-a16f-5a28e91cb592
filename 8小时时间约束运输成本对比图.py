import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
sns.set_style("whitegrid")
plt.figure(figsize=(14, 10))

# 数据准备
waste_types = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

# 无时间约束的成本数据（基于10小时约束，相当于无约束）
no_constraint_costs = [1116.45, 455.81, 896.52, 467.13]  # 元

# 8小时时间约束的成本数据
with_constraint_costs = [1116.45, 455.81, 1130.55, 467.13]  # 元

# 计算成本增加量和增长率
cost_increase = [with_constraint_costs[i] - no_constraint_costs[i] for i in range(4)]
growth_rate = [(cost_increase[i] / no_constraint_costs[i]) * 100 if no_constraint_costs[i] > 0 else 0 for i in range(4)]

# 创建子图布局
fig = plt.figure(figsize=(16, 12))

# 子图1：成本对比柱状图
ax1 = plt.subplot(2, 2, 1)
x = np.arange(len(waste_types))
width = 0.35

bars1 = ax1.bar(x - width/2, no_constraint_costs, width, label='无时间约束', 
                color='#3498db', alpha=0.8, edgecolor='black', linewidth=0.5)
bars2 = ax1.bar(x + width/2, with_constraint_costs, width, label='8小时时间约束', 
                color='#e74c3c', alpha=0.8, edgecolor='black', linewidth=0.5)

# 添加数值标签
def add_value_labels(bars):
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 10,
                f'{height:.0f}', ha='center', va='bottom', fontsize=10, fontweight='bold')

add_value_labels(bars1)
add_value_labels(bars2)

ax1.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
ax1.set_ylabel('运输成本 (元)', fontsize=14, fontweight='bold')
ax1.set_title('8小时时间约束下各类垃圾运输成本对比', fontsize=16, fontweight='bold', pad=20)
ax1.set_xticks(x)
ax1.set_xticklabels(waste_types, fontsize=12)
ax1.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')
ax1.grid(axis='y', linestyle='--', alpha=0.7)

# 子图2：成本增加量柱状图
ax2 = plt.subplot(2, 2, 2)
colors = ['#2ecc71' if inc == 0 else '#e74c3c' for inc in cost_increase]
bars3 = ax2.bar(waste_types, cost_increase, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)

# 添加数值标签
for i, bar in enumerate(bars3):
    height = bar.get_height()
    if height > 0:
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'+{height:.0f}', ha='center', va='bottom', fontsize=11, fontweight='bold')
    else:
        ax2.text(bar.get_x() + bar.get_width()/2., 10,
                '0', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax2.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
ax2.set_ylabel('成本增加量 (元)', fontsize=14, fontweight='bold')
ax2.set_title('时间约束导致的成本增加量', fontsize=16, fontweight='bold', pad=20)
ax2.grid(axis='y', linestyle='--', alpha=0.7)
ax2.tick_params(axis='x', labelsize=12)

# 子图3：成本增长率柱状图
ax3 = plt.subplot(2, 2, 3)
colors_rate = ['#2ecc71' if rate == 0 else '#e74c3c' for rate in growth_rate]
bars4 = ax3.bar(waste_types, growth_rate, color=colors_rate, alpha=0.8, edgecolor='black', linewidth=0.5)

# 添加数值标签
for i, bar in enumerate(bars4):
    height = bar.get_height()
    if height > 0:
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'+{height:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')
    else:
        ax3.text(bar.get_x() + bar.get_width()/2., 1,
                '0%', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax3.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
ax3.set_ylabel('成本增长率 (%)', fontsize=14, fontweight='bold')
ax3.set_title('时间约束导致的成本增长率', fontsize=16, fontweight='bold', pad=20)
ax3.grid(axis='y', linestyle='--', alpha=0.7)
ax3.tick_params(axis='x', labelsize=12)

# 子图4：总成本对比饼图
ax4 = plt.subplot(2, 2, 4)
total_no_constraint = sum(no_constraint_costs)
total_with_constraint = sum(with_constraint_costs)
total_increase = total_with_constraint - total_no_constraint

# 创建饼图数据
pie_data = [total_no_constraint, total_increase]
pie_labels = [f'基础成本\n{total_no_constraint:.0f}元', f'时间约束增加\n{total_increase:.0f}元']
colors_pie = ['#3498db', '#e74c3c']

wedges, texts, autotexts = ax4.pie(pie_data, labels=pie_labels, colors=colors_pie, 
                                   autopct='%1.1f%%', startangle=90, 
                                   textprops={'fontsize': 11, 'fontweight': 'bold'})

ax4.set_title(f'总运输成本构成分析\n总成本: {total_with_constraint:.0f}元', 
              fontsize=16, fontweight='bold', pad=20)

# 调整布局
plt.tight_layout(pad=3.0)

# 添加总体说明文本
fig.text(0.5, 0.02, 
         f'分析结果：8小时时间约束主要影响有害垃圾运输，导致成本增加{total_increase:.0f}元（+{(total_increase/total_no_constraint)*100:.1f}%）',
         ha='center', fontsize=12, fontweight='bold', 
         bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))

# 保存图片
plt.savefig('8小时时间约束运输成本对比分析.png', dpi=300, bbox_inches='tight')
plt.savefig('8小时时间约束运输成本对比分析.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 打印详细分析结果
print("="*60)
print("8小时时间约束运输成本对比分析")
print("="*60)
print(f"{'垃圾类型':<10} {'无约束成本':<12} {'有约束成本':<12} {'成本增加':<10} {'增长率':<10}")
print("-"*60)
for i, waste_type in enumerate(waste_types):
    print(f"{waste_type:<10} {no_constraint_costs[i]:<12.2f} {with_constraint_costs[i]:<12.2f} "
          f"{cost_increase[i]:<10.2f} {growth_rate[i]:<10.1f}%")
print("-"*60)
print(f"{'总计':<10} {total_no_constraint:<12.2f} {total_with_constraint:<12.2f} "
      f"{total_increase:<10.2f} {(total_increase/total_no_constraint)*100:<10.1f}%")
print("="*60)
print("\n关键发现：")
print("1. 有害垃圾受时间约束影响最大，成本增加234.03元（+26.1%）")
print("2. 厨余垃圾、可回收物和其他垃圾不受8小时时间约束影响")
print("3. 总体成本增加7.9%，主要由有害垃圾路径拆分导致")
print("4. 时间约束对不同垃圾类型的影响存在显著差异")
