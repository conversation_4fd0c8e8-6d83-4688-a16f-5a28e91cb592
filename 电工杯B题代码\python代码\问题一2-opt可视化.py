import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm

# Data
coords = np.array([
    [12,  8],
    [5,   15],
    [20,  30],
    [25,  10],
    [35,  22],
    [18,   5],
    [30,  35],
    [10,  25],
    [22,  18],
    [38,  15],
    [5,    8],
    [15,  32],
    [28,   5],
    [30,  12],
    [10,  10],
    [20,  20],
    [35,  30],
    [8,   22],
    [25,  25],
    [32,   8],
    [15,   5],
    [28,  20],
    [38,  25],
    [10,  30],
    [20,  10],
    [30,  18],
    [5,   25],
    [18,  30],
    [35,  10],
    [22,  35],
])
depot = np.array([0, 0])

routes = [
    [0, 1, 23, 0],
    [0, 2, 18, 27, 0],
    [0, 3, 28, 0],
    [0, 4, 0],
    [0, 5, 22, 0],
    [0, 6, 20, 0],
    [0, 7, 30, 0],
    [0, 8, 12, 24, 0],
    [0, 9, 16, 0],
    [0, 10, 25, 0],
    [0, 11, 17, 0],
    [0, 13, 0],
    [0, 14, 21, 0],
    [0, 15, 19, 0],
    [0, 26, 0],
    [0, 29, 0],
]

loads = np.array([4.70,4.60,4.10,3.10,4.90,4.90,4.80,4.60,4.90,5.00,5.00,3.20,4.20,4.70,3.60,3.70])
dists = np.array([90.97,53.16,73.04,53.85,83.03,65.98,95.44,72.54,59.54,81.90,92.73,56.89,64.68,70.71,69.97,72.80])

# 1) Route panorama
plt.figure(figsize=(8,6))
cmap = cm.autumn(np.linspace(0,1,len(routes)))
for k, rt in enumerate(routes):
    pts = np.vstack([depot if x==0 else coords[x-1] for x in rt])
    plt.plot(pts[:,0], pts[:,1], '-o', linewidth=1.8,
             color=cmap[k], markerfacecolor='white', markeredgecolor=cmap[k])
    mid = len(pts)//2
    plt.text(pts[mid,0], pts[mid,1], f'V{k+1}',
             fontsize=11, fontweight='bold', color=cmap[k])
plt.scatter(*depot, marker='s', s=100, color='black', label='Depot')
plt.text(depot[0], depot[1], '  Depot', fontsize=12, fontweight='bold')
plt.title('All Vehicle Routes')
plt.xlabel('X (km)'); plt.ylabel('Y (km)')
plt.grid(True); plt.axis('equal')
plt.show()

# 2) Load vs Distance Gantt-style bars
fig, ax1 = plt.subplots(figsize=(8,6))
y = np.arange(1, len(loads)+1)
ax1.barh(y, loads, height=0.4, color=[(0.2,0.6,1)]*len(loads))
ax1.set_xlabel('Load (t)')
ax1.set_ylabel('Vehicle')
ax1.set_yticks(y); ax1.set_yticklabels([f'V{k}' for k in y])
ax1.invert_yaxis()
ax2 = ax1.twiny()
ax2.barh(y+0.4, dists, height=0.4, color=[(1,0.5,0.2)]*len(dists))
ax2.set_xlabel('Distance (km)')
fig.suptitle('Vehicle Load and Distance Comparison')
fig.tight_layout()
plt.show()

# 3) Load-Distance scatter
plt.figure(figsize=(8,6))
sizes = loads * 50
sc = plt.scatter(loads, dists, s=sizes, c=dists, cmap='viridis', alpha=0.8)
plt.colorbar(sc, label='Distance (km)')
plt.xlabel('Load (t)'); plt.ylabel('Distance (km)')
plt.title('Load vs. Distance')
# linear fit
p = np.polyfit(loads, dists, 1)
xv = np.linspace(loads.min(), loads.max(), 100)
plt.plot(xv, np.polyval(p, xv), '--k', linewidth=1.2)
plt.text(loads.mean(), dists.mean(),
         f'y={p[0]:.2f}x+{p[1]:.1f}', backgroundcolor='white')
plt.grid(True)
plt.show()

# 4) Distance waterfall
order = np.argsort(-dists)
sd = dists[order]
labels = [f'V{idx+1}' for idx in order]
plt.figure(figsize=(8,6))
plt.bar(range(len(sd)), sd, color=(0.8,0.2,0.2))
plt.xticks(range(len(sd)), labels, rotation=45)
plt.ylabel('Distance (km)')
plt.title('Vehicle Distance Waterfall')
plt.tight_layout()
plt.show()

# 5) Cumulative distance curve
cumd = np.cumsum(sd)
plt.figure(figsize=(8,6))
plt.plot(range(len(cumd)), cumd, '-s', linewidth=1.6, markersize=8,
         markerfacecolor='white')
plt.xticks(range(len(cumd)), labels, rotation=45)
plt.xlabel('Vehicle (sorted by distance)')
plt.ylabel('Cumulative Distance (km)')
plt.title('Cumulative Distance Covered')
plt.grid(True)
plt.tight_layout()
plt.show()
