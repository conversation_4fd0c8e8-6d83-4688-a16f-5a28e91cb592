%% ==== 批量评估中转站组合最优方案入口 ====
clear; clc; close all; rng(1);

allStations = [31, 32, 33, 34, 35];
minCost = inf; bestCombo = [];

for r = 2:length(allStations)
    combos = nchoosek(allStations, r);
    for i = 1:size(combos,1)
        try
            [success, cost] = runPlan(combos(i,:));
            if success && cost < minCost
                minCost = cost;
                bestCombo = combos(i,:);
            end
        catch
            % 静默跳过失败组合
        end
    end
end

fprintf('\n>> 最优中转站组合: [%s], 最小日均总成本: %.2f 元\n', num2str(bestCombo), minCost);

% ============ 可视化最优方案 ============
runPlan(bestCombo, true);

%% ==== 函数：封装主程序为 runPlan =====
function [valid, totalCost] = runPlan(selectedStation, plotFlag)
    if nargin < 2, plotFlag = false; end

    speed = 40;
    carbonPrice = 0.50;
    planHorizon = 365*10 + 2;
    buildCostPerStation = [5.0e5; 6.0e5; 4.5e5; 5.5e5; 5.8e5];
    buildCostPerDay = buildCostPerStation /planHorizon;

    T1 = readtable('附件1.xlsx','VariableNamingRule','preserve');
    T1.Properties.VariableNames = {'id','x','y','w'};
    T1.w(1) = 0;
    coordsC = [T1.x, T1.y];
    n = height(T1) - 1;

    T2 = readtable('附件2.xlsx','HeaderLines',1,'VariableNamingRule','preserve');
    for k = 1:4
        veh(k).Q = T2{k,3};
        veh(k).V = T2{k,4};
        veh(k).C = T2{k,5};
    end

    T3 = readtable('附件3.xlsx','HeaderLines',1,'VariableNamingRule','preserve');
    demW = [zeros(1,4); T3{:,2:5}];

    cand.id  = [31; 32; 33; 34; 35];
    cand.x   = [15; 25; 35; 10; 20];
    cand.y   = [15; 25; 15; 25; 30];
    cand.S   = [20 15 5 30; 25 20 6 35; 18 12 4 25; 22 18 5 32; 24 22 7 38];

    assignMat = assignTransferStations(coordsC, demW, cand, selectedStation);
    labels = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"];
    totalDist = 0; totalCost = 0; vehicleID = 1; valid = true;

    % === 可视化每类垃圾的收集点→中转站分配图 === 群：925355789获取完整正版资料
    if plotFlag
        marker = {'o','s','^','d'};
        clr = lines(4);
        for k = 1:4
            figure('Name', sprintf('%s中转站分配图', labels(k)), 'NumberTitle', 'off'); hold on; axis equal; box on;
            title(sprintf('%s-中转站分配图', labels(k)));
            assignment = assignMat{k};
            for i = 1:n
                stn = assignment(i);
                if stn == 0, continue; end
                idxStn = find(cand.id == stn);
                plot([coordsC(i+1,1), cand.x(idxStn)], [coordsC(i+1,2), cand.y(idxStn)], '-', 'Color', clr(k,:), 'LineWidth', 2);
                plot(coordsC(i+1,1), coordsC(i+1,2), marker{k}, 'Color', clr(k,:), 'MarkerSize', 10, 'MarkerFaceColor', clr(k,:));
                 text(coordsC(i+1,1)+0.5, coordsC(i+1,2), sprintf('%d', i), 'FontSize', 15, 'Color', clr(k,:));
            end
            for i = 1:numel(selectedStation)
                idx = find(cand.id == selectedStation(i));
                text(cand.x(idx), cand.y(idx)+0.5, sprintf('中转站%d', cand.id(idx)), 'Color', 'red','FontSize', 15);
            end
            xlim([-1 43]);
            ylim([-1 38]);
            %
            % % 自动生成主刻度和次刻度
            xticks(0:5:40);
            yticks(0:5:35);

            set(gcf, 'Position', [100 100 1000 850]);
            set(gca, 'FontSize', 20);
            text(coordsC(1,1), coordsC(1,2)+1, '处理厂 0', 'Color', 'black','FontSize', 15);
            saveas(gcf, sprintf('%s_中转站分配图.png', labels(k)));
        end
    end

    for k = 1:4
        assignment = assignMat{k};
        demand = demW(:,k);
        Qk = veh(k).Q; Ck = veh(k).C;
        if plotFlag
            fig = figure('Name', labels(k), 'NumberTitle', 'off'); hold on; axis equal; box on;
            xlim([-1 43]);
            ylim([-1 38]);
            %
            % % 自动生成主刻度和次刻度
            xticks(0:5:40);
            yticks(0:5:35);

            set(gcf, 'Position', [100 100 1000 850]);
            set(gca, 'FontSize', 20);
            title(sprintf('%s 路径图（含中转站）', labels(k)));
            fprintf('\n>>> [%s] 路线方案：\n', labels(k));
        end
        for s = 1:numel(selectedStation)
            stn = selectedStation(s);
            group = find(assignment == stn);
            if isempty(group), continue; end
            coordsSub = [coordsC; [cand.x, cand.y]];
            D = squareform(pdist(coordsSub));
            loadList = demand(group+1);
            nodeList = group;
            remaining = true(size(nodeList));
            while any(remaining)
                curLoad = 0; selIdx = [];
                for i = 1:numel(nodeList)
                    if remaining(i) && curLoad + loadList(i) <= Qk
                        selIdx(end+1) = i;
                        curLoad = curLoad + loadList(i);
                    end
                end
                if isempty(selIdx), valid = false; return; end
                remaining(selIdx) = false;
                realRoute = [0, nodeList(selIdx)', stn, 0];
                idxRoute = arrayfun(@(x) getNodeIndex(x, n, selectedStation), realRoute);
                dNow = routeDist(idxRoute, D);
                totalDist = totalDist + dNow;
                totalCost = totalCost + dNow * Ck;

                if plotFlag
                    fprintf('车辆%02d: 载重 = %.1f 吨  距离 = %.2f km  路线: ', vehicleID, curLoad, dNow);
                    realNodeStr = strjoin(arrayfun(@(x) num2str(getRealNodeInline(x, n, selectedStation)), idxRoute, 'UniformOutput', false), '-');
                    fprintf('%s\n', realNodeStr);
                    X = coordsSub(idxRoute, 1);
                    Y = coordsSub(idxRoute, 2);
                    plot(X, Y, '-o', 'LineWidth', 2);
                    for i = 1:numel(realRoute)
                        labelColor = 'black';
                        if realRoute(i) == 0
                            txt = '处理厂 0';
                        elseif ismember(realRoute(i), selectedStation)
                            txt = sprintf('中转站 %d', realRoute(i));
                            labelColor = 'red';
                        else
                            txt = sprintf('%d', realRoute(i));
                        end
                        text(X(i)+0.2, Y(i)+0.2, txt, 'FontSize', 15, 'Color', labelColor);
                    end
                end
                vehicleID = vehicleID + 1;
            end
        end
        if plotFlag
            saveas(fig, sprintf('%s_路径图.png', labels(k)));
        end
    end
    buildCost = sum(buildCostPerDay(ismember([31,32,33,34,35], selectedStation)));
    totalCost = totalCost + buildCost;
end



function idx = getNodeIndex(realID, n, selectedStation)
    if realID == 0
        idx = 1;
    elseif realID <= n
        idx = realID + 1;
    elseif any(selectedStation == realID)
        idx = n + find(selectedStation == realID) + 1;
    else
        idx = -1;
    end
end

function realID = getRealNodeInline(idx, n, selectedStation)
    if idx == 1
        realID = 0;
    elseif idx <= n + 1
        realID = idx - 1;
    else
        realID = selectedStation(idx - n - 1);
    end
end

function assignMat = assignTransferStations(coordsC, demW, cand, selectedStation)
    selIdx = ismember(cand.id, selectedStation);
    selCoord = [cand.x(selIdx), cand.y(selIdx)];
    selCap = cand.S(selIdx, :);
    numStation = sum(selIdx);
    n = size(coordsC,1)-1;

    assignMat = cell(1,4);
    for k = 1:4
        demand_k = demW(2:end,k);
        stationLoad = zeros(numStation,1);
        assignment = zeros(n,1);
        for j = 1:n
            pt = coordsC(j+1,:);
            distList = vecnorm(selCoord - pt, 2, 2);
            [~, sortIdx] = sort(distList);
            for idx = sortIdx'
                if stationLoad(idx) + demand_k(j) <= selCap(idx,k)
                    stationLoad(idx) = stationLoad(idx) + demand_k(j);
                    assignment(j) = selectedStation(idx);
                    break;
                end
            end
            if assignment(j) == 0
                warning('收集点 %d 无法为第 %d 类垃圾分配中转站', j, k);
            end
        end
        assignMat{k} = assignment;
    end
end

function d = routeDist(rt, D)
    d = sum(D(sub2ind(size(D), rt(1:end-1), rt(2:end))));
end
