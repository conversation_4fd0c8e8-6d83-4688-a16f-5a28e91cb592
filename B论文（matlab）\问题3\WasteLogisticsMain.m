% 用于初步求解
    %% 主程序入口
    clear; clc; close all; rng(1);

    % 全局参数
    speed = 40;                  % 车辆速度 (km/h)
    carbonPrice = 0.50;          % 碳排放价格 (元/kgCO2e)
    planHorizon = 365*10 + 2;    % 规划周期 (天)
    maxOuter = 1;                % 外层迭代次数

    % 读取附件1: 收集点与处理厂
    T1 = readtable('附件1.xlsx','VariableNamingRule','preserve');
    T1.Properties.VariableNames = {'id','x','y','w'};
    T1.w(1) = 0;
    coordsC = [T1.x, T1.y];
    n = height(T1) - 1;

    % 读取附件2: 车辆参数
    tbl2 = readtable('附件2.xlsx','HeaderLines',1,'VariableNamingRule','preserve');
    for k = 1:4
        veh(k).Q     = tbl2{k,3};
        veh(k).V     = tbl2{k,4};
        veh(k).Cdist = tbl2{k,5};
        veh(k).alpha = tbl2{k,6};
        veh(k).beta  = tbl2{k,7};
    end

    % 读取附件3: 每日需求
    tbl3 = readtable('附件3.xlsx','HeaderLines',1,'VariableNamingRule','preserve');
    demW = [zeros(1,4); tbl3{:,2:5}];

    % 候选中转站信息
    cand.id  = [31; 32; 33; 34; 35];
    cand.x   = [15; 25; 35; 10; 20];
    cand.y   = [15; 25; 15; 25; 30];
    cand.T   = [50; 60; 45; 55; 58];
    cand.win = [6 18; 8 16; 10 14; 7 17; 9 15];
    cand.S   = [20 15 5 30; 25 20 6 35; 18 12 4 25; 22 18 5 32; 24 22 7 38];
    m = numel(cand.id);

    % 构建总距离矩阵
    coordsAll = [coordsC; cand.x, cand.y];
    D = squareform(pdist(coordsAll));

    % 初始粗估费用矩阵
    Crough = estimateCrough(D, demW, veh, n, m);

    % 记录最优方案
    bestCost = inf;
    bestY = [];
    bestX = [];
    bestDetail = [];

    % 外层迭代：选址与路径
    for outer = 1:maxOuter
        fprintf('\n=== 外层迭代 %d ===\n', outer);

        % 选址 MILP
        [y, xAssign] = siteSelectMILP(Crough, demW, cand, planHorizon);
        openIdx = find(y);
        fprintf('启用中转站: %s\n', num2str(cand.id(openIdx)'));

        % 路径与排放计算
        [costReal, emitReal, Cnew, detail] = routingEmission(openIdx, xAssign, D, demW, veh, cand, carbonPrice, speed);
        buildCost = sum(cand.T(openIdx) * 1e4 / planHorizon);
        totalCost = buildCost + costReal + carbonPrice * emitReal;
        fprintf('总成本: %.2f 元/日\n', totalCost);

        % 更新最优方案
        if totalCost < bestCost
            bestCost = totalCost;
            bestY = y;
            bestX = xAssign;
            bestDetail = detail;
        end

        % 收敛判断
        if outer > 1 && isequal(prevY, y)
            fprintf('方案收敛，终止迭代\n');
            break;
        end
        Crough = Cnew;
        prevY = y;
    end

    % 可视化最优分配 群：*********获取完整正版资料
    openIdxBest = find(bestY);
    fprintf('\n=== 最优方案：启用中转站 %s，成本 %.2f 元/日 ===\n', num2str(cand.id(openIdxBest)'), bestCost);
    visualizeAssignment(coordsAll, cand, bestX);
    disp(bestDetail);



function Crough = estimateCrough(D, demW, veh, n, m)
    avgC = mean([veh.Cdist]);
    Crough = zeros(n, m);
    for j = 1:m
        Crough(:, j) = 2 * D(2:n+1, n+1+j) * avgC;
    end
end

function [y, x] = siteSelectMILP(Crough, demW, cand, H)
    [n, m] = size(Crough);
    Nvar = m + n*m;
    buildCost = cand.T * 1e4 / H;
    f = [buildCost(:); Crough(:)];

    intcon = 1:Nvar; lb = zeros(Nvar,1); ub = ones(Nvar,1);
    Aeq = zeros(n, Nvar); beq = ones(n,1);
    for i = 1:n
        Aeq(i, m + (0:m-1)*n + i) = 1;
    end
    Ak = []; bk = [];
    for j = 1:m
        for k = 1:4
            row = zeros(1, Nvar);
            row(m + (j-1)*n + (1:n)) = demW(2:n+1, k)';
            row(j) = -cand.S(j,k);
            Ak = [Ak; row]; bk = [bk; 0];
        end
    end
    opts = optimoptions('intlinprog','Display','off');
    sol = intlinprog(f, intcon, Ak, bk, Aeq, beq, lb, ub, opts);

    y = sol(1:m) > 0.5;
    x = reshape(sol(m+1:end), n, m);
end

function [costTot, emitTot, Cnew, detailTbl] = routingEmission(openIdx, xAssign, D, demW, veh, cand, cP, speed)
    n = size(xAssign,1); m = numel(cand.id);
    costTot = 0; emitTot = 0; Cnew = zeros(n, m); rec = [];
    for idx = 1:numel(openIdx)
        j = openIdx(idx);
        cust = find(xAssign(:,j));
        Dsub = D([1; cust+1], [1; cust+1]);
        for k = 1:4
            W = demW(cust, k)';
            if ~any(W), continue; end
            [cost, dist, emit] = solveCVRPga(Dsub, W, veh(k), cand.win(j,:), cP, speed);
            costTot = costTot + cost;
            emitTot = emitTot + emit;
            Cnew(cust, j) = cost / numel(cust);
            rec = [rec; {cand.id(j), k, dist, emit, cost}];
        end
    end
    detailTbl = cell2table(rec, 'VariableNames', {'Station','Type','Dist','Emit','Cost'});
end

function [costTot, distTot, emitTot] = solveCVRPga(D, w, vehk, win, cP, speed)
    routes = savingsCVRP(D, w, vehk, win, speed);
    distTot = 0; emitTot = 0;
    for r = 1:numel(routes)
        rt = routes{r};
        d = sum(D(sub2ind(size(D), rt(1:end-1), rt(2:end))));
        loadAvg = mean(w(rt(2:end-1)-1));
        e = (vehk.alpha + vehk.beta * loadAvg) * d;
        distTot = distTot + d;
        emitTot = emitTot + e;
    end
    costTot = distTot * vehk.Cdist + cP * emitTot;
end

function visualizeAssignment(coordsAll, cand, xAssign)
    figure('Color','w'); hold on; axis equal; box on;
    scatter(coordsAll(2:size(xAssign,1)+1,1), coordsAll(2:size(xAssign,1)+1,2), 40, 'k', 'filled', 'DisplayName','客户');
    used = any(xAssign,1);
    scatter(cand.x(used), cand.y(used), 100, 'rs', 'filled', 'DisplayName','启用中转站');
    for i = 1:size(xAssign,1)
        j = find(xAssign(i,:),1);
        if isempty(j), continue; end
        plot([coordsAll(i+1,1), cand.x(j)], [coordsAll(i+1,2), cand.y(j)], 'b-','HandleVisibility', 'off');
    end
    legend('Location','bestoutside'); xlabel('X'); ylabel('Y'); title('最优分配可视化');
end
% (coordsAll, cand, xAssign);

    

%% 辅助函数 ==================================================================

function routes = savingsCVRP(D, w, vehk, win, speed)
    n = numel(w); depot = 1;
    routes = arrayfun(@(i){[depot, i+1, depot]}, 1:n);
    S = [];
    for i = 1:n-1
        for j = i+1:n
            S(end+1,:) = [i, j, D(depot,i+1)+D(depot,j+1)-D(i+1,j+1)];
        end
    end
    S = sortrows(S, -3);
    for s = 1:size(S,1)
        i = S(s,1)+1; j = S(s,2)+1;
        r1 = find(cellfun(@(r) any(r==i), routes));
        r2 = find(cellfun(@(r) any(r==j), routes));
        if r1~=r2
            merged = [routes{r1}(1:end-1), routes{r2}(2:end)];
            if sum(w(merged(2:end-1)-1)) <= vehk.Q
                routes{r1} = merged; routes(r2) = [];
            end
        end
    end
    for r = 1:numel(routes)
        routes{r} = twoOpt(routes{r}, D);
    end
end

function rt = twoOpt(rt, D)
    improved = true;
    while improved
        improved = false;
        for i = 2:length(rt)-2
            for j = i+1:length(rt)-1
                if D(rt(i-1),rt(j)) + D(rt(i),rt(j+1)) < D(rt(i-1),rt(i)) + D(rt(j),rt(j+1))
                    rt(i:j) = rt(j:-1:i);
                    improved = true;
                end
            end
        end
    end
end


