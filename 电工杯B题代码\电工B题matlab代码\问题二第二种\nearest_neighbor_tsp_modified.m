%% 修改后的最近邻算法
function route = nearest_neighbor_tsp_modified(points, dist_matrix)
    if isempty(points)
        route = [0, 0];
        return;
    end
    
    unvisited = points;
    route = [0];
    current = 1;
    
    while ~isempty(unvisited)
        min_dist = inf;
        next_point = -1;
        next_idx = -1;
        
        for i = 1:length(unvisited)
            point = unvisited(i);
            dist = dist_matrix(current, point + 1);
            if dist < min_dist
                min_dist = dist;
                next_point = point;
                next_idx = i;
            end
        end
        
        route = [route, next_point];
        current = next_point + 1;
        unvisited(next_idx) = [];
    end
    
    route = [route, 0];
end

