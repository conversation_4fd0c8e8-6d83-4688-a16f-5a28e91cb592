%%计算单个中转站方案入口 ====
clear; clc; close all; rng(1);

speed = 40;
carbonPrice = 0.50;
planHorizon = 365*10 + 2;
buildCostPerStation = [5.0e5; 6.0e5; 4.5e5; 5.5e5; 5.8e5];
buildCostPerDay = buildCostPerStation / (planHorizon);

T1 = readtable('附件1.xlsx','VariableNamingRule','preserve');
T1.Properties.VariableNames = {'id','x','y','w'};
T1.w(1) = 0;
coordsC = [T1.x, T1.y];
n = height(T1) - 1;

T2 = readtable('附件2.xlsx','HeaderLines',1,'VariableNamingRule','preserve');
for k = 1:4
    veh(k).Q = T2{k,3};
    veh(k).V = T2{k,4};
    veh(k).C = T2{k,5};
end

T3 = readtable('附件3.xlsx','HeaderLines',1,'VariableNamingRule','preserve');
demW = [zeros(1,4); T3{:,2:5}];

cand.id  = [31; 32; 33; 34; 35];
cand.x   = [15; 25; 35; 10; 20];
cand.y   = [15; 25; 15; 25; 30];
cand.S   = [20 15 5 30; 25 20 6 35; 18 12 4 25; 22 18 5 32; 24 22 7 38];

selectedStation = [31,33, 34,35];
assignMat = assignTransferStations(coordsC, demW, cand, selectedStation);
bestPlan.routes = cell(4,1);
bestPlan.transCost1 = 0; bestPlan.transCost2 = 0;
bestPlan.buildCost = sum(buildCostPerDay(ismember(cand.id, selectedStation)));
bestPlan.totalDist = 0;

labels = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"];
vehicleStats = zeros(4, 3);
stnStats = zeros(numel(selectedStation), 4);
vehicleID = 1;

for k = 1:4
    assignment = assignMat{k};
    demand = demW(:,k);
    Qk = veh(k).Q; Ck = veh(k).C;
    routesK = {}; distK = 0; costK = 0;
    fig = figure('Name', labels(k), 'NumberTitle', 'off'); hold on; axis equal; box on;
    title(sprintf('%s 路径图（含中转站）', labels(k)));

    for s = 1:numel(selectedStation)
        stn = selectedStation(s);
        stn_idx = find(cand.id == stn);
        group = find(assignment == stn);
        stnStats(s,k) = sum(demand(group+1));
        if isempty(group), continue; end

        coordsSub = [coordsC; [cand.x, cand.y]];
        D = squareform(pdist(coordsSub));
        loadList = demand(group+1);
        nodeList = group;
        remaining = true(size(nodeList));

        while any(remaining)
            curLoad = 0;
            selIdx = [];
            for i = 1:numel(nodeList)
                if remaining(i) && curLoad + loadList(i) <= Qk
                    selIdx(end+1) = i;
                    curLoad = curLoad + loadList(i);
                end
            end
            if isempty(selIdx), break; end
            remaining(selIdx) = false;
            realRoute = [0, nodeList(selIdx)', stn, 0];
            idxRoute = arrayfun(@(x) getNodeIndex(x, n, selectedStation), realRoute);

            dNow = routeDist(idxRoute, D);
            distK = distK + dNow;
            routesK{end+1} = idxRoute;
            fprintf('车辆%02d: 载重 = %.1f 吨  距离 = %.2f km  路线: ', vehicleID, curLoad, dNow);
            realNodeStr = strjoin(arrayfun(@(x) num2str(getRealNodeInline(x, n, selectedStation)), idxRoute, 'UniformOutput', false), '-');
            fprintf('%s\n', realNodeStr);
            vehicleID = vehicleID + 1;

            % 可视化当前路线
            X = coordsSub(idxRoute, 1);
            Y = coordsSub(idxRoute, 2);
            plot(X, Y, '-o', 'LineWidth', 2);
            for i = 1:numel(realRoute)
                labelColor = 'black';
                if realRoute(i) == 0
                    txt = '处理厂 0';
                elseif ismember(realRoute(i), selectedStation)
                    txt = sprintf('中转站 %d', realRoute(i));
                    labelColor = 'red';
                else
                    txt = sprintf('%d', realRoute(i));
                end
                text(X(i)+0.2, Y(i)+0.2, txt, 'FontSize', 10, 'Color', labelColor);
            end
        end
    end
    costK = distK * Ck;
    bestPlan.routes{k} = routesK;
    bestPlan.transCost1 = bestPlan.transCost1 + costK;
    bestPlan.totalDist = bestPlan.totalDist + distK;
    vehicleStats(k,:) = [numel(routesK), distK, costK];
    fprintf('车辆数 = %d\n', numel(routesK));

    saveas(fig, sprintf('%s_路径图.png', labels(k)));
end

bestPlan.totalCost = bestPlan.transCost1 + bestPlan.transCost2 + bestPlan.buildCost;
fprintf('\n>> 日均总成本 = %.2f 元\n', bestPlan.totalCost);

%% ==== 函数区 ====
function idx = getNodeIndex(realID, n, selectedStation)
    if realID == 0
        idx = 1;
    elseif realID <= n
        idx = realID + 1;
    elseif any(selectedStation == realID)
        idx = n + find(selectedStation == realID) + 1;
    else
        idx = -1;
    end
end

function realID = getRealNodeInline(idx, n, selectedStation)
    if idx == 1
        realID = 0;
    elseif idx <= n + 1
        realID = idx - 1;
    else
        realID = selectedStation(idx - n - 1);
    end
end

function assignMat = assignTransferStations(coordsC, demW, cand, selectedStation)
    selIdx = ismember(cand.id, selectedStation);
    selCoord = [cand.x(selIdx), cand.y(selIdx)];
    selCap = cand.S(selIdx, :);
    numStation = sum(selIdx);
    n = size(coordsC,1)-1;

    assignMat = cell(1,4);
    for k = 1:4
        demand_k = demW(2:end,k);
        stationLoad = zeros(numStation,1);
        assignment = zeros(n,1);
        for j = 1:n
            pt = coordsC(j+1,:);
            distList = vecnorm(selCoord - pt, 2, 2);
            [~, sortIdx] = sort(distList);
            for idx = sortIdx'
                if stationLoad(idx) + demand_k(j) <= selCap(idx,k)
                    stationLoad(idx) = stationLoad(idx) + demand_k(j);
                    assignment(j) = selectedStation(idx);
                    break;
                end
            end
            if assignment(j) == 0
                warning('收集点 %d 无法为第 %d 类垃圾分配中转站', j, k);
            end
        end
        assignMat{k} = assignment;
    end
end

function d = routeDist(rt, D)
    d = sum(D(sub2ind(size(D), rt(1:end-1), rt(2:end))));
end
