import pandas as pd
import numpy as np

# === Step 1: 读取数据 ===
df_location = pd.read_excel("附件1.xlsx", sheet_name="附件130个垃圾分类收集点坐标及总垃圾量", skiprows=2, usecols="A:D")
df_location.columns = ["ID", "X", "Y", "Weight"]
df_location["ID"] = df_location["ID"].astype(int)

df_depot = pd.DataFrame([{"ID": 0, "X": 0.0, "Y": 0.0}])
df_all_points = pd.concat([df_depot, df_location[['ID', 'X', 'Y']]], ignore_index=True)

df_garbage = pd.read_excel("附件3.xlsx", sheet_name="附件330个收集点的4类垃圾量分布", skiprows=1)
df_garbage.columns = ['ID', '厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']
df_garbage = df_garbage.astype({"ID": int})

# === Step 2: 构建距离矩阵 ===
coords = df_all_points[['X', 'Y']].to_numpy()
dist_matrix = np.linalg.norm(coords[:, None, :] - coords[None, :, :], axis=2)

# === Step 3: 构建车辆参数 ===
vehicle_params = pd.DataFrame({
    "垃圾类型": ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"],
    "载重Qk": [8, 6, 3, 10],
    "容积Vk": [20, 25, 10, 18],
    "运输成本Ck": [2.5, 2.0, 5.0, 1.8],
    "碳排放αk": [0.8, 0.6, 1.2, 0.7],
    "碳排放βk": [0.3, 0.2, 0.5, 0.25]
})

# === Step 4: 公共算法函数定义 ===
def nearest_neighbor(dist_matrix, nodes):
    unvisited = set(nodes)
    tour = [0]
    current = 0
    while unvisited:
        next_node = min(unvisited, key=lambda x: dist_matrix[current][x])
        tour.append(next_node)
        unvisited.remove(next_node)
        current = next_node
    tour.append(0)
    return tour

def split_by_capacity(tour, demand_map, Q):
    trips = []
    current_trip = [0]
    current_load = 0
    for node in tour[1:-1]:
        demand = demand_map[node]
        if current_load + demand <= Q:
            current_trip.append(node)
            current_load += demand
        else:
            current_trip.append(0)
            trips.append(current_trip)
            current_trip = [0, node]
            current_load = demand
    current_trip.append(0)
    trips.append(current_trip)
    return trips

def route_distance(route, dist_matrix):
    return sum(dist_matrix[route[i]][route[i + 1]] for i in range(len(route) - 1))

def two_opt(route, dist_matrix):
    best = route
    improved = True
    while improved:
        improved = False
        for i in range(1, len(best) - 2):
            for j in range(i + 1, len(best) - 1):
                if j - i == 1:
                    continue
                new_route = best[:i] + best[i:j][::-1] + best[j:]
                if route_distance(new_route, dist_matrix) < route_distance(best, dist_matrix):
                    best = new_route
                    improved = True
    return best

# === Step 5: 多类型垃圾运输路径优化 ===
all_results = []
overall_total_cost = 0

for idx, row in vehicle_params.iterrows():
    garbage_type = row['垃圾类型']
    vehicle_Q = row['载重Qk']
    vehicle_C = row['运输成本Ck']

    active_points = df_garbage[df_garbage[garbage_type] > 0][['ID', garbage_type]]
    active_ids = active_points['ID'].tolist()
    active_demands = dict(zip(active_points['ID'], active_points[garbage_type]))

    tsp_path = nearest_neighbor(dist_matrix, active_ids)
    split_trips = split_by_capacity(tsp_path, active_demands, vehicle_Q)

    optimized_trips = []
    trip_costs = []
    for trip in split_trips:
        opt_trip = two_opt(trip, dist_matrix)
        dist = route_distance(opt_trip, dist_matrix)
        cost = dist * vehicle_C
        optimized_trips.append(opt_trip)
        trip_costs.append(round(cost, 2))
        overall_total_cost += cost

    for i, (trip, cost) in enumerate(zip(optimized_trips, trip_costs)):
        all_results.append({
            "垃圾类型": garbage_type,
            "车辆编号": f"{garbage_type}-{i+1}",
            "运输路径": " -> ".join(map(str, trip)),
            "运输成本（元）": cost
        })

# === Step 6: 输出调度表和总成本 ===
results_df = pd.DataFrame(all_results)
print(results_df.to_string(index=False))
print(f"\n✅ 总运输成本：{round(overall_total_cost, 2)} 元")
