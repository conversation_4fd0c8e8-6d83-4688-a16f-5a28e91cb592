import pandas as pd
import numpy as np
from scipy.spatial.distance import cdist
import ast
import matplotlib.pyplot as plt

# === 数据读取 ===
df_location = pd.read_excel("附件1.xlsx", sheet_name="附件130个垃圾分类收集点坐标及总垃圾量", skiprows=2, usecols="A:D")
df_location.columns = ["ID", "X", "Y", "TotalWeight"]

df_garbage = pd.read_excel("附件3.xlsx", sheet_name="附件330个收集点的4类垃圾量分布", skiprows=1)
df_garbage.columns = ["ID", "厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"]

df_transfer = pd.read_excel("附件4.xlsx", sheet_name="附件4中转站候选位置及参数", skiprows=1)
df_transfer.columns = ["中转站编号", "X", "Y", "建设成本", "时间窗口", "存储容量"]
df_transfer["存储容量"] = df_transfer["存储容量"].apply(ast.literal_eval)
df_transfer["时间窗口"] = df_transfer["时间窗口"].apply(ast.literal_eval)

# === 参数定义 ===
garbage_types = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"]
vehicle_params = {
    "厨余垃圾": {"Q": 8, "C": 2.5, "α": 0.8, "β": 0.3, "v": 30},
    "可回收物": {"Q": 6, "C": 2.0, "α": 0.6, "β": 0.2, "v": 40},
    "有害垃圾": {"Q": 3, "C": 5.0, "α": 1.2, "β": 0.5, "v": 25},
    "其他垃圾": {"Q": 10, "C": 1.8, "α": 0.7, "β": 0.25, "v": 35}
}
T_MAX = 24  # 每辆车最多服务8小时

# === 合并点信息并计算距离矩阵 ===
df_points = pd.merge(df_location, df_garbage, on="ID")
point_coords = df_points[["X", "Y"]].to_numpy()
transfer_coords = df_transfer[["X", "Y"]].to_numpy()
all_coords = np.vstack([transfer_coords, point_coords])
dist_matrix = cdist(all_coords, all_coords)

# === 函数定义 ===
def nearest_neighbor_path(nodes, depot):
    unvisited = set(nodes)
    tour = [depot]
    current = depot
    while unvisited:
        next_node = min(unvisited, key=lambda x: dist_matrix[current][x])
        tour.append(next_node)
        unvisited.remove(next_node)
        current = next_node
    tour.append(depot)
    return tour

def split_by_capacity_and_time(tour, demand_map, Q, max_distance):
    trips = []
    trip = [tour[0]]
    load = 0
    dist = 0
    for i in range(1, len(tour)-1):
        last = trip[-1]
        node = tour[i]
        delta = dist_matrix[last][node]
        return_home = dist_matrix[node][tour[0]]
        if load + demand_map[node] <= Q and dist + delta + return_home <= max_distance:
            trip.append(node)
            load += demand_map[node]
            dist += delta
        else:
            trip.append(tour[0])
            trips.append(trip)
            trip = [tour[0], node]
            load = demand_map[node]
            dist = dist_matrix[tour[0]][node]
    trip.append(tour[0])
    trips.append(trip)
    return trips

def route_distance(route):
    return sum(dist_matrix[route[i]][route[i+1]] for i in range(len(route)-1))

def route_weight(route, demand_map):
    return sum(demand_map.get(i, 0) for i in route if i != route[0])

def two_opt(route):
    best = route
    improved = True
    while improved:
        improved = False
        for i in range(1, len(best)-2):
            for j in range(i+1, len(best)-1):
                if j - i == 1: continue
                new = best[:i] + best[i:j][::-1] + best[j:]
                if route_distance(new) < route_distance(best):
                    best = new
                    improved = True
    return best

# === 主调度循环 ===
all_results = []
for gtype in garbage_types:
    w_k = df_points[gtype].to_numpy()
    capacity_k = [c[garbage_types.index(gtype)] for c in df_transfer["存储容量"]]
    assignments = -np.ones(len(df_points), dtype=int)
    used_capacity = np.zeros(len(df_transfer))
    
    # 收集点分配至中转站
    d_point_to_station = cdist(df_points[["X", "Y"]], df_transfer[["X", "Y"]])
    for i in range(len(df_points)):
        for j in np.argsort(d_point_to_station[i]):
            if used_capacity[j] + w_k[i] <= capacity_k[j]:
                assignments[i] = j
                used_capacity[j] += w_k[i]
                break

    df_points[f"{gtype}_分配中转站"] = assignments

    # 路径规划
    for j in range(len(df_transfer)):
        assigned_indices = df_points[df_points[f"{gtype}_分配中转站"] == j].index
        if len(assigned_indices) == 0:
            continue

        # 索引转换
        nodes = [i + len(df_transfer) for i in assigned_indices]
        depot = j
        param = vehicle_params[gtype]
        max_dist = param["v"] * T_MAX

        demands = {n: w_k[n - len(df_transfer)] for n in nodes}
        path = nearest_neighbor_path(nodes, depot)
        split_trips = split_by_capacity_and_time(path, demands, param["Q"], max_dist)

        for t, trip in enumerate(split_trips):
            opt_trip = two_opt(trip)
            d = route_distance(opt_trip)
            w = route_weight(opt_trip, demands)
            cost = round(d * param["C"], 2)
            emission = round(param["α"] * d + param["β"] * w, 2)
            all_results.append({
                "垃圾类型": gtype,
                "中转站编号": df_transfer.loc[j, "中转站编号"],
                "车辆编号": f"{gtype}-车{t+1}",
                "运输路径": " -> ".join(map(str, opt_trip)),
                "运输距离(km)": round(d, 2),
                "垃圾重量(t)": round(w, 2),
                "运输成本(元)": cost,
                "碳排放(kg)": emission
            })

# === 汇总与输出 ===
df_result = pd.DataFrame(all_results)
total_cost = df_result["运输成本(元)"].sum()
total_emission = df_result["碳排放(kg)"].sum()

print(df_result.to_string(index=False))
print(f"\n✅ 总运输成本：{round(total_cost, 2)} 元")
print(f"✅ 总碳排放：{round(total_emission, 2)} 公斤")


import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 可视化：展示所有运输路径（每类垃圾不同颜色）
plt.figure(figsize=(12, 10))
color_map = {
    "厨余垃圾": "red",
    "可回收物": "green",
    "有害垃圾": "blue",
    "其他垃圾": "orange"
}

# 节点坐标索引映射
coord_map = {idx: (x, y) for idx, (x, y) in enumerate(all_coords)}

# 绘制路径
for _, row in df_result.iterrows():
    path = list(map(int, row["运输路径"].split(" -> ")))
    coords_path = [coord_map[i] for i in path]
    xs, ys = zip(*coords_path)
    plt.plot(xs, ys, '-o', color=color_map[row["垃圾类型"]], label=row["垃圾类型"])

# 标注中转站和收集点
for idx, row in df_transfer.iterrows():
    plt.scatter(row["X"], row["Y"], c='black', marker='s', s=100, label='中转站' if idx == 0 else "")
    plt.text(row["X"] + 0.3, row["Y"], str(row["中转站编号"]), fontsize=10)

for idx, row in df_points.iterrows():
    plt.scatter(row["X"], row["Y"], c='gray', s=40)
    plt.text(row["X"] + 0.3, row["Y"], str(row["ID"]), fontsize=8)

plt.title("问题三运输路径可视化（按垃圾类型）")
plt.xlabel("X 坐标")
plt.ylabel("Y 坐标")
plt.legend(loc='upper right')
plt.grid(True)
plt.tight_layout()
plt.show()


for gtype in df_result["垃圾类型"].unique():
    plt.figure(figsize=(10, 8))
    plt.title(f"{gtype}运输路径图")
    for _, row in df_result[df_result["垃圾类型"] == gtype].iterrows():
        path = list(map(int, row["运输路径"].split(" -> ")))
        coords_path = [coord_map[i] for i in path]
        xs, ys = zip(*coords_path)
        plt.plot(xs, ys, '-o', label=row["车辆编号"])

    for idx, row in df_transfer.iterrows():
        plt.scatter(row["X"], row["Y"], c='black', marker='s', s=100)
        plt.text(row["X"] + 0.3, row["Y"], f"站{row['中转站编号']}", fontsize=9)

    plt.xlabel("X 坐标")
    plt.ylabel("Y 坐标")
    plt.legend(loc='upper right')
    plt.grid(True)
    plt.tight_layout()
    plt.show()


# 重新加载用户上传的三份数据文件
file1_path = "附件1.xlsx"
file3_path = "附件3.xlsx"
file4_path = "附件4.xlsx"

# 加载收集点位置
df_location = pd.read_excel(file1_path, sheet_name="附件130个垃圾分类收集点坐标及总垃圾量", skiprows=2, usecols="A:D")
df_location.columns = ["ID", "X", "Y", "TotalWeight"]

# 加载垃圾量
df_garbage = pd.read_excel(file3_path, sheet_name="附件330个收集点的4类垃圾量分布", skiprows=1)
df_garbage.columns = ["ID", "厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"]

# 加载中转站信息
df_transfer = pd.read_excel(file4_path, sheet_name="附件4中转站候选位置及参数", skiprows=1)
df_transfer.columns = ["中转站编号", "X", "Y", "建设成本", "时间窗口", "存储容量"]
df_transfer["存储容量"] = df_transfer["存储容量"].apply(ast.literal_eval)
df_transfer["时间窗口"] = df_transfer["时间窗口"].apply(ast.literal_eval)

# 合并收集点信息
df_points = pd.merge(df_location, df_garbage, on="ID")

# 构建所有节点坐标并计算距离矩阵
point_coords = df_points[["X", "Y"]].to_numpy()
transfer_coords = df_transfer[["X", "Y"]].to_numpy()
all_coords = np.vstack([transfer_coords, point_coords])
dist_matrix = cdist(all_coords, all_coords)

# 构建映射
id_index_map = {id_: idx for idx, id_ in enumerate(
    df_transfer["中转站编号"].tolist() + df_points["ID"].tolist())}

# 准备非对称距离矩阵
dist_asym = dist_matrix.copy()


# 定义非对称规则（来自附件5说明）
def override_distance(i, j, d_ij, d_ji):
    if i in id_index_map and j in id_index_map:
        idx_i = id_index_map[i]
        idx_j = id_index_map[j]
        dist_asym[idx_i, idx_j] = d_ij
        dist_asym[idx_j, idx_i] = d_ji

override_distance(4, 31, 18, 15)
override_distance(27, 28, 14, 18)
override_distance(23, 0, 45, 40)
override_distance(9, 16, 8, 10)

# 路径计算函数与2-opt替换（使用非对称距离）
def route_distance_asym(route):
    return sum(dist_asym[route[i], route[i+1]] for i in range(len(route) - 1))

def two_opt_asym(route):
    best = route
    improved = True
    while improved:
        improved = False
        for i in range(1, len(best)-2):
            for j in range(i+1, len(best)-1):
                if j - i == 1: continue
                new = best[:i] + best[i:j][::-1] + best[j:]
                if route_distance_asym(new) < route_distance_asym(best):
                    best = new
                    improved = True
    return best

# 准备其他参数
garbage_types = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"]
vehicle_params = {
    "厨余垃圾": {"Q": 8, "C": 2.5, "α": 0.8, "β": 0.3, "v": 30},
    "可回收物": {"Q": 6, "C": 2.0, "α": 0.6, "β": 0.2, "v": 40},
    "有害垃圾": {"Q": 3, "C": 5.0, "α": 1.2, "β": 0.5, "v": 25},
    "其他垃圾": {"Q": 10, "C": 1.8, "α": 0.7, "β": 0.25, "v": 35}
}
T_MAX = 8  # 每辆车最大行驶时间（小时）

# 准备路径优化结构并执行调度
results_asym = []
from scipy.spatial.distance import cdist

def nearest_neighbor_path(nodes, depot):
    unvisited = set(nodes)
    tour = [depot]
    current = depot
    while unvisited:
        next_node = min(unvisited, key=lambda x: dist_asym[current][x])
        tour.append(next_node)
        unvisited.remove(next_node)
        current = next_node
    tour.append(depot)
    return tour

def split_by_capacity_and_time(tour, demand_map, Q, max_distance):
    trips = []
    trip = [tour[0]]
    load = 0
    dist = 0
    for i in range(1, len(tour)-1):
        last = trip[-1]
        node = tour[i]
        delta = dist_asym[last][node]
        return_home = dist_asym[node][tour[0]]
        if load + demand_map[node] <= Q and dist + delta + return_home <= max_distance:
            trip.append(node)
            load += demand_map[node]
            dist += delta
        else:
            trip.append(tour[0])
            trips.append(trip)
            trip = [tour[0], node]
            load = demand_map[node]
            dist = dist_asym[tour[0]][node]
    trip.append(tour[0])
    trips.append(trip)
    return trips

def route_weight(route, demand_map):
    return sum(demand_map.get(i, 0) for i in route if i != route[0])

# 主调度过程
for gtype in garbage_types:
    w_k = df_points[gtype].to_numpy()
    capacity_k = [c[garbage_types.index(gtype)] for c in df_transfer["存储容量"]]
    assignments = -np.ones(len(df_points), dtype=int)
    used_capacity = np.zeros(len(df_transfer))
    d_point_to_station = cdist(df_points[["X", "Y"]], df_transfer[["X", "Y"]])
    for i in range(len(df_points)):
        for j in np.argsort(d_point_to_station[i]):
            if used_capacity[j] + w_k[i] <= capacity_k[j]:
                assignments[i] = j
                used_capacity[j] += w_k[i]
                break

    df_points[f"{gtype}_分配中转站"] = assignments

    for j in range(len(df_transfer)):
        assigned_indices = df_points[df_points[f"{gtype}_分配中转站"] == j].index
        if len(assigned_indices) == 0:
            continue

        nodes = [i + len(df_transfer) for i in assigned_indices]
        depot = j
        param = vehicle_params[gtype]
        max_dist = param["v"] * T_MAX
        demands = {n: w_k[n - len(df_transfer)] for n in nodes}
        path = nearest_neighbor_path(nodes, depot)
        split_trips = split_by_capacity_and_time(path, demands, param["Q"], max_dist)

        for t, trip in enumerate(split_trips):
            opt_trip = two_opt_asym(trip)
            d = route_distance_asym(opt_trip)
            w = route_weight(opt_trip, demands)
            cost = round(d * param["C"], 2)
            emission = round(param["α"] * d + param["β"] * w, 2)
            results_asym.append({
                "垃圾类型": gtype,
                "中转站编号": df_transfer.loc[j, "中转站编号"],
                "车辆编号": f"{gtype}-车{t+1}",
                "运输路径": " -> ".join(map(str, opt_trip)),
                "运输距离(km)": round(d, 2),
                "垃圾重量(t)": round(w, 2),
                "运输成本(元)": cost,
                "碳排放(kg)": emission
            })

# 输出最终结果表格
df_result_asym = pd.DataFrame(results_asym)
# 将结果保存为 Excel 文件
df_result_asym.to_excel("非对称路网调度结果.xlsx", index=False)

# 如果你用的是Jupyter Notebook或本地Python环境：
print("✅ 结果已保存为文件：非对称路网调度结果.xlsx")

total_cost_asym = df_result_asym["运输成本(元)"].sum()
total_emission_asym = df_result_asym["碳排放(kg)"].sum()
total_cost_asym, total_emission_asym
