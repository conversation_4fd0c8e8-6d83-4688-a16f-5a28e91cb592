%% PROBLEM3_TRANSFER_ROUTING.M
% 含中转站选址与路径优化的两阶段启发式（对称路网，不含单行道处理）
clear; clc; close all;
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 1. 输入：收集点与需求
% 点 0 = 处理厂，1..30 = 收集点
pt_data = [ ...
    0,   0,   0,      0,    0,    0,    0;    % 0: 处理厂
    12,   8,   0.72, 0.12, 0.06, 0.30, 0;  % 1–30: xi, yi, w_i1..w_i4
    5,   15,   1.38, 0.23, 0.05, 0.64, 0;
    20,  30,   1.08, 0.18, 0.04, 0.50, 0;
    25,  10,   1.55, 0.31, 0.06, 1.18, 0;
    35,  22,   1.62, 0.27, 0.05, 0.76, 0;
    18,   5,   1.76, 0.384,0.096,0.96, 0;
    30,  35,   0.77, 0.168,0.042,0.42, 0;
    10,  25,   1.02, 0.238,0.068,0.374,0;
    22,  18,   1.32, 0.176,0.044,0.66, 0;
    38,  15,   1.45, 0.30, 0.075,0.675,0;
    5,    8,   1.35, 0.27, 0.108,0.972,0;
    15,  32,   1.87, 0.51, 0.068,0.952,0;
    28,   5,   2.58, 0.516,0.129,1.075,0;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    30,  12,   1.134,0.21, 0.063,0.693,0;
    10,  10,   0.78, 0.13, 0.065,0.325,0;
    20,  20,   0.768,0.192,0.080,0.56, 0;
    35,  30,   0.72, 0.27, 0.09, 0.72, 0;
    8,    22,  1.595,0.348,0.087,0.87, 0;
    25,  25,   1.50, 0.36, 0.09, 1.05, 0;
    32,   8,   1.08, 0.18, 0.09, 0.45, 0;
    15,   5,   0.912,0.19, 0.038,0.76,0;
    28,  20,   0.90, 0.195,0.075,0.33, 0;
    38,  25,   0.99, 0.27, 0.072,0.468,0;
    10,  30,   1.44, 0.24, 0.048,0.672,0;
    20,  10,   1.74, 0.319,0.116,0.725,0;
    30,  18,   1.17, 0.39, 0.13, 0.91, 0;
    5,   25,   1.70, 0.34, 0.17, 1.19, 0;
    18,  30,   2.64, 0.66, 0.044,1.056,0;
    35,  10,   0.864,0.216,0.072,0.648,0;
    22,  35,   0.986,0.204,0.085,0.425,0
];
coords = pt_data(:,1:2);
w_all  = pt_data(:,3:6);  % (n+1)x4
n = 30;
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 2. 输入：候选中转站数据 (5 个)
% 序号 31..35
st_data = [ ...
  31,  5,  0.4,  20, 6;   % id, Sj1 (厨余), Sj2, Sj3, Sj4, capacity
  32, 10,  0.5,  25, 8;
  33,  8,  0.6,  15, 7;
  34, 12,  0.7,  30, 9;
  35,  7,  0.3,  18, 5
];
% 对应时间窗 [aj,bj] （工厂 6-18h）
tw = [ ...
 31,  7,17;  % station, open, close
 32,  8,16;
 33,  6,18;
 34,  9,15;
 35,  6,18
];
Tj = 10000;  % 每站建设成本 (元/天, 已按10年寿命摊)

%% 3. 参数
% 车辆参数同前
Q = [8,6,3,10]; 
C = [2.5,2.0,5.0,1.8];
a = [0.8,0.6,1.2,0.7];
beta = [0.3,0.2,0.5,0.25];
v = 40;  

%% 4. 距离矩阵：点 0..30 + station 31..35
all_coords = [coords; % 0..30
  12,5;  7,28;  20,8;  30,15;  25,10  % 示例站点坐标
];
D = squareform(pdist(all_coords));

%% 5. 阶段一：选址（简单启发：全选）
y = ones(5,1);  % 打开所有5个站
fprintf('阶段1: 选址 y = [%s]\n', num2str(y'));

%% 分配：每点、每类型 分配到最近站
assign = zeros(n,4);
for i=1:n
  for k=1:4
    dmin = inf; sj = -1;
    for s=1:5
      j = 30+s;
      dij = D(i+1,j+1);
      if dij<dmin
        dmin=dij; sj=s;
      end
    end
    assign(i,k) = sj;  % i-> station index sj
  end
end

%% 6. 阶段二：对每站 j 及每类型 k 运行最近邻 CVRP
all_routes = cell(5,4);
all_dist   = zeros(5,4);
all_E      = zeros(5,4);

for s=1:5
  j = 30+s;
  st_idx = j+1;
  for k=1:4
    pts = find(assign(:,k)==s)';  % 分配到站 s 的点
    if isempty(pts), continue; end
    % 构造局部 coords_w、w_w
    local_coords = [all_coords(j,:); all_coords(pts+1,:)];
    local_w      = w_all(pts+1,k);
    m = numel(pts);
    % 距离矩阵
    Dloc = squareform(pdist(local_coords));
    % 贪心最近邻
    unserv = 1:m;
    routes = {};
    while ~isempty(unserv)
      curr = 1;  load=0; route=1;
      while true
        bestD=inf; bi=-1;
        for t=unserv
          if load+local_w(t)<=Q(k)
            dij=Dloc(curr,t+1);
            if dij<bestD
              bestD=dij;bi=t;
            end
          end
        end
        if bi<0, break; end
        route=[route,bi]; load=load+local_w(bi); curr=bi;
        unserv(unserv==bi)=[];
      end
      route=[route,1];
      routes{end+1}=route;
    end
    % 计算距离 & 排放
    dist=0; E=0;
    for r=1:numel(routes)
      rt = routes{r};
      for t=1:length(rt)-1
        u=rt(t); v_=rt(t+1);
        duv=Dloc(u,v_);
        dist=dist+duv;
        E=E + duv*a(k) + beta(k)*(sum(local_w(rt(2:end))));
      end
    end
    all_routes{s,k}=routes;
    all_dist(s,k)=dist;
    all_E(s,k)=E;
  end
end

%% 7. 输出 & 可视化
fprintf('\n阶段2 结果:\n');
for s=1:5
  for k=1:4
    if isempty(all_routes{s,k}), continue; end
    fprintf('站%2d 类别%d: ',30+s,k);
    fprintf('%d条路径, 距离%.1f, 排放%.1f\n',...
      numel(all_routes{s,k}), all_dist(s,k), all_E(s,k));
  end
end
%%
%% 1. 单站点-单类型示例 (中转站31，厨余垃圾)
s = 1; k = 1; j = 30 + s;
pts = find(assign(:,k) == s);
figure('Name', sprintf('中转站 %d - 类型 %d 路线示例', j, k), 'NumberTitle', 'off');
hold on; grid on;
% 绘制中转站
scatter(all_coords(j+1,1), all_coords(j+1,2), 150, 'rs', 'filled');
text(all_coords(j+1,1), all_coords(j+1,2), sprintf(' 中转站%d', j), 'FontWeight', 'bold');
% 绘制客户点
scatter(all_coords(pts+1,1), all_coords(pts+1,2), 80, 'bo');
for ii = pts'
    text(all_coords(ii+1,1), all_coords(ii+1,2), [' 点', num2str(ii)]);
end
% 绘制路线
routes_loc = all_routes{s,k};
for rr = 1:length(routes_loc)
    r = routes_loc{rr};
    % 本地索引转全局索引\    
    rt_global = zeros(size(r));
    for t = 1:length(r)
        if r(t) == 1
            rt_global(t) = j;
        else
            rt_global(t) = pts(r(t)-1);
        end
    end
    plot(all_coords(rt_global+1,1), all_coords(rt_global+1,2), '-o', 'LineWidth', 2);
end

title('中转站31 厨余垃圾运输路径示例');
xlabel('X 坐标 (km)');
ylabel('Y 坐标 (km)');
legend({'中转站','收集点','运输路径'}, 'Location', 'best');

%% 2. 单站点-多类型子图 (中转站31)
figure('Name', sprintf('中转站 %d 各类型路线', j), 'NumberTitle', 'off');
for k = 1:4
    subplot(2,2,k); hold on; grid on;
    % 中转站
    scatter(all_coords(j+1,1), all_coords(j+1,2), 120, 'rs', 'filled');
    text(all_coords(j+1,1), all_coords(j+1,2), sprintf(' 中转站%d', j));
    % 客户点
    pts = find(assign(:,k) == s);
    scatter(all_coords(pts+1,1), all_coords(pts+1,2), 60, 'bo');
    for ii = pts'
        text(all_coords(ii+1,1), all_coords(ii+1,2), [' 点', num2str(ii)]);
    end
    % 路线
    routes_loc = all_routes{s,k};
    for rr = 1:length(routes_loc)
        r = routes_loc{rr};
        rt_global = zeros(size(r));
        for t = 1:length(r)
            if r(t) == 1
                rt_global(t) = j;
            else
                rt_global(t) = pts(r(t)-1);
            end
        end
        plot(all_coords(rt_global+1,1), all_coords(rt_global+1,2), 'LineWidth', 1.5);
    end
    title(sprintf('垃圾类型 %d 路线', k)); axis equal;
    xlabel('X 坐标'); ylabel('Y 坐标');
end
sgtitle(sprintf('中转站%d 各类型运输路径', j));

%% 3. 全局可视化: 所有中转站与类型路线概览
figure('Name', '全局运输路线概览', 'NumberTitle', 'off'); hold on; grid on;
% 厂区 & 客户点
scatter(all_coords(1,1), all_coords(1,2), 120, 'kd', 'filled');
text(all_coords(1,1), all_coords(1,2), ' 处理厂');
scatter(all_coords(2:31,1), all_coords(2:31,2), 50, 'ko');
for ii = 1:30
    text(all_coords(ii+1,1), all_coords(ii+1,2), [' 点', num2str(ii)]);
end
% 中转站
scatter(all_coords(32:36,1), all_coords(32:36,2), 120, 'rs');
for s = 1:5
    text(all_coords(30+s+1,1), all_coords(30+s+1,2), sprintf(' 中转站%d', 30+s));
end
% 路线，不同站点颜色，不同类型线型
colors = lines(5);
linestyles = {'-','--',':','-.'};
for s = 1:5
    j = 30 + s;
    for k = 1:4
        for rr = 1:length(all_routes{s,k})
            r = all_routes{s,k}{rr};
            pts = find(assign(:,k) == s);
            rt_global = zeros(size(r));
            for t = 1:length(r)
                if r(t) == 1
                    rt_global(t) = j;
                else
                    rt_global(t) = pts(r(t)-1);
                end
            end
            plot(all_coords(rt_global+1,1), all_coords(rt_global+1,2), ...
                'Color', colors(s,:), 'LineStyle', linestyles{k}, 'LineWidth', 1.2);
        end
    end
end

title('全局中转站运输路线概览');
xlabel('X 坐标 (km)');
ylabel('Y 坐标 (km)');
legend({'处理厂','客户点','中转站','运输路线'}, 'Location', 'eastoutside');
