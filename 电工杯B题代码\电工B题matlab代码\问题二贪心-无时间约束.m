%% VRP_GREEDY_MULTI_DETAILED.M
% 多类型垃圾运输 - 最近邻贪心 + 详细输出 + 可视化
clear; clc; close all;

%% 1. 输入数据%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
data = [ ...
    0,   0,   0.00, 0.00, 0.00, 0.00;  % 0: 厂区
    12,   8,   0.72, 0.12, 0.06, 0.30;
    5,   15,   1.38, 0.23, 0.05, 0.64;
    20,  30,   1.08, 0.18, 0.04, 0.50;
    25,  10,   1.55, 0.31, 0.06, 1.18;
    35,  22,   1.62, 0.27, 0.05, 0.76;
    18,   5,   1.76, 0.384,0.096,0.96;
    30,  35,   0.77, 0.168,0.042,0.42;
    10,  25,   1.02, 0.238,0.068,0.374;
    22,  18,   1.32, 0.176,0.044,0.66;
    38,  15,   1.45, 0.30, 0.075,0.675;
    5,    8,   1.35, 0.27, 0.108,0.972;
    15,  32,   1.87, 0.51, 0.068,0.952;
    28,   5,   2.58, 0.516,0.129,1.075;
    30,  12,   1.134,0.21, 0.063,0.693;
    10,  10,   0.78, 0.13, 0.065,0.325;
    20,  20,   0.768,0.192,0.080,0.56;
    35,  30,   0.72, 0.27, 0.090,0.72;
    8,   22,   1.595,0.348,0.087,0.87;
    25,  25,   1.50, 0.36, 0.090,1.05;
    32,   8,   1.08, 0.18, 0.090,0.45;
    15,   5,   0.912,0.19, 0.038,0.76;
    28,  20,   0.90, 0.195,0.075,0.33;
    38,  25,   0.99, 0.27, 0.072,0.468;
    10,  30,   1.44, 0.24, 0.048,0.672;
    20,  10,   1.74, 0.319,0.116,0.725;
    30,  18,   1.17, 0.39, 0.130,0.91;
    5,   25,   1.70, 0.34, 0.170,1.19;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    18,  30,   2.64, 0.66, 0.044,1.056;
    35,  10,   0.864,0.216,0.072,0.648;
    22,  35,   0.986,0.204,0.085,0.425
];
coords = data(:,1:2);
w_all  = data(:,3:6);
n      = size(coords,1)-1;

% 车辆参数
Q = [8, 6, 3, 10];         % 载重上限
C = [2.5, 2.0, 5.0, 1.8];   % 单位距离成本

% 距离矩阵
D = squareform(pdist(coords));

% 存储结果
all_routes = cell(4,1);
all_dist   = cell(4,1);
all_load   = cell(4,1);
all_cost   = zeros(4,1);

%% 2. 对每类车辆执行最近邻贪心
for k = 1:4
    fprintf('\n=== 垃圾类型 %d (Q=%.1f 吨, C=%.1f 元/km) ===\n', k, Q(k), C(k));
    unserved = find(w_all(2:end,k)>0)';  % 待服务点编号 1..n
    routes_k = {};
    loads_k  = [];
    dist_k   = [];
    
    v = 0;  % 车辆计数
    % 一直派车，直到无未服务点
    while ~isempty(unserved)
        v = v + 1;
        currNode = 0; currLoad = 0;
        path = 0;  % 路径起始
        % 本车多趟装载
        while true
            bestD = inf; bestI = -1;
            for i = unserved
                if currLoad + w_all(i+1,k) <= Q(k)
                    if D(currNode+1,i+1) < bestD
                        bestD = D(currNode+1,i+1);
                        bestI = i;
                    end
                end
            end
            if bestI < 0
                break;  % 该车满载或四周已无可装点
            end
            % 访问 bestI
            path = [path, bestI];
            currLoad = currLoad + w_all(bestI+1,k);
            currNode = bestI;
            unserved(unserved==bestI) = [];
        end
        % 返回厂区
        path = [path, 0];
        
        % 计算该车总距离
        dsum = 0;
        for t = 1:length(path)-1
            dsum = dsum + D(path(t)+1, path(t+1)+1);
        end
        
        % 记录
        routes_k{v} = path;
        loads_k(v)  = currLoad;
        dist_k(v)   = dsum;
        
        % 打印该车信息
        fprintf('  车 %d: 路径 %s | 载重 %.2f 吨 | 距离 %.2f km\n', ...
            v, mat2str(path), currLoad, dsum);
    end
    
    % 汇总该类型
    all_routes{k} = routes_k;
    all_load{k}   = loads_k;
    all_dist{k}   = dist_k;
    all_cost(k)   = sum(dist_k)*C(k);
    fprintf('类型 %d 共用车辆 %d 辆, 总距离 %.2f km, 总成本 %.2f 元\n', ...
        k, v, sum(dist_k), all_cost(k));
end

fprintf('\n== 全部类别 总运输成本: %.2f 元 ==\n', sum(all_cost));%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16

%% 3. 可视化：路径总览
figure('Name','所有类型车辆路径','NumberTitle','off'); hold on; grid on;
scatter(coords(1,1),coords(1,2),120,'ks','filled');
text(coords(1,1),coords(1,2),' 0','FontWeight','bold');
scatter(coords(2:end,1),coords(2:end,2),60,'ko');
for i=2:n+1, text(coords(i,1),coords(i,2),[' ',num2str(i-1)]); end

linestyles = {'-','--',':','-.'};
colors     = lines(4);
for k=1:4
    for v=1:numel(all_routes{k})
        rt = all_routes{k}{v}+1;
        plot(coords(rt,1),coords(rt,2), ...
            'LineStyle',linestyles{k}, 'Color',colors(k,:), 'LineWidth',1.8);
    end
end
title('四类车辆贪心调度路径');
xlabel('X (km)'); ylabel('Y (km)');
leg = {'厂区','客户'};
for k=1:4, leg{end+1}=sprintf('类型%d路径',k); end
legend(leg,'Location','bestoutside');

%% 4. 可视化：载重/距离/成本统计
% 每类柱状图
figure('Name','统计对比','NumberTitle','off');
subplot(3,1,1);
bar(cellfun(@sum, all_load));
title('各类车辆总载重 (吨)'); xlabel('车辆类型'); ylabel('吨');
subplot(3,1,2);
bar(cellfun(@sum, all_dist));
title('各类车辆总距离 (km)'); xlabel('车辆类型'); ylabel('km');
subplot(3,1,3);
bar(all_cost);
title('各类运输总成本 (元)'); xlabel('车辆类型'); ylabel('元');
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
% 成本占比饼图
figure('Name','成本占比','NumberTitle','off');
pie(all_cost, arrayfun(@(k)sprintf('类%d',k),1:4,'Uni',false));
title('各类运输成本占比');

