%% 修改后的扫描算法
function clusters = sweep_algorithm_modified(coords, demands, capacity)
    n = size(coords, 1) - 1;
    
    if n == 0
        clusters = {};
        return;
    end
    
    depot = coords(1, :);
    
    % 计算极角
    angles = zeros(n, 1);
    for i = 1:n
        dx = coords(i+1, 1) - depot(1);
        dy = coords(i+1, 2) - depot(2);
        angles(i) = atan2(dy, dx);
    end
    
    % 排序
    [~, sorted_idx] = sort(angles);
    
    % 贪心分组
    clusters = {};
    current_cluster = [];
    current_load = 0;
    cluster_count = 0;
    
    for i = 1:n
        idx = sorted_idx(i);
        point_demand = demands(idx + 1);
        
        if current_load + point_demand <= capacity
            current_cluster = [current_cluster, idx];
            current_load = current_load + point_demand;
        else
            if ~isempty(current_cluster)
                cluster_count = cluster_count + 1;
                clusters{cluster_count} = current_cluster;
            end
            current_cluster = [idx];
            current_load = point_demand;
        end
    end
    
    if ~isempty(current_cluster)
        cluster_count = cluster_count + 1;
        clusters{cluster_count} = current_cluster;
    end
end
