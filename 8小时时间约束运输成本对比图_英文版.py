import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# 设置图表样式
sns.set_style("whitegrid")
plt.figure(figsize=(16, 12))

# 数据准备 - 使用英文标签
waste_types = ['Kitchen Waste', 'Recyclables', 'Hazardous Waste', 'Other Waste']
waste_types_cn = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

# 无时间约束的成本数据（基于10小时约束，相当于无约束）
no_constraint_costs = [1116.45, 455.81, 896.52, 467.13]  # 元

# 8小时时间约束的成本数据
with_constraint_costs = [1116.45, 455.81, 1130.55, 467.13]  # 元

# 计算成本增加量和增长率
cost_increase = [with_constraint_costs[i] - no_constraint_costs[i] for i in range(4)]
growth_rate = [(cost_increase[i] / no_constraint_costs[i]) * 100 if no_constraint_costs[i] > 0 else 0 for i in range(4)]

# 创建子图布局
fig = plt.figure(figsize=(16, 12))

# 子图1：成本对比柱状图
ax1 = plt.subplot(2, 2, 1)
x = np.arange(len(waste_types))
width = 0.35

bars1 = ax1.bar(x - width/2, no_constraint_costs, width, label='No Time Constraint', 
                color='#3498db', alpha=0.8, edgecolor='black', linewidth=0.5)
bars2 = ax1.bar(x + width/2, with_constraint_costs, width, label='8-Hour Time Constraint', 
                color='#e74c3c', alpha=0.8, edgecolor='black', linewidth=0.5)

# 添加数值标签
def add_value_labels(bars, ax):
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 10,
                f'{height:.0f}', ha='center', va='bottom', fontsize=10, fontweight='bold')

add_value_labels(bars1, ax1)
add_value_labels(bars2, ax1)

ax1.set_xlabel('Waste Type', fontsize=14, fontweight='bold')
ax1.set_ylabel('Transportation Cost (Yuan)', fontsize=14, fontweight='bold')
ax1.set_title('Transportation Cost Comparison under 8-Hour Time Constraint', fontsize=16, fontweight='bold', pad=20)
ax1.set_xticks(x)
ax1.set_xticklabels(waste_types, fontsize=12)
ax1.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')
ax1.grid(axis='y', linestyle='--', alpha=0.7)

# 子图2：成本增加量柱状图
ax2 = plt.subplot(2, 2, 2)
colors = ['#2ecc71' if inc == 0 else '#e74c3c' for inc in cost_increase]
bars3 = ax2.bar(waste_types, cost_increase, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)

# 添加数值标签
for i, bar in enumerate(bars3):
    height = bar.get_height()
    if height > 0:
        ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'+{height:.0f}', ha='center', va='bottom', fontsize=11, fontweight='bold')
    else:
        ax2.text(bar.get_x() + bar.get_width()/2., 10,
                '0', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax2.set_xlabel('Waste Type', fontsize=14, fontweight='bold')
ax2.set_ylabel('Cost Increase (Yuan)', fontsize=14, fontweight='bold')
ax2.set_title('Cost Increase Due to Time Constraint', fontsize=16, fontweight='bold', pad=20)
ax2.grid(axis='y', linestyle='--', alpha=0.7)
ax2.tick_params(axis='x', labelsize=10, rotation=15)

# 子图3：成本增长率柱状图
ax3 = plt.subplot(2, 2, 3)
colors_rate = ['#2ecc71' if rate == 0 else '#e74c3c' for rate in growth_rate]
bars4 = ax3.bar(waste_types, growth_rate, color=colors_rate, alpha=0.8, edgecolor='black', linewidth=0.5)

# 添加数值标签
for i, bar in enumerate(bars4):
    height = bar.get_height()
    if height > 0:
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'+{height:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')
    else:
        ax3.text(bar.get_x() + bar.get_width()/2., 1,
                '0%', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax3.set_xlabel('Waste Type', fontsize=14, fontweight='bold')
ax3.set_ylabel('Cost Growth Rate (%)', fontsize=14, fontweight='bold')
ax3.set_title('Cost Growth Rate Due to Time Constraint', fontsize=16, fontweight='bold', pad=20)
ax3.grid(axis='y', linestyle='--', alpha=0.7)
ax3.tick_params(axis='x', labelsize=10, rotation=15)

# 子图4：总成本对比饼图
ax4 = plt.subplot(2, 2, 4)
total_no_constraint = sum(no_constraint_costs)
total_with_constraint = sum(with_constraint_costs)
total_increase = total_with_constraint - total_no_constraint

# 创建饼图数据
pie_data = [total_no_constraint, total_increase]
pie_labels = [f'Base Cost\n{total_no_constraint:.0f} Yuan', f'Time Constraint\nIncrease\n{total_increase:.0f} Yuan']
colors_pie = ['#3498db', '#e74c3c']

wedges, texts, autotexts = ax4.pie(pie_data, labels=pie_labels, colors=colors_pie, 
                                   autopct='%1.1f%%', startangle=90, 
                                   textprops={'fontsize': 11, 'fontweight': 'bold'})

ax4.set_title(f'Total Transportation Cost Composition\nTotal Cost: {total_with_constraint:.0f} Yuan', 
              fontsize=16, fontweight='bold', pad=20)

# 调整布局
plt.tight_layout(pad=3.0)

# 添加总体说明文本
fig.text(0.5, 0.02, 
         f'Analysis Result: 8-hour time constraint mainly affects hazardous waste transportation, causing cost increase of {total_increase:.0f} Yuan (+{(total_increase/total_no_constraint)*100:.1f}%)',
         ha='center', fontsize=12, fontweight='bold', 
         bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))

# 保存图片
plt.savefig('8Hour_Time_Constraint_Cost_Analysis_EN.png', dpi=300, bbox_inches='tight')
plt.savefig('8Hour_Time_Constraint_Cost_Analysis_EN.pdf', bbox_inches='tight')

# 显示图表
plt.show()

# 打印详细分析结果
print("="*80)
print("8-Hour Time Constraint Transportation Cost Analysis")
print("="*80)
print(f"{'Waste Type':<20} {'No Constraint':<15} {'With Constraint':<15} {'Increase':<10} {'Growth Rate':<12}")
print("-"*80)
for i, waste_type in enumerate(waste_types):
    print(f"{waste_type:<20} {no_constraint_costs[i]:<15.2f} {with_constraint_costs[i]:<15.2f} "
          f"{cost_increase[i]:<10.2f} {growth_rate[i]:<12.1f}%")
print("-"*80)
print(f"{'Total':<20} {total_no_constraint:<15.2f} {total_with_constraint:<15.2f} "
      f"{total_increase:<10.2f} {(total_increase/total_no_constraint)*100:<12.1f}%")
print("="*80)
print("\nKey Findings:")
print("1. Hazardous waste is most affected by time constraints, with cost increase of 234.03 Yuan (+26.1%)")
print("2. Kitchen waste, recyclables, and other waste are not affected by 8-hour time constraint")
print("3. Overall cost increase is 7.9%, mainly due to hazardous waste route splitting")
print("4. Time constraints have significantly different impacts on different waste types")

# 添加中英文对照表
print("\n" + "="*80)
print("Waste Type Translation:")
print("="*80)
for i, (en, cn) in enumerate(zip(waste_types, waste_types_cn)):
    print(f"{en:<20} -> {cn}")
print("="*80)
