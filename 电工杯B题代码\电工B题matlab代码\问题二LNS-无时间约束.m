
%% VRP_LNS_MULTI_TYPE.M
% 多类型垃圾运输 - 大规模邻域搜索 (LNS) 实现
clear; clc; close all;

clear; clc; close all;

%% 1. 输入数据
data = [ ...
    0,    0,   0.00, 0.00, 0.00, 0.00;  % 0: 厂区
    12,   8,   0.72, 0.12, 0.06, 0.30;
    5,   15,   1.38, 0.23, 0.05, 0.64;
    20,  30,   1.08, 0.18, 0.04, 0.50;
    25,  10,   1.55, 0.31, 0.06, 1.18;
    35,  22,   1.62, 0.27, 0.05, 0.76;
    18,   5,   1.76, 0.384,0.096,0.96;
    30,  35,   0.77, 0.168,0.042,0.42;
    10,  25,   1.02, 0.238,0.068,0.374;
    22,  18,   1.32, 0.176,0.044,0.66;
    38,  15,   1.45, 0.30, 0.075,0.675;
    5,    8,   1.35, 0.27, 0.108,0.972;
    15,  32,   1.87, 0.51, 0.068,0.952;
    28,   5,   2.58, 0.516,0.129,1.075;
    30,  12,   1.134,0.21, 0.063,0.693;
    10,  10,   0.78, 0.13, 0.065,0.325;
    20,  20,   0.768,0.192,0.080,0.56;
    35,  30,   0.72, 0.27, 0.090,0.72;
    8,   22,   1.595,0.348,0.087,0.87;
    25,  25,   1.50, 0.36, 0.090,1.05;
    32,   8,   1.08, 0.18, 0.090,0.45;
    15,   5,   0.912,0.19, 0.038,0.76;
    28,  20,   0.90, 0.195,0.075,0.33;
    38,  25,   0.99, 0.27, 0.072,0.468;
    10,  30,   1.44, 0.24, 0.048,0.672;
    20,  10,   1.74, 0.319,0.116,0.725;
    30,  18,   1.17, 0.39, 0.130,0.91;
    5,   25,   1.70, 0.34, 0.170,1.19;
    18,  30,   2.64, 0.66, 0.044,1.056;
    35,  10,   0.864,0.216,0.072,0.648;
    22,  35,   0.986,0.204,0.085,0.425
];
coords = data(:,1:2);
w_all  = data(:,3:6);
N      = size(coords,1);
D      = squareform(pdist(coords));  % 距离矩阵
Q      = [8,6,3,10];                % 各类型载重
C      = [2.5,2.0,5.0,1.8];          % 各类型单位距离成本
maxIter = 300;                       % LNS 迭代次数
removeRatio = 0.2;                   % 破坏比率

all_routes = cell(4,1);
all_cost   = zeros(4,1);

%% 2. 对每类垃圾使用 LNS 优化
for k = 1:4
    fprintf('\n=== 类型 %d 优化 ===\n', k);
    % 初始解：最近邻
    unserved = find(w_all(2:end,k)>0)';  % 客户编号
    routes = {};
    loads  = [];
    dist_k = [];
    while ~isempty(unserved)
        load=0; curr=0; r=[0];
        while true
            candidates = unserved(w_all(unserved+1,k) + load <= Q(k));
            if isempty(candidates), break; end
            [~,idx] = min(D(curr+1,candidates+1));
            next = candidates(idx);
            r(end+1) = next;
            load = load + w_all(next+1,k);
            curr = next;
            unserved(unserved==next)=[];
        end
        r(end+1)=0;
        routes{end+1}=r;
        loads(end+1)=load;
        dist_k(end+1)=sum(arrayfun(@(i) D(r(i)+1,r(i+1)+1),1:numel(r)-1));
    end
    curr_routes = routes; curr_cost = sum(dist_k)*C(k);
    best_routes = curr_routes; best_cost = curr_cost;

    % LNS 主循环
    for iter=1:maxIter
                % 破坏：移除部分客户
        new_routes = curr_routes;
        removed = false(size(w_all,1)-1,1);
        for rr = 1:numel(new_routes)
            r = new_routes{rr}(2:end-1);  % 当前路径的客户节点
            if isempty(r)
                continue;  % 路径中无客户，跳过
            end
            m = max(1, floor(removeRatio * numel(r)));
            m = min(m, numel(r));  % 确保不超出
            sel = r(randperm(numel(r), m));
            % 标记并移除
            removed(sel) = true;
            r = setdiff(r, sel);
            new_routes{rr} = [0, r, 0];
        end
        % 修复：插入未服务客户到最优位置，保证所有客户重新安排
        for rr = 1:numel(new_routes)
            r = new_routes{rr};
            toins = find(removed)';
            while ~isempty(toins)
                ii = toins(1);
                bestInc = inf; bestPos = 2;
                for pos = 2:length(r)
                    inc = D(r(pos-1)+1, ii+1) + D(ii+1, r(pos)+1) - D(r(pos-1)+1, r(pos)+1);
                    if inc < bestInc
                        bestInc = inc;
                        bestPos  = pos;
                    end
                end
                % 执行插入
                r = [r(1:bestPos-1), ii, r(bestPos:end)];
                removed(ii) = false;
                toins(1) = [];
            end
            new_routes{rr} = r;
        end
        for rr=1:numel(new_routes)
            r=new_routes{rr};
            toins = find(removed);
            for v=toins'
                bestInc=inf; bestPos=2;
                for pos=2:numel(r)
                    inc = D(r(pos-1)+1,v+1)+D(v+1,r(pos)+1)-D(r(pos-1)+1,r(pos)+1);
                    if inc<bestInc, bestInc=inc; bestPos=pos; end
                end
                r=[r(1:bestPos-1),v,r(bestPos:end)];
                removed(v)=false;
            end
            new_routes{rr}=r;
        end
        % 计算新成本
        new_dist = cellfun(@(r) sum(arrayfun(@(i) D(r(i)+1,r(i+1)+1),1:numel(r)-1)), new_routes);
        new_cost = sum(new_dist)*C(k);
        if new_cost<best_cost
            best_cost = new_cost;
            best_routes = new_routes;
        end
        curr_routes = new_routes;
    end

    all_routes{k}=best_routes;
    all_cost(k)=best_cost;
    fprintf('类型 %d 最优成本: %.2f 元\n',k,best_cost);
end

fprintf('\n== 全部类别 总成本: %.2f 元 ==\n', sum(all_cost));

%% 3. 各类型具体路径输出
%% 3. 各类型具体路径输出
for k=1:4
    fprintf(' 垃圾类型 %d 最终路径 ---', k);
    routes_k = all_routes{k};
    for v=1:numel(routes_k)
        fprintf('类型%d 车%d: %s', k, v, mat2str(routes_k{v}));
    end
end
%% 4. 精美可视化
% 4.1 四类型路径叠加总览
figure('Name','四类型路径总览','NumberTitle','off','Position',[100,100,800,600]); hold on; grid on;
scatter(coords(1,1),coords(1,2),120,'ks','filled'); text(coords(1,1),coords(1,2),'厂区','FontSize',12);
scatter(coords(2:end,1),coords(2:end,2),50,'ko');
cols = lines(4);
lstyles = {'-','--',':','-.'};
for k=1:4
    for v=1:numel(all_routes{k})
        r = all_routes{k}{v} + 1;  % 转为MATLAB索引
        plot(coords(r,1),coords(r,2), 'Color',cols(k,:), 'LineStyle',lstyles{k}, 'LineWidth',1.5);
    end
end
title('四类垃圾运输路径总览','FontSize',14);
legend({'厂区','客户','厨余','可回收','有害','其他'},'Location','eastoutside');

% 4.2 分类型子图展示
figure('Name','分类型路径','NumberTitle','off','Position',[150,150,1000,800]);
for k=1:4
    subplot(2,2,k); hold on; grid on;
    scatter(coords(1,1),coords(1,2),100,'ks','filled'); text(coords(1,1),coords(1,2),'厂区');
    idx = find(w_all(2:end,k)>0)+1;
    scatter(coords(idx,1),coords(idx,2),60,'bo');
    for v=1:numel(all_routes{k})
        r = all_routes{k}{v} + 1;
        plot(coords(r,1),coords(r,2),'-o','LineWidth',1.5);
    end
    title(sprintf('类型%d 运输路径',k)); xlabel('X (km)'); ylabel('Y (km)');
end

% 4.3 成本柱状图
figure('Name','成本对比','NumberTitle','off','Position',[200,200,600,400]);
bar(all_cost); xticks(1:4); xticklabels({'厨余','可回收','有害','其他'});
title('各类运输总成本'); ylabel('成本 (元)');
grid on;


