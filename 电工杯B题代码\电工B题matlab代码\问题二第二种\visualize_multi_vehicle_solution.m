function visualize_multi_vehicle_solution(coords, all_routes, garbage_types, ...
                                        all_costs, all_vehicle_counts)
    
    % 创建子图
    figure('Name', '多车型垃圾运输路径规划结果', 'Position', [50, 50, 1400, 1000]);
    
    % 颜色定义
    colors = {[1,0,0], [0,0,1], [0,0.8,0], [1,0.5,0]}; % 红、蓝、绿、橙
    markers = {'s', 'o', '^', 'd'}; % 方形、圆形、三角、菱形
    
    for k = 1:4
        subplot(2, 2, k);
        hold on;
        
        % 绘制处理厂
        plot(coords(1,1), coords(1,2), 'ks', 'MarkerSize', 12, ...
             'MarkerFaceColor', 'k', 'LineWidth', 2);
        text(coords(1,1)+1, coords(1,2)+1, '处理厂', 'FontSize', 10, ...
             'FontWeight', 'bold');
        
        % 绘制收集点
        for i = 2:size(coords, 1)
            plot(coords(i,1), coords(i,2), markers{k}, 'MarkerSize', 6, ...
                 'MarkerFaceColor', 'w', 'MarkerEdgeColor', colors{k}, ...
                 'LineWidth', 1.5);
            text(coords(i,1)+0.5, coords(i,2)+0.5, num2str(i-1), ...
                 'FontSize', 8);
        end
        
        % 绘制路径
        routes = all_routes{k};
        if ~isempty(routes)
            for v = 1:length(routes)
                route = routes{v};
                
                for i = 1:length(route)-1
                    from_idx = route(i) + 1;
                    to_idx = route(i+1) + 1;
                    
                    if from_idx <= size(coords,1) && to_idx <= size(coords,1)
                        % 绘制路径线
                        plot([coords(from_idx,1), coords(to_idx,1)], ...
                             [coords(from_idx,2), coords(to_idx,2)], ...
                             'Color', colors{k}, 'LineWidth', 2);
                        
                        % 添加方向箭头
                        dx = coords(to_idx,1) - coords(from_idx,1);
                        dy = coords(to_idx,2) - coords(from_idx,2);
                        mid_x = coords(from_idx,1) + 0.7*dx;
                        mid_y = coords(from_idx,2) + 0.7*dy;
                        
                        if dx^2 + dy^2 > 1 % 避免太短的线段
                            quiver(mid_x, mid_y, dx*0.1, dy*0.1, 0, ...
                                   'Color', colors{k}, 'LineWidth', 1.5, ...
                                   'MaxHeadSize', 2);
                        end
                    end
                end
            end
        end
        
        % 设置子图属性
        grid on;
        xlabel('X坐标 (公里)');
        ylabel('Y坐标 (公里)');
        title(sprintf('%s运输路径\n车辆:%d辆, 成本:%.0f元', ...
                     garbage_types{k}, all_vehicle_counts(k), all_costs(k)));
        axis equal;
        xlim([-2, 42]);
        ylim([-2, 38]);
    end
    
    % 添加总标题
    sgtitle(sprintf('多车型垃圾分类运输路径优化结果\n总车辆:%d辆, 总成本:%.0f元', ...
                   sum(all_vehicle_counts), sum(all_costs)), 'FontSize', 14);
    
    % 保存图片
    saveas(gcf, 'Multi_Vehicle_CVRP_Solution.png');
    fprintf('\n结果图已保存为 Multi_Vehicle_CVRP_Solution.png\n');
end
