"""
多车辆协同与载重约束下的优化

解决第二问问题二：多车辆协同与载重约束下的优化
现实中，垃圾分类运输需区分不同垃圾类型（本题中仅考虑4类垃圾，即厨余垃圾、可回收物、有害垃圾、其他垃圾），
每类垃圾需由专用车辆运输（车辆类型1,2,3,4k = 分别对应上述4类垃圾）。
每类车辆的载重限制kQ 、容积限制kV 、单位距离运输成本kC 不同（参数见附件2），
且每个收集点可能产生多种类型的垃圾（各类型垃圾量, 0i k w ，满足i k i k w w = = ）。
车辆从处理厂出发，完成同类型垃圾收集后返回处理厂，不同类型车辆可独立调度。

1）建立以最小化每日总运输成本为目标的多车辆协同运输模型。
2）若附件1中30个收集点调整为产生4类垃圾（数据见附件3），且附件2中kQ，kV，kC给定，
   说明如何将问题一的算法扩展至本问题（需考虑多车辆调度与类型约束），
   给出此问题数学模型，分析模型的约束条件变化，并求出最优解。
3）若增加"车辆每日最大行驶时间"约束，如何修改模型？
   举例说明时间约束对路径规划的影响（如某车辆因时间不足需拆分任务）。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import time

# 1. 数据读取与预处理
# 读取收集点数据
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
points_data = pd.read_excel('B题/附件1.xlsx', skiprows=2, names=col_names)

# 读取附件3中的4类垃圾数据
garbage_data = pd.read_excel('B题/附件3.xlsx', skiprows=1)
garbage_data.columns = ['收集点编号', '厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

# 读取附件2中的车辆参数
vehicle_params = pd.read_excel('B题/附件2.xlsx', skiprows=1)
vehicle_params.columns = ['车辆类型k', '垃圾类型', '载重', '容积', '距离成本', '碳排放系数1', '碳排放系数2']

# 提取坐标
points = points_data[['x', 'y']].values
n = len(points)

# 提取各类垃圾重量和体积
garbage_weights = {}
garbage_volumes = {}
# 垃圾类型映射
garbage_types = {1: '厨余垃圾', 2: '可回收物', 3: '有害垃圾', 4: '其他垃圾'}

for k in range(1, 5):  # 4类垃圾
    garbage_weights[k] = garbage_data[garbage_types[k]].values
    # 假设体积与重量有一定比例关系，可根据实际情况调整
    garbage_volumes[k] = garbage_weights[k] * 1.2  # 假设体积是重量的1.2倍

# 提取车辆参数
Q = {}  # 载重限制
V = {}  # 容积限制
C = {}  # 单位距离成本
for k in range(1, 5):
    Q[k] = vehicle_params.loc[k-1, '载重']
    V[k] = vehicle_params.loc[k-1, '容积']
    C[k] = vehicle_params.loc[k-1, '距离成本']

# 垃圾处理厂坐标
depot = np.array([0, 0])

# 计算距离矩阵
all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 2. 数学模型
"""
多车辆协同与载重约束下的优化模型

决策变量:
x_{i,j,k,v} = 1 表示车辆v(类型k)从点i到点j行驶，否则为0
y_{i,k,v} = 1 表示点i由车辆v(类型k)服务，否则为0

目标函数:
min Z = ∑_{k=1}^4 ∑_{v=1}^{V_k} ∑_{i=0}^n ∑_{j=0,j≠i}^n C_k * d_{i,j} * x_{i,j,k,v}

约束条件:
1. 每个收集点的每类垃圾只能由一辆对应类型的车辆服务:
   ∑_{v=1}^{V_k} y_{i,k,v} = 1, ∀i∈{1,2,...,n}, ∀k∈{1,2,3,4} 且 w_{i,k} > 0

2. 车辆载重约束:
   ∑_{i=1}^n w_{i,k} * y_{i,k,v} ≤ Q_k, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

3. 车辆容积约束:
   ∑_{i=1}^n v_{i,k} * y_{i,k,v} ≤ V_k, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

4. 流量守恒约束:
   ∑_{j=0,j≠i}^n x_{i,j,k,v} = ∑_{j=0,j≠i}^n x_{j,i,k,v} = y_{i,k,v},
   ∀i∈{1,2,...,n}, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

5. 子回路消除约束:
   ∑_{i,j∈S} x_{i,j,k,v} ≤ |S| - 1, ∀S⊂{1,2,...,n}, |S|≥2, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

6. 车辆类型约束:
   每类垃圾只能由对应类型的车辆运输

7. 时间约束(扩展):
   ∑_{i=0}^n ∑_{j=0,j≠i}^n (d_{i,j}/speed + service_time * y_{i,k,v}) * x_{i,j,k,v} ≤ max_time,
   ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}
"""

# 3. 节约算法求解每类垃圾的路径
def savings_algorithm(garbage_type):
    weights = garbage_weights[garbage_type]
    volumes = garbage_volumes[garbage_type]
    max_weight = Q[garbage_type]
    max_volume = V[garbage_type]

    # 初始化：每个点一个单独路径
    valid_points = [i+1 for i in range(n) if weights[i] > 0]  # 只考虑有该类垃圾的点
    routes = [[i] for i in valid_points]
    route_weights = [weights[i-1] for i in valid_points]
    route_volumes = [volumes[i-1] for i in valid_points]

    # 计算节约值
    savings = []
    for i in valid_points:
        for j in valid_points:
            if i != j:
                s = dist_matrix[i, 0] + dist_matrix[j, 0] - dist_matrix[i, j]
                savings.append((i, j, s))

    # 按节约值降序排序
    savings.sort(key=lambda x: x[2], reverse=True)

    # 合并路径
    def find_route(routes, node):
        for idx, route in enumerate(routes):
            if node in route:
                return idx
        return None

    for i, j, s in savings:
        ri = find_route(routes, i)
        rj = find_route(routes, j)

        if ri is not None and rj is not None and ri != rj:
            route_i = routes[ri]
            route_j = routes[rj]

            # 检查是否可以合并（端点相连且不超过载重和容积限制）
            merged_weight = route_weights[ri] + route_weights[rj]
            merged_volume = route_volumes[ri] + route_volumes[rj]

            if merged_weight <= max_weight and merged_volume <= max_volume:
                # 检查端点连接情况
                if route_i[-1] == i and route_j[0] == j:
                    routes[ri] = route_i + route_j
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[0] == i and route_j[-1] == j:
                    routes[ri] = route_j + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[0] == i and route_j[0] == j:
                    routes[ri] = route_j[::-1] + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[-1] == i and route_j[-1] == j:
                    routes[ri] = route_i + route_j[::-1]
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]

    return routes, route_weights, route_volumes

# 4. 计算路径成本
def calculate_route_cost(route, garbage_type):
    cost = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        cost += dist_matrix[prev, node] * C[garbage_type]
        prev = node
    # 返回垃圾处理厂
    cost += dist_matrix[prev, 0] * C[garbage_type]
    return cost

# 5. 考虑时间约束的路径规划
def calculate_route_time(route, speed=40):  # 速度单位：km/h
    distance = 0
    prev = 0
    for node in route:
        distance += dist_matrix[prev, node]
        prev = node
    distance += dist_matrix[prev, 0]  # 返回垃圾处理厂

    # 假设每个点停留时间为10分钟(0.167小时)
    stop_time = len(route) * 0.167

    # 总时间 = 行驶时间 + 停留时间
    total_time = distance / speed + stop_time
    return total_time

# 考虑时间约束的路径规划
def time_constrained_routing(garbage_type, max_time=8):  # 最大行驶时间8小时
    routes, weights, volumes = savings_algorithm(garbage_type)
    new_routes = []
    new_weights = []
    new_volumes = []

    for i, route in enumerate(routes):
        route_time = calculate_route_time(route)

        if route_time <= max_time:
            # 如果路径时间不超过限制，直接添加
            new_routes.append(route)
            new_weights.append(weights[i])
            new_volumes.append(volumes[i])
        else:
            # 如果超过时间限制，需要拆分路径
            current_route = []
            current_weight = 0
            current_volume = 0

            for node in route:
                # 计算添加当前节点后的时间
                temp_route = current_route + [node]
                temp_time = calculate_route_time(temp_route)

                if temp_time <= max_time:
                    # 可以添加当前节点
                    current_route.append(node)
                    current_weight += garbage_weights[garbage_type][node-1]
                    current_volume += garbage_volumes[garbage_type][node-1]
                else:
                    # 需要开始新的路径
                    if current_route:
                        new_routes.append(current_route)
                        new_weights.append(current_weight)
                        new_volumes.append(current_volume)

                    # 开始新路径
                    current_route = [node]
                    current_weight = garbage_weights[garbage_type][node-1]
                    current_volume = garbage_volumes[garbage_type][node-1]

            # 添加最后一个路径
            if current_route:
                new_routes.append(current_route)
                new_weights.append(current_weight)
                new_volumes.append(current_volume)

    return new_routes, new_weights, new_volumes

# 6. 结果可视化
def plot_routes(routes_dict, title):
    plt.figure(figsize=(12, 10))
    plt.scatter(0, 0, c='red', s=200, marker='*', label='垃圾处理厂')

    # 绘制收集点
    for i in range(n):
        plt.scatter(points[i, 0], points[i, 1], c='blue', s=100)
        plt.text(points[i, 0]+0.1, points[i, 1]+0.1, f'{i+1}', fontsize=12)

    # 绘制路径
    colors = ['g', 'm', 'c', 'y']
    for k in range(1, 5):
        for route in routes_dict[k]:
            prev = 0  # 从垃圾处理厂出发
            for node in route:
                plt.plot([all_points[prev, 0], all_points[node, 0]],
                         [all_points[prev, 1], all_points[node, 1]],
                         c=colors[k-1], linewidth=2)
                prev = node
            # 返回垃圾处理厂
            plt.plot([all_points[prev, 0], all_points[0, 0]],
                     [all_points[prev, 1], all_points[0, 1]],
                     c=colors[k-1], linewidth=2)

    plt.title(title)
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.grid(True)
    plt.legend()
    plt.savefig(f'{title}.png', dpi=300)
    plt.show()

# 7. 主函数
if __name__ == "__main__":
    print("解决第二问问题二：多车辆协同与载重约束下的优化")
    print("\n1. 建立以最小化每日总运输成本为目标的多车辆协同运输模型")
    print("数学模型已在代码注释中给出")

    print("\n2. 求解多车辆协同与载重约束下的优化问题")
    start_time = time.time()

    # 求解所有垃圾类型的路径
    all_routes = {}
    all_costs = {}
    total_cost = 0

    for k in range(1, 5):
        print(f"\n处理第{k}类垃圾...")
        routes, weights, volumes = savings_algorithm(k)

        # 计算该类垃圾的总成本
        type_cost = 0
        for route in routes:
            route_cost = calculate_route_cost(route, k)
            type_cost += route_cost

        all_routes[k] = routes
        all_costs[k] = type_cost
        total_cost += type_cost

        print(f"第{k}类垃圾共需{len(routes)}辆车，总成本: {type_cost:.2f}")

    print(f"\n总运输成本: {total_cost:.2f}")
    print(f"求解时间: {time.time() - start_time:.2f}秒")

    # 绘制不考虑时间约束的路径
    plot_routes(all_routes, '多车辆协同与载重约束下的优化路径')

    print("\n3. 考虑时间约束的模型修改")
    print("时间约束的数学模型已在代码注释中给出")

    # 考虑时间约束的总成本计算
    print("\n考虑时间约束(8小时)后的结果:")
    time_constrained_routes = {}
    time_constrained_costs = {}
    time_constrained_total_cost = 0

    for k in range(1, 5):
        routes, weights, volumes = time_constrained_routing(k, max_time=8)

        # 计算该类垃圾的总成本
        type_cost = 0
        for route in routes:
            route_cost = calculate_route_cost(route, k)
            type_cost += route_cost

        time_constrained_routes[k] = routes
        time_constrained_costs[k] = type_cost
        time_constrained_total_cost += type_cost

        print(f"第{k}类垃圾共需{len(routes)}辆车，总成本: {type_cost:.2f}")

    print(f"\n考虑时间约束后的总运输成本: {time_constrained_total_cost:.2f}")

    # 绘制考虑时间约束的路径
    plot_routes(time_constrained_routes, '考虑时间约束的多车辆协同路径')

    # 输出详细结果
    print("\n详细路径规划结果:")
    for k in range(1, 5):
        print(f"\n第{k}类垃圾路径规划:")
        for i, route in enumerate(all_routes[k]):
            route_str = ' -> '.join(str(r) for r in route)
            route_cost = calculate_route_cost(route, k)
            route_time = calculate_route_time(route)
            print(f"路径{i+1}: 0 -> {route_str} -> 0")
            print(f"  成本: {route_cost:.2f}, 时间: {route_time:.2f}小时")

    print("\n考虑时间约束后的详细路径规划结果:")
    for k in range(1, 5):
        print(f"\n第{k}类垃圾路径规划(时间约束):")
        for i, route in enumerate(time_constrained_routes[k]):
            route_str = ' -> '.join(str(r) for r in route)
            route_cost = calculate_route_cost(route, k)
            route_time = calculate_route_time(route)
            print(f"路径{i+1}: 0 -> {route_str} -> 0")
            print(f"  成本: {route_cost:.2f}, 时间: {route_time:.2f}小时")

    # 分析时间约束对路径规划的影响
    print("\n时间约束对路径规划的影响分析:")
    for k in range(1, 5):
        original_routes = len(all_routes[k])
        constrained_routes = len(time_constrained_routes[k])

        if constrained_routes > original_routes:
            print(f"第{k}类垃圾: 由于时间约束，路径数量从{original_routes}增加到{constrained_routes}")

            # 找出被拆分的路径示例
            for i, route in enumerate(all_routes[k]):
                route_time = calculate_route_time(route)
                if route_time > 8:  # 超过8小时的路径
                    route_str = ' -> '.join(str(r) for r in route)
                    print(f"  示例: 路径 0 -> {route_str} -> 0 (时间: {route_time:.2f}小时) 需要拆分")

                    # 找出拆分后的路径
                    split_routes = []
                    for new_route in time_constrained_routes[k]:
                        if set(new_route).issubset(set(route)):
                            split_routes.append(new_route)

                    if split_routes:
                        print("  拆分为:")
                        for j, split_route in enumerate(split_routes):
                            split_str = ' -> '.join(str(r) for r in split_route)
                            split_time = calculate_route_time(split_route)
                            print(f"    路径 0 -> {split_str} -> 0 (时间: {split_time:.2f}小时)")

                    break  # 只显示一个示例
        else:
            print(f"第{k}类垃圾: 时间约束未导致路径拆分，路径数量保持为{original_routes}")
