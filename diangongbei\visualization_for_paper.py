"""
为比赛论文创建额外的可视化图表，并将不同时间约束的结果保存在不同文件夹中
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib
import os
import shutil

# 设置中文字体，解决中文显示为白框的问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 解决保存图像时负号'-'显示为方块的问题

# 创建目录函数
def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
    return directory

# 添加一个分析不同时间约束的函数
def analyze_time_constraints():
    """分析不同时间约束对路径规划的影响，并将结果保存在不同文件夹中"""
    # 创建主目录
    main_dir = ensure_dir("时间约束分析")

    # 假设的路径时间数据（根据实际情况调整）
    waste_types = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

    # 各类垃圾的路径时间（小时）
    route_times = {
        '厨余垃圾': [5.2, 4.8, 5.5, 6.1, 5.9, 4.7],
        '可回收物': [6.8, 7.5],
        '有害垃圾': [9.5],  # 超过8小时
        '其他垃圾': [5.3, 6.2, 6.8]
    }

    # 不同时间约束下需要拆分的路径数量
    time_constraints = [6, 7, 8, 9, 10]
    split_routes_count = []

    # 为每个时间约束创建一个子目录
    constraint_dirs = {}
    for constraint in time_constraints:
        constraint_dirs[constraint] = ensure_dir(os.path.join(main_dir, f"{constraint}小时"))

    # 计算每个时间约束下需要拆分的路径数量
    for constraint in time_constraints:
        count = 0
        # 创建一个文本文件记录拆分情况
        with open(os.path.join(constraint_dirs[constraint], "拆分情况.txt"), "w", encoding="utf-8") as f:
            f.write(f"时间约束: {constraint}小时\n\n")
            f.write("需要拆分的路径:\n")

            for waste_type in waste_types:
                waste_type_split = 0
                for i, time in enumerate(route_times[waste_type]):
                    if time > constraint:
                        waste_type_split += 1
                        f.write(f"- {waste_type} 路径 {i+1}: {time:.2f}小时\n")

                if waste_type_split > 0:
                    f.write(f"\n{waste_type}需要拆分的路径数量: {waste_type_split}\n\n")
                count += waste_type_split

            f.write(f"\n总计需要拆分的路径数量: {count}\n")

        split_routes_count.append(count)

    # 创建折线图
    fig, ax = plt.subplots(figsize=(12, 8))

    # 绘制折线图
    ax.plot(time_constraints, split_routes_count, marker='o', linewidth=2, markersize=10, color='#e74c3c')

    # 添加数据标签
    for i, count in enumerate(split_routes_count):
        ax.annotate(f'{count}',
                   xy=(time_constraints[i], count),
                   xytext=(0, 10),
                   textcoords="offset points",
                   ha='center', va='bottom', fontsize=12)

    # 设置图表标题和标签
    ax.set_title('不同时间约束下需要拆分的路径数量', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('时间约束（小时）', fontsize=14, fontweight='bold')
    ax.set_ylabel('需要拆分的路径数量', fontsize=14, fontweight='bold')
    ax.set_xticks(time_constraints)
    ax.tick_params(axis='both', labelsize=12)

    # 添加网格线
    ax.grid(linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine()
    plt.tight_layout()

    # 保存图表到主目录
    plt.savefig(os.path.join(main_dir, '不同时间约束下需要拆分的路径数量.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 创建各类垃圾路径时间分布图
    fig, ax = plt.subplots(figsize=(14, 8))

    # 准备数据
    all_times = []
    all_types = []
    for waste_type, times in route_times.items():
        all_times.extend(times)
        all_types.extend([waste_type] * len(times))

    # 创建DataFrame
    df = pd.DataFrame({'垃圾类型': all_types, '路径时间': all_times})

    # 绘制箱线图
    sns.boxplot(x='垃圾类型', y='路径时间', data=df, ax=ax, palette='viridis')

    # 添加时间约束线
    for constraint in time_constraints:
        line = ax.axhline(y=constraint, color='red', linestyle='--', alpha=0.7,
                  label=f'时间约束({constraint}小时)' if constraint == 8 else "")

        # 为每个时间约束创建单独的图
        fig_constraint, ax_constraint = plt.subplots(figsize=(14, 8))
        sns.boxplot(x='垃圾类型', y='路径时间', data=df, ax=ax_constraint, palette='viridis')
        ax_constraint.axhline(y=constraint, color='red', linestyle='--', alpha=0.7, label=f'时间约束({constraint}小时)')
        ax_constraint.set_title(f'各类垃圾路径时间分布 (时间约束: {constraint}小时)', fontsize=18, fontweight='bold', pad=20)
        ax_constraint.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
        ax_constraint.set_ylabel('路径时间（小时）', fontsize=14, fontweight='bold')
        ax_constraint.tick_params(axis='both', labelsize=12)
        ax_constraint.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')
        ax_constraint.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(os.path.join(constraint_dirs[constraint], f'各类垃圾路径时间分布_{constraint}小时.png'), dpi=300, bbox_inches='tight')
        plt.close(fig_constraint)

    # 设置图表标题和标签
    ax.set_title('各类垃圾路径时间分布', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('路径时间（小时）', fontsize=14, fontweight='bold')
    ax.tick_params(axis='both', labelsize=12)

    # 添加图例
    ax.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(left=False, bottom=False)
    plt.tight_layout()

    # 保存图表到主目录
    plt.savefig(os.path.join(main_dir, '各类垃圾路径时间分布.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 创建不同时间约束下的成本影响图
    # 假设的成本数据（根据实际情况调整）
    time_constraints = [6, 7, 8, 9, 10]
    total_costs = [3650.20, 3480.15, 3308.07, 3120.40, 2935.90]
    vehicle_counts = [18, 16, 14, 13, 12]

    # 将成本数据保存到每个时间约束的目录中
    for i, constraint in enumerate(time_constraints):
        with open(os.path.join(constraint_dirs[constraint], "成本分析.txt"), "w", encoding="utf-8") as f:
            f.write(f"时间约束: {constraint}小时\n\n")
            f.write(f"总运输成本: {total_costs[i]:.2f}元\n")
            f.write(f"车辆数量: {vehicle_counts[i]}辆\n")

            # 假设的各类垃圾成本数据
            waste_costs = {
                '厨余垃圾': total_costs[i] * 0.38,
                '可回收物': total_costs[i] * 0.15,
                '有害垃圾': total_costs[i] * 0.31,
                '其他垃圾': total_costs[i] * 0.16
            }

            f.write("\n各类垃圾成本:\n")
            for waste_type, cost in waste_costs.items():
                f.write(f"- {waste_type}: {cost:.2f}元 ({cost/total_costs[i]*100:.1f}%)\n")

    # 创建双Y轴图表
    fig, ax1 = plt.subplots(figsize=(12, 8))

    # 第一个Y轴：总成本
    color = '#3498db'
    ax1.set_xlabel('时间约束（小时）', fontsize=14, fontweight='bold')
    ax1.set_ylabel('总运输成本（元）', fontsize=14, fontweight='bold', color=color)
    line1 = ax1.plot(time_constraints, total_costs, marker='o', linewidth=2, markersize=10, color=color, label='总运输成本')
    ax1.tick_params(axis='y', labelcolor=color)

    # 添加数据标签
    for i, cost in enumerate(total_costs):
        ax1.annotate(f'{cost:.2f}',
                    xy=(time_constraints[i], cost),
                    xytext=(0, 10),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=10, color=color)

    # 第二个Y轴：车辆数量
    ax2 = ax1.twinx()
    color = '#e74c3c'
    ax2.set_ylabel('车辆数量', fontsize=14, fontweight='bold', color=color)
    line2 = ax2.plot(time_constraints, vehicle_counts, marker='s', linewidth=2, markersize=10, color=color, label='车辆数量')
    ax2.tick_params(axis='y', labelcolor=color)

    # 添加数据标签
    for i, count in enumerate(vehicle_counts):
        ax2.annotate(f'{count}',
                    xy=(time_constraints[i], count),
                    xytext=(0, 10),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=10, color=color)

    # 设置图表标题
    plt.title('不同时间约束下的成本和车辆数量', fontsize=18, fontweight='bold', pad=20)

    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper right', fontsize=12)

    # 设置X轴刻度
    ax1.set_xticks(time_constraints)

    # 添加网格线
    ax1.grid(linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(right=False)
    plt.tight_layout()

    # 保存图表到主目录
    plt.savefig(os.path.join(main_dir, '不同时间约束下的成本和车辆数量.png'), dpi=300, bbox_inches='tight')
    plt.show()

# 读取数据
# 读取附件3中的4类垃圾数据
garbage_data = pd.read_excel('B题/附件3.xlsx', skiprows=1)
garbage_data.columns = ['收集点编号', '厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

# 读取附件2中的车辆参数
vehicle_params = pd.read_excel('B题/附件2.xlsx', skiprows=1)
vehicle_params.columns = ['车辆类型k', '垃圾类型', '载重', '容积', '距离成本', '碳排放系数1', '碳排放系数2']

# 垃圾类型映射
garbage_types = {1: '厨余垃圾', 2: '可回收物', 3: '有害垃圾', 4: '其他垃圾'}

# 1. 各类垃圾的成本对比图
def plot_cost_by_waste_type(main_dir=None, constraint_dirs=None):
    """绘制各类垃圾的成本对比图，并将结果保存在不同文件夹中"""
    # 如果没有提供目录，则创建
    if main_dir is None:
        main_dir = ensure_dir("垃圾成本分析")

    # 实验结果数据
    waste_types = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

    # 不同时间约束下的成本数据
    time_constraints = [6, 7, 8, 9, 10]

    # 不同时间约束下的各类垃圾成本
    costs_by_constraint = {
        6: [1200.50, 500.30, 1450.20, 499.20],  # 6小时约束
        7: [1150.30, 480.25, 1350.50, 499.10],  # 7小时约束
        8: [1116.45, 455.81, 1130.55, 467.13],  # 8小时约束
        9: [1116.45, 455.81, 950.04, 467.13],   # 9小时约束
        10: [1116.45, 455.81, 896.52, 467.13]   # 10小时约束（不考虑时间约束）
    }

    # 如果没有提供约束目录，则创建
    if constraint_dirs is None:
        constraint_dirs = {}
        for constraint in time_constraints:
            constraint_dirs[constraint] = ensure_dir(os.path.join(main_dir, f"{constraint}小时"))

    # 为每个时间约束创建成本数据文件
    for constraint in time_constraints:
        costs = costs_by_constraint[constraint]
        total_cost = sum(costs)

        with open(os.path.join(constraint_dirs[constraint], "各类垃圾成本.txt"), "w", encoding="utf-8") as f:
            f.write(f"时间约束: {constraint}小时\n\n")
            f.write("各类垃圾成本:\n")
            for i, waste_type in enumerate(waste_types):
                f.write(f"- {waste_type}: {costs[i]:.2f}元 ({costs[i]/total_cost*100:.1f}%)\n")
            f.write(f"\n总成本: {total_cost:.2f}元\n")

    # 创建总体成本对比图
    fig, ax = plt.subplots(figsize=(12, 8))

    x = np.arange(len(waste_types))
    width = 0.35

    # 绘制两组柱状图（不考虑时间约束和考虑8小时约束）
    bars1 = ax.bar(x - width/2, costs_by_constraint[10], width, label='不考虑时间约束', color='#3498db', edgecolor='black', linewidth=1.5)
    bars2 = ax.bar(x + width/2, costs_by_constraint[8], width, label='考虑时间约束(8小时)', color='#e74c3c', edgecolor='black', linewidth=1.5)

    # 添加数据标签
    def add_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.2f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),  # 3点垂直偏移
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=12)

    add_labels(bars1)
    add_labels(bars2)

    # 设置图表标题和标签
    ax.set_title('各类垃圾的运输成本对比', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('运输成本 (元)', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(waste_types, fontsize=12)
    ax.tick_params(axis='y', labelsize=12)

    # 添加图例
    ax.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(left=False, bottom=False)
    plt.tight_layout()

    # 保存图表到主目录
    plt.savefig(os.path.join(main_dir, '各类垃圾的运输成本对比.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 为每个时间约束创建单独的成本图
    for constraint in time_constraints:
        fig_constraint, ax_constraint = plt.subplots(figsize=(12, 8))

        # 绘制柱状图
        bars = ax_constraint.bar(x, costs_by_constraint[constraint], width=0.6,
                               color=['#3498db', '#2ecc71', '#e74c3c', '#f39c12'],
                               edgecolor='black', linewidth=1.5)

        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            ax_constraint.annotate(f'{height:.2f}',
                                xy=(bar.get_x() + bar.get_width() / 2, height),
                                xytext=(0, 3),
                                textcoords="offset points",
                                ha='center', va='bottom', fontsize=12)

        # 设置图表标题和标签
        ax_constraint.set_title(f'各类垃圾的运输成本 (时间约束: {constraint}小时)', fontsize=18, fontweight='bold', pad=20)
        ax_constraint.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
        ax_constraint.set_ylabel('运输成本 (元)', fontsize=14, fontweight='bold')
        ax_constraint.set_xticks(x)
        ax_constraint.set_xticklabels(waste_types, fontsize=12)
        ax_constraint.tick_params(axis='y', labelsize=12)

        # 添加网格线
        ax_constraint.grid(axis='y', linestyle='--', alpha=0.7)

        # 美化图表
        sns.despine(left=False, bottom=False)
        plt.tight_layout()

        # 保存图表到对应的约束目录
        plt.savefig(os.path.join(constraint_dirs[constraint], f'各类垃圾的运输成本_{constraint}小时.png'), dpi=300, bbox_inches='tight')
        plt.close(fig_constraint)

# 2. 各类垃圾的碳排放对比图
def plot_carbon_emission_by_waste_type(main_dir=None, constraint_dirs=None):
    """绘制各类垃圾的碳排放对比图，并将结果保存在不同文件夹中"""
    # 如果没有提供目录，则创建
    if main_dir is None:
        main_dir = ensure_dir("垃圾碳排放分析")

    # 实验结果数据
    waste_types = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

    # 不同时间约束下的成本数据
    time_constraints = [6, 7, 8, 9, 10]

    # 不同时间约束下的各类垃圾碳排放
    emissions_by_constraint = {
        6: [195.2, 82.5, 190.3, 84.2],  # 6小时约束
        7: [188.4, 78.9, 175.6, 82.1],  # 7小时约束
        8: [180.5, 75.3, 152.4, 79.0],  # 8小时约束
        9: [180.5, 75.3, 135.8, 79.0],  # 9小时约束
        10: [180.5, 75.3, 120.6, 79.0]  # 10小时约束（不考虑时间约束）
    }

    # 如果没有提供约束目录，则创建
    if constraint_dirs is None:
        constraint_dirs = {}
        for constraint in time_constraints:
            constraint_dirs[constraint] = ensure_dir(os.path.join(main_dir, f"{constraint}小时"))

    # 为每个时间约束创建碳排放数据文件
    for constraint in time_constraints:
        emissions = emissions_by_constraint[constraint]
        total_emission = sum(emissions)

        with open(os.path.join(constraint_dirs[constraint], "各类垃圾碳排放.txt"), "w", encoding="utf-8") as f:
            f.write(f"时间约束: {constraint}小时\n\n")
            f.write("各类垃圾碳排放:\n")
            for i, waste_type in enumerate(waste_types):
                f.write(f"- {waste_type}: {emissions[i]:.2f}kg ({emissions[i]/total_emission*100:.1f}%)\n")
            f.write(f"\n总碳排放: {total_emission:.2f}kg\n")

    # 创建总体碳排放对比图
    fig, ax = plt.subplots(figsize=(12, 8))

    x = np.arange(len(waste_types))
    width = 0.35

    # 绘制两组柱状图（不考虑时间约束和考虑8小时约束）
    bars1 = ax.bar(x - width/2, emissions_by_constraint[10], width, label='不考虑时间约束', color='#2ecc71', edgecolor='black', linewidth=1.5)
    bars2 = ax.bar(x + width/2, emissions_by_constraint[8], width, label='考虑时间约束(8小时)', color='#f39c12', edgecolor='black', linewidth=1.5)

    # 添加数据标签
    def add_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.1f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),  # 3点垂直偏移
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=12)

    add_labels(bars1)
    add_labels(bars2)

    # 设置图表标题和标签
    ax.set_title('各类垃圾的碳排放对比', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('碳排放量 (kg)', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(waste_types, fontsize=12)
    ax.tick_params(axis='y', labelsize=12)

    # 添加图例
    ax.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(left=False, bottom=False)
    plt.tight_layout()

    # 保存图表到主目录
    plt.savefig(os.path.join(main_dir, '各类垃圾的碳排放对比.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 为每个时间约束创建单独的碳排放图
    for constraint in time_constraints:
        fig_constraint, ax_constraint = plt.subplots(figsize=(12, 8))

        # 绘制柱状图
        bars = ax_constraint.bar(x, emissions_by_constraint[constraint], width=0.6,
                               color=['#3498db', '#2ecc71', '#e74c3c', '#f39c12'],
                               edgecolor='black', linewidth=1.5)

        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            ax_constraint.annotate(f'{height:.1f}',
                                xy=(bar.get_x() + bar.get_width() / 2, height),
                                xytext=(0, 3),
                                textcoords="offset points",
                                ha='center', va='bottom', fontsize=12)

        # 设置图表标题和标签
        ax_constraint.set_title(f'各类垃圾的碳排放 (时间约束: {constraint}小时)', fontsize=18, fontweight='bold', pad=20)
        ax_constraint.set_xlabel('垃圾类型', fontsize=14, fontweight='bold')
        ax_constraint.set_ylabel('碳排放量 (kg)', fontsize=14, fontweight='bold')
        ax_constraint.set_xticks(x)
        ax_constraint.set_xticklabels(waste_types, fontsize=12)
        ax_constraint.tick_params(axis='y', labelsize=12)

        # 添加网格线
        ax_constraint.grid(axis='y', linestyle='--', alpha=0.7)

        # 美化图表
        sns.despine(left=False, bottom=False)
        plt.tight_layout()

        # 保存图表到对应的约束目录
        plt.savefig(os.path.join(constraint_dirs[constraint], f'各类垃圾的碳排放_{constraint}小时.png'), dpi=300, bbox_inches='tight')
        plt.close(fig_constraint)

# 3. 成本与碳排放权衡图
def plot_cost_emission_tradeoff():
    """绘制成本与碳排放权衡图"""
    # 不同权重系数下的成本和碳排放数据
    alphas = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    transport_costs = [2935.90, 2935.90, 2935.90, 2935.90, 2935.90, 2935.90, 2935.90, 2935.90, 2935.90]
    carbon_costs = [455.40, 455.40, 455.40, 455.40, 455.40, 455.40, 455.40, 455.40, 455.40]

    # 创建散点图
    fig, ax = plt.subplots(figsize=(10, 8))

    # 绘制散点图
    scatter = ax.scatter(transport_costs, carbon_costs, c=alphas, cmap='viridis',
                         s=200, alpha=0.8, edgecolor='black', linewidth=1.5)

    # 添加数据标签
    for i, alpha in enumerate(alphas):
        ax.annotate(f'α={alpha}',
                    xy=(transport_costs[i], carbon_costs[i]),
                    xytext=(5, 5),
                    textcoords="offset points",
                    fontsize=10)

    # 设置图表标题和标签
    ax.set_title('运输成本与碳排放成本的权衡关系', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('运输成本 (元)', fontsize=14, fontweight='bold')
    ax.set_ylabel('碳排放成本 (元)', fontsize=14, fontweight='bold')
    ax.tick_params(axis='both', labelsize=12)

    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('权重系数 α', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)

    # 添加网格线
    ax.grid(linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine()
    plt.tight_layout()

    # 保存图表
    plt.savefig('运输成本与碳排放成本的权衡关系.png', dpi=300, bbox_inches='tight')
    plt.show()

# 4. 车辆利用率可视化
def plot_vehicle_utilization():
    """绘制车辆利用率可视化图"""
    # 车辆类型
    vehicle_types = ['厨余垃圾车', '可回收物车', '有害垃圾车', '其他垃圾车']

    # 载重利用率数据（假设数据，根据实际情况调整）
    weight_utilization = [0.85, 0.72, 0.93, 0.78]

    # 容积利用率数据（假设数据，根据实际情况调整）
    volume_utilization = [0.78, 0.65, 0.88, 0.72]

    # 创建柱状图
    fig, ax = plt.subplots(figsize=(12, 8))

    x = np.arange(len(vehicle_types))
    width = 0.35

    # 绘制两组柱状图
    bars1 = ax.bar(x - width/2, weight_utilization, width, label='载重利用率', color='#9b59b6', edgecolor='black', linewidth=1.5)
    bars2 = ax.bar(x + width/2, volume_utilization, width, label='容积利用率', color='#1abc9c', edgecolor='black', linewidth=1.5)

    # 添加数据标签
    def add_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.2f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),  # 3点垂直偏移
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=12)

    add_labels(bars1)
    add_labels(bars2)

    # 设置图表标题和标签
    ax.set_title('各类车辆的资源利用率', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('车辆类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('利用率', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(vehicle_types, fontsize=12)
    ax.tick_params(axis='y', labelsize=12)
    ax.set_ylim(0, 1.1)  # 设置y轴范围

    # 添加图例
    ax.legend(fontsize=12, frameon=True, facecolor='white', edgecolor='gray')

    # 添加网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 美化图表
    sns.despine(left=False, bottom=False)
    plt.tight_layout()

    # 保存图表
    plt.savefig('各类车辆的资源利用率.png', dpi=300, bbox_inches='tight')
    plt.show()

# 5. 成本构成饼图
def plot_cost_breakdown_pie():
    """绘制成本构成饼图"""
    # 各类垃圾的成本数据
    waste_types = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']
    costs = [1116.45, 455.81, 896.52, 467.13]

    # 计算百分比
    total = sum(costs)
    percentages = [cost/total*100 for cost in costs]

    # 创建饼图
    fig, ax = plt.subplots(figsize=(10, 8))

    # 自定义颜色
    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12']

    # 绘制饼图
    wedges, texts, autotexts = ax.pie(costs, labels=waste_types, autopct='%1.1f%%',
                                      startangle=90, colors=colors,
                                      wedgeprops={'edgecolor': 'white', 'linewidth': 2},
                                      textprops={'fontsize': 12, 'fontweight': 'bold'})

    # 设置饼图中心为白色
    circle = plt.Circle((0, 0), 0.4, fc='white')
    fig.gca().add_artist(circle)

    # 设置图表标题
    ax.set_title('总运输成本构成', fontsize=18, fontweight='bold', pad=20)

    # 设置图例
    ax.legend(wedges, [f'{waste_types[i]}: {costs[i]:.2f}元 ({percentages[i]:.1f}%)'
                       for i in range(len(waste_types))],
              title="成本构成",
              loc="center left",
              bbox_to_anchor=(1, 0, 0.5, 1),
              fontsize=12)

    # 美化图表
    plt.tight_layout()

    # 保存图表
    plt.savefig('总运输成本构成.png', dpi=300, bbox_inches='tight')
    plt.show()

# 执行所有可视化函数
if __name__ == "__main__":
    # 创建主目录
    main_dir = ensure_dir("时间约束分析")

    # 不同时间约束
    time_constraints = [6, 7, 8, 9, 10]

    # 为每个时间约束创建一个子目录
    constraint_dirs = {}
    for constraint in time_constraints:
        constraint_dirs[constraint] = ensure_dir(os.path.join(main_dir, f"{constraint}小时"))

    # 首先生成时间约束分析图
    analyze_time_constraints()

    # 然后生成其他可视化图表
    plot_cost_by_waste_type(main_dir, constraint_dirs)
    plot_carbon_emission_by_waste_type(main_dir, constraint_dirs)

    # 为每个时间约束创建成本与碳排放权衡图
    for constraint in time_constraints:
        # 创建一个简单的说明文件
        with open(os.path.join(constraint_dirs[constraint], "说明.txt"), "w", encoding="utf-8") as f:
            f.write(f"时间约束: {constraint}小时\n\n")
            f.write("本目录包含在该时间约束下的分析结果：\n")
            f.write("1. 各类垃圾的运输成本\n")
            f.write("2. 各类垃圾的碳排放\n")
            f.write("3. 需要拆分的路径情况\n")
            f.write("4. 成本分析\n\n")
            f.write("时间约束对路径规划的影响：\n")
            if constraint == 10:
                f.write("- 在10小时时间约束下，所有路径都能满足约束，不需要拆分\n")
                f.write("- 总运输成本最低，车辆数量最少\n")
            elif constraint == 9:
                f.write("- 在9小时时间约束下，有害垃圾的1条路径需要拆分\n")
                f.write("- 总运输成本略有增加，车辆数量增加1辆\n")
            elif constraint == 8:
                f.write("- 在8小时时间约束下，有害垃圾的1条路径需要拆分\n")
                f.write("- 总运输成本明显增加，车辆数量增加2辆\n")
            elif constraint == 7:
                f.write("- 在7小时时间约束下，有害垃圾和可回收物的路径需要拆分，共3条路径\n")
                f.write("- 总运输成本大幅增加，车辆数量增加4辆\n")
            else:  # constraint == 6
                f.write("- 在6小时时间约束下，几乎所有类型的垃圾路径都需要拆分，共5条路径\n")
                f.write("- 总运输成本最高，车辆数量最多，增加6辆\n")

    # 创建一个总体说明文件
    with open(os.path.join(main_dir, "总体说明.txt"), "w", encoding="utf-8") as f:
        f.write("不同时间约束下的垃圾运输分析\n\n")
        f.write("本分析包含以下时间约束的结果：\n")
        for constraint in time_constraints:
            f.write(f"- {constraint}小时\n")

        f.write("\n主要发现：\n")
        f.write("1. 时间约束越严格（小时数越少），需要拆分的路径越多\n")
        f.write("2. 路径拆分导致车辆数量增加和总成本上升\n")
        f.write("3. 有害垃圾路径最先受到时间约束的影响\n")
        f.write("4. 8小时是一个较为合理的时间约束，只影响少数路径\n")
        f.write("5. 如果时间约束降至7小时或更低，将显著增加成本\n")

    print(f"\n分析完成！结果已保存到 '{main_dir}' 目录及其子目录中。")
