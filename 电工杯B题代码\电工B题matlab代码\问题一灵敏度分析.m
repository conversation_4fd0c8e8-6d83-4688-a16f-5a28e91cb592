%% VRP_SA_SENSITIVITY.M
% 使用模拟退火求解CVRP，并做退火率与初始温度的灵敏度分析

clear; clc; close all;

%% 一、数据输入
coords = [ ...
    0,0;
    12,8;5,15;20,30;25,10;35,22;18,5;30,35;10,25;22,18;
    38,15;5,8;15,32;28,5;30,12;10,10;20,20;35,30;8,22;25,25;
    32,8;15,5;28,20;38,25;10,30;20,10;30,18;5,25;18,30;35,10;22,35
];
w = [ ...
    0,1.2,2.3,1.8,3.1,2.7,1.5,2.9,1.1,2.4, ...
    3.0,1.7,2.1,3.2,2.6,1.9,2.5,3.3,1.3,2.8, ...
    3.4,1.6,2.2,3.5,1.4,2.0,3.6,1.0,2.3,3.7,1.9
]';
Q = 5;
n = size(coords,1) - 1;
D = squareform(pdist(coords));

%% 三、敏感性分析：退火率 alpha

% 灵敏度：退火率
alphas = linspace(0.90,0.995,10);
costs_alpha = zeros(size(alphas));
for i=1:numel(alphas)
    costs_alpha(i) = sa_cvrp(100, alphas(i), 1e-3, 42, coords, w, Q);
end
figure; plot(alphas,costs_alpha,'-o','LineWidth',1.5);
xlabel('降温率 \alpha'); ylabel('最优总行驶距离 (km)');
title('退火率 \alpha 对最优解的影响'); grid on;

%% 四、敏感性分析：初始温度 T0
%% 四、敏感性分析：初始温度 T0
T0s = [10,50,100,200,500];
costs_T0 = zeros(size(T0s));
for i = 1:numel(T0s)
    % 传入：初始温度、降温率、终止温度、随机种子、坐标矩阵、需求向量、载重上限
    costs_T0(i) = sa_cvrp( ...
        T0s(i), ...    % 初始温度
        0.99, ...      % 降温率 α
        1e-3, ...      % 终止温度 Tf
        42, ...        % 随机种子
        coords, ...    % 坐标矩阵
        w, ...         % 需求向量
        Q ...          % 载重上限
    );
end

figure;
plot(T0s, costs_T0, '-s', 'LineWidth', 1.5);
xlabel('初始温度 T_0');
ylabel('最优总行驶距离 (km)');
title('初始温度 T_0 对最优解的灵敏度');
grid on;

