%% 可视化函数
function visualize_solution(coords, routes, total_distance)
    figure('Name', '车辆路径规划结果', 'Position', [100, 100, 1000, 800]);
    %论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    % 颜色设置
    colors = ['r', 'b', 'g', 'm', 'c', 'k', 'y'];
    if length(routes) > length(colors)
        colors = repmat(colors, 1, ceil(length(routes)/length(colors)));
    end
    
    hold on;
    
    % 绘制处理厂
    plot(coords(1,1), coords(1,2), 'ks', 'MarkerSize', 15, 'MarkerFaceColor', 'k');
    text(coords(1,1)+0.5, coords(1,2)+0.5, '处理厂', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 绘制收集点
    for i = 2:size(coords, 1)
        plot(coords(i,1), coords(i,2), 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'w', ...
             'MarkerEdgeColor', 'k', 'LineWidth', 1.5);
        text(coords(i,1)+0.5, coords(i,2)+0.5, num2str(i-1), 'FontSize', 10);
    end
    %论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    % 绘制每辆车的路径
    for v = 1:length(routes)
        route = routes{v};
        color = colors(mod(v-1, length(colors)) + 1);
        
        for i = 1:length(route)-1
            from_idx = route(i) + 1; % +1因为MATLAB索引
            to_idx = route(i+1) + 1;
            
            % 绘制路径线
            plot([coords(from_idx,1), coords(to_idx,1)], ...
                 [coords(from_idx,2), coords(to_idx,2)], ...
                 color, 'LineWidth', 2);
            
            % 添加箭头指示方向
            dx = coords(to_idx,1) - coords(from_idx,1);
            dy = coords(to_idx,2) - coords(from_idx,2);
            mid_x = coords(from_idx,1) + 0.7*dx;
            mid_y = coords(from_idx,2) + 0.7*dy;
            
            % 简化的箭头
            arrow_length = 0.8;
            angle = atan2(dy, dx);
            arrow_x = [mid_x, mid_x - arrow_length*cos(angle + pi/6), ...%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
                      mid_x - arrow_length*cos(angle - pi/6), mid_x];
            arrow_y = [mid_y, mid_y - arrow_length*sin(angle + pi/6), ...
                      mid_y - arrow_length*sin(angle - pi/6), mid_y];
            plot(arrow_x, arrow_y, color, 'LineWidth', 1.5);
        end
    end
    
    % 图形设置
    grid on;
    xlabel('X坐标 (公里)', 'FontSize', 12);
    ylabel('Y坐标 (公里)', 'FontSize', 12);
    title(sprintf('车辆路径规划结果\n车辆数量: %d辆, 总距离: %.2f公里', ...
                 length(routes), total_distance), 'FontSize', 14);
    
    % 添加图例
    legend_entries = cell(length(routes), 1);
    for v = 1:length(routes)
        legend_entries{v} = sprintf('车辆%d', v);
    end
    legend(legend_entries, 'Location', 'bestoutside');
    
    axis equal;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    hold off;
    
    % 保存图片
    saveas(gcf, 'CVRP_solution.png');
    fprintf('\n结果图已保存为 CVRP_solution.png\n');
end