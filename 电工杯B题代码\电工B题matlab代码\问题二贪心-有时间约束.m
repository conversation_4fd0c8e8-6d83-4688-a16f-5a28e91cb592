%% VRP_GREEDY_MULTI_TIME.M
% 多类型垃圾运输 - 最近邻贪心 + 时间约束(8h/天 @40km/h)+ 详细输出 + 可视化

clear; clc; close all;

%% 1. 输入数据 & 参数
data = [ 
    0,0,0,0,0,0;
    12,8,0.72,0.12,0.06,0.30;
    5,15,1.38,0.23,0.05,0.64;
    20,30,1.08,0.18,0.04,0.50;
    25,10,1.55,0.31,0.06,1.18;
    35,22,1.62,0.27,0.05,0.76;
    18,5,1.76,0.384,0.096,0.96;
    30,35,0.77,0.168,0.042,0.42;
    10,25,1.02,0.238,0.068,0.374;
    22,18,1.32,0.176,0.044,0.66;
    38,15,1.45,0.30,0.075,0.675;
    5,8,1.35,0.27,0.108,0.972;
    15,32,1.87,0.51,0.068,0.952;
    28,5,2.58,0.516,0.129,1.075;
    30,12,1.134,0.21,0.063,0.693;
    10,10,0.78,0.13,0.065,0.325;
    20,20,0.768,0.192,0.080,0.56;
    35,30,0.72,0.27,0.090,0.72;
    8,22,1.595,0.348,0.087,0.87;
    25,25,1.50,0.36,0.090,1.05;
    32,8,1.08,0.18,0.090,0.45;
    15,5,0.912,0.19,0.038,0.76;
    28,20,0.90,0.195,0.075,0.33;
    38,25,0.99,0.27,0.072,0.468;
    10,30,1.44,0.24,0.048,0.672;
    20,10,1.74,0.319,0.116,0.725;
    30,18,1.17,0.39,0.130,0.91;
    5,25,1.70,0.34,0.170,1.19;
    18,30,2.64,0.66,0.044,1.056;
    35,10,0.864,0.216,0.072,0.648;
    22,35,0.986,0.204,0.085,0.425
];
coords = data(:,1:2);
w_all  = data(:,3:6);
n      = size(coords,1)-1;

Q = [8,6,3,10];         % 载重上限
C = [2.5,2.0,5.0,1.8];   % 单位距离成本
v = 40;                 % 行驶速度 km/h
Tmax = 4;               % 每日最大作业时长 h

vmaxDist = v*Tmax; 
Dmax = vmaxDist;
% 距离矩阵
D = squareform(pdist(coords));

all_routes = cell(4,1);
all_dist   = cell(4,1);
all_load   = cell(4,1);
all_cost   = zeros(4,1);

%% 2. 贪心分配 + 时间检查
for k = 1:4
    fprintf('\n=== 类型 %d (Q=%.0f吨, C=%.1f元/km) ===\n', k, Q(k), C(k));
    unserved = find(w_all(2:end,k)>0)';  
    routes_k = {};
    loads_k  = [];
    dist_k   = [];
    time_k   = [];  % 每车累计时间
    vehCount = 0;
    
    while ~isempty(unserved)
        % 新车辆
        vehCount = vehCount + 1;
        currNode = 0; currLoad = 0; cumDist = 0;
        path = 0;
        
        % 多趟装运直到载重或时间超限
        while true
            % 准备尝试装下一个点后新累计距离与时间
            bestD = inf; bestI = -1;
            for i = unserved
                if currLoad + w_all(i+1,k) <= Q(k)
                    dij = D(currNode+1, i+1);
                    newDist = cumDist + dij + D(i+1,1);  % 加回厂段
                    if newDist <= Dmax && dij < bestD
                        bestD = dij; bestI = i;
                    end
                end
            end
            if bestI<0
                break;  
            end
            % 确认访问 bestI
            path = [path, bestI];
            currLoad = currLoad + w_all(bestI+1,k);
            currNode = bestI;
            cumDist = cumDist + bestD;
            unserved(unserved==bestI) = [];
        end
        
        % 返回厂区
        path = [path, 0];
        cumDist = cumDist + D(currNode+1,1);
        cumTime = cumDist / v;
        
        % 记录本车
        routes_k{vehCount} = path;
        loads_k(vehCount)  = currLoad;
        dist_k(vehCount)   = cumDist;
        time_k(vehCount)   = cumTime;
        
        fprintf('  车%2d: 路径 %s | 载重 %.2f吨 | 距离 %.2fkm | 时间 %.2fh\n', ...
            vehCount, mat2str(path), currLoad, cumDist, cumTime);
    end
    
    % 汇总
    all_routes{k} = routes_k;
    all_load{k}   = loads_k;
    all_dist{k}   = dist_k;
    all_cost(k)   = sum(dist_k)*C(k);
    
    fprintf('类型%d: 共%d辆, 总距%.2fkm, 总时%.2fh, 成本%.2f元\n', ...
        k, vehCount, sum(dist_k), sum(time_k), all_cost(k));
end

fprintf('\n== 全部类型 总成本: %.2f元 ==\n', sum(all_cost));

%% 3. 可视化：路径总览
figure('Name','带时间约束的四类路径','NumberTitle','off'); hold on; grid on;
scatter(coords(1,1),coords(1,2),120,'ks','filled'); text(coords(1,1),coords(1,2),' 0');
scatter(coords(2:end,1),coords(2:end,2),60,'ko');
for i=2:n+1, text(coords(i,1),coords(i,2),[' ',num2str(i-1)]); end
styles = {'-','--',':','-.'}; cols=lines(4);
for k=1:4
    for v=1:numel(all_routes{k})
        rt = all_routes{k}{v}+1;
        plot(coords(rt,1),coords(rt,2),styles{k},'Color',cols(k,:),'LineWidth',1.8);
    end
end
title('四类车辆路径（含8h/天时间约束）');
legend(['厂区','客户',arrayfun(@(k)sprintf('类型%d',k),1:4,'uni',0)],'Location','bestoutside');


% 饼图：成本占比
figure('Name','成本占比(Time)','NumberTitle','off');
pie(all_cost, arrayfun(@(k)sprintf('类%d',k),1:4,'uni',0));
title('各类运输成本占比');
