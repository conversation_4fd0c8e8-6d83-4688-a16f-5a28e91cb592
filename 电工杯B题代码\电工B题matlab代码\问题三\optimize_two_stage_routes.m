%% 两段式路径优化
function [routes, cost, emissions] = optimize_two_stage_routes(...
    collection_points, station_idx, dist_matrix, demands, capacity, ...
    unit_cost, alpha_k, beta_k, time_window, depot_hours, vehicle_speed)
    
    routes = {};
    cost = 0;
    emissions = 0;
    
    if isempty(collection_points)
        return;
    end
    
    % 第一段：收集点聚类（基于容量约束）
    clusters = simple_clustering(collection_points, demands, capacity);
    
    % 第二段：每个聚类优化路径
    for i = 1:length(clusters)
        cluster = clusters{i};
        cluster_demand = sum(demands(ismember(collection_points, cluster)));
        
        % 构造路径：处理厂 -> 收集点们 -> 中转站 -> 处理厂
        route = [0]; % 从处理厂开始
        
        % 添加收集点（使用最近邻）
        remaining_points = cluster;
        current_pos = 1; % 处理厂索引
        
        while ~isempty(remaining_points)
            % 找最近的收集点
            min_dist = inf;
            next_point = -1;
            next_idx = -1;
            
            for j = 1:length(remaining_points)
                point = remaining_points(j);
                dist = dist_matrix(current_pos, point + 1);
                if dist < min_dist
                    min_dist = dist;
                    next_point = point;
                    next_idx = j;
                end
            end
            
            route = [route, next_point];
            current_pos = next_point + 1;
            remaining_points(next_idx) = [];
        end
        
        % 添加中转站和返回处理厂
        route = [route, station_idx - 1, 0]; % 调整索引
        
        % 计算成本和排放
        route_distance = calculate_route_distance(route, dist_matrix);
        route_cost = route_distance * unit_cost;
        route_emissions = route_distance * alpha_k + cluster_demand * beta_k;
        
        routes{end+1} = route;
        cost = cost + route_cost;
        emissions = emissions + route_emissions;
    end
end