%% 详细输出路径结果
function output_detailed_routes(routing_solution, selected_stations, ...
                               allocation_plan, garbage_types, Q, C)
    
    station_keys = keys(routing_solution);
    
    for i = 1:length(station_keys)
        station_id = station_keys{i};
        routes = routing_solution(station_id);
        assigned_points = allocation_plan(station_id);
        
        fprintf('\n中转站 %d (服务收集点: ', station_id);
        for j = 1:length(assigned_points)
            fprintf('%d ', assigned_points(j));
        end
        fprintf('):\n');
        
        for k = 1:4
            k_routes = routes{k};
            if ~isempty(k_routes)
                fprintf('  %s运输:\n', garbage_types{k});
                for v = 1:length(k_routes)
                    route = k_routes{v};
                    fprintf('    车辆%d: ', v);
                    for r = 1:length(route)
                        if r == length(route)
                            fprintf('%d', route(r));
                        else
                            fprintf('%d→', route(r));
                        end
                    end
                    fprintf('\n');
                end
            end
        end
    end
end