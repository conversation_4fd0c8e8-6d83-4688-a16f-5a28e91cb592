%% VRP_SA.M
% 基于模拟退火的CVRP求解（单一车辆类型，Q=5吨）
clear; clc; close all;

%% 一、输入数据%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
data = [ ...
    0,   0,  0;   % 0: 处理厂
    12,  8,  1.2;
    5,   15, 2.3;
    20,  30, 1.8;
    25,  10, 3.1;
    35,  22, 2.7;
    18,   5, 1.5;
    30,  35, 2.9;
    10,  25, 1.1;
    22,  18, 2.4;
    38,  15, 3.0;
    5,    8, 1.7;
    15,  32, 2.1;
    28,   5, 3.2;
    30,  12, 2.6;
    10,  10, 1.9;
    20,  20, 2.5;
    35,  30, 3.3;
    8,   22, 1.3;
    25,  25, 2.8;
    32,   8, 3.4;
    15,   5, 1.6;
    28,  20, 2.2;
    38,  25, 3.5;
    10,  30, 1.4;
    20,  10, 2.0;
    30,  18, 3.6;
    5,   25, 1.0;
    18,  30, 2.3;
    35,  10, 3.7;
    22,  35, 1.9
];
coords = data(:,1:2);
w      = data(:,3);
Q      = 5;
n      = size(coords,1)-1;
D      = squareform(pdist(coords));

%% 二、初始解（最近邻贪心）
routes = {};
unserved = 1:n;
while ~isempty(unserved)
    load = 0; curr=0; route=[0];
    while true
        bestD=inf; sel=-1;
        for i=unserved
            if load + w(i+1) <= Q && D(curr+1,i+1)<bestD
                bestD=D(curr+1,i+1); sel=i;
            end
        end
        if sel<0, break; end
        route(end+1)=sel;
        load = load + w(sel+1);
        curr = sel;
        unserved(unserved==sel)=[];
    end
    route(end+1)=0;
    routes{end+1}=route;
end

%% 三、计算成本函数
costFunc = @(R) sum(cellfun(@(rt)...
    sum(arrayfun(@(k) D(rt(k)+1,rt(k+1)+1),1:length(rt)-1)), R));
currRoutes = routes;
currCost   = costFunc(currRoutes);
bestRoutes = currRoutes; bestCost = currCost;
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 四、模拟退火参数
T0 = 100;    % 初始温度
Tf = 1e-3;   % 终止温度
alpha = 0.99; % 降温系数
iter=0;

%% 五、SA主循环
T = T0;
while T > Tf
    iter = iter + 1;
    % 生成邻域解：随机交换两个客户
    newRoutes = currRoutes;
    % 选择两辆车及各自位置
    v1 = randi(numel(newRoutes)); rv1=newRoutes{v1};
    v2 = randi(numel(newRoutes)); rv2=newRoutes{v2};
    if numel(rv1)>2 && numel(rv2)>2
        % 随机位置（不含0端点）
        p1 = randi([2 numel(rv1)-1]); p2 = randi([2 numel(rv2)-1]);
        c1 = rv1(p1); c2 = rv2(p2);
        % 交换
        rv1(p1)=c2; rv2(p2)=c1;
        % 检查容量
        load1 = sum(w(rv1(2:end-1)+1)); load2 = sum(w(rv2(2:end-1)+1));
        if load1<=Q && load2<=Q
            newRoutes{v1}=rv1; newRoutes{v2}=rv2;
        end
    end
    % 计算新成本
    newCost = costFunc(newRoutes);
    % 接受准则
    if newCost < currCost || rand < exp((currCost-newCost)/T)
        currRoutes = newRoutes; currCost = newCost;
        if currCost < bestCost
            bestCost = currCost; bestRoutes = currRoutes;
        end
    end
    T = alpha * T;
end

%% 六、结果输出
fprintf('SA 迭代 %d 次, 最优车辆数=%d, 总距离=%.2f km\n', ...
    iter, numel(bestRoutes), bestCost);
for k=1:numel(bestRoutes)
    rt=bestRoutes{k};
    fprintf('  车%d: %s 距离=%.2f\n', k, mat2str(rt), ...
        sum(arrayfun(@(i) D(rt(i)+1, rt(i+1)+1),1:length(rt)-1)));
end

%% 七、可视化
figure; hold on; grid on;
scatter(coords(1,1),coords(1,2),100,'ks','filled');
scatter(coords(2:end,1),coords(2:end,2),50,'bo');
cols=lines(numel(bestRoutes));
for k=1:numel(bestRoutes)
    rt=bestRoutes{k}+1;
    plot(coords(rt,1),coords(rt,2),'-o','Color',cols(k,:),'LineWidth',1.5);
end
title('SA 优化后的路径'); xlabel('X'); ylabel('Y');
legend(['厂区', arrayfun(@(k)sprintf('车%%d',k),1:numel(bestRoutes),'uni',0)]);
