%% 2-opt改进算法
function improved_route = two_opt_improvement(route, dist_matrix)
    improved_route = route;
    n = length(route);
    improved = true;
    max_iterations = 100;
    iteration = 0;
    
    while improved && iteration < max_iterations
        improved = false;
        iteration = iteration + 1;
        
        for i = 2:n-2  % 不包括起点和终点
            for j = i+1:n-1
                % 计算当前距离
                current_dist = dist_matrix(improved_route(i-1)+1, improved_route(i)+1) + ...
                              dist_matrix(improved_route(j)+1, improved_route(j+1)+1);
                
                % 计算交换后的距离
                new_dist = dist_matrix(improved_route(i-1)+1, improved_route(j)+1) + ...
                          dist_matrix(improved_route(i)+1, improved_route(j+1)+1);
                
                if new_dist < current_dist
                    % 执行2-opt交换
                    improved_route(i:j) = improved_route(j:-1:i);
                    improved = true;
                end
            end
        end
    end
end

