function [routing_solution, total_cost, total_emissions] = solve_routing_with_emissions(...
    selected_stations, allocation_plan, coords, ts_coords, demands, ...
    dist_matrix, Q, C, alpha, beta, ts_time_windows, depot_hours, vehicle_speed)
    
    routing_solution = containers.Map('KeyType', 'int32', 'ValueType', 'any');
    total_cost = 0;
    total_emissions = 0;
    
    % 为每个选中的中转站求解路径问题
    for i = 1:length(selected_stations)
        station_id = selected_stations(i);
        assigned_points = allocation_plan(station_id);
        
        fprintf('  优化中转站 %d 的路径 (服务 %d 个收集点)...\n', ...
                station_id, length(assigned_points));
        
        % 构建子问题：收集点 -> 中转站 -> 处理厂
        % 修正中转站索引计算
        ts_local_idx = find([31:35] == station_id);  % 找到中转站在候选列表中的位置
        if isempty(ts_local_idx)
            ts_local_idx = 1;  % 默认索引，防止错误
        end
        
        [station_routes, station_cost, station_emissions] = solve_station_routing(...
            station_id, assigned_points, coords, ts_coords, demands, ...
            dist_matrix, Q, C, alpha, beta, ts_time_windows(ts_local_idx, :), ...
            depot_hours, vehicle_speed);
        
        routing_solution(station_id) = station_routes;
        total_cost = total_cost + station_cost;
        total_emissions = total_emissions + station_emissions;
        
        fprintf('    中转站 %d: 成本 %.2f元, 碳排放 %.2f kg\n', ...
                station_id, station_cost, station_emissions);
    end
end
