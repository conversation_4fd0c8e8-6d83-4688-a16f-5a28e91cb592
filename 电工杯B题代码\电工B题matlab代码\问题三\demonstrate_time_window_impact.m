%% 时间窗口影响演示
function demonstrate_time_window_impact()
    fprintf('\n=== 时间窗口约束影响演示 ===\n');
    
    fprintf('案例1: 中转站时间窗口收紧的影响\n');
    fprintf('原时间窗口: [7:00-17:00] (10小时)\n');
    fprintf('收紧后: [9:00-15:00] (6小时)\n');
    fprintf('影响分析:\n');
    fprintf('  - 可服务时间减少40%%\n');
    fprintf('  - 需要增加车辆数量约30-40%%\n');
    fprintf('  - 总成本可能增加20-30%%\n');
    
    fprintf('\n案例2: 处理厂工作时间延长的好处\n');
    fprintf('原工作时间: [6:00-18:00] (12小时)\n');
    fprintf('延长后: [5:00-20:00] (15小时)\n');
    fprintf('改善效果:\n');
    fprintf('  - 车辆调度更灵活\n');
    fprintf('  - 可减少车辆数量10-20%%\n');
    fprintf('  - 提高中转站利用率\n');
    
    fprintf('\n案例3: 时间窗口错配问题\n');
    fprintf('问题场景: 中转站A [8:00-16:00], 中转站B [10:00-18:00]\n');
    fprintf('解决策略:\n');
    fprintf('  - 早班车优先使用中转站A\n');
    fprintf('  - 晚班车优先使用中转站B\n');
    fprintf('  - 通过路径规划避免时间冲突\n');
end