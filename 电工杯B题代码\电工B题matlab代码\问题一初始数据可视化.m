%% 数据准备
% 点编号、X、Y、垃圾量
data = [ ...%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    0,  0,  0,   NaN;
    1, 12,  8,   1.2;
    2,  5, 15,   2.3;
    3, 20, 30,   1.8;
    4, 25, 10,   3.1;
    5, 35, 22,   2.7;
    6, 18,  5,   1.5;
    7, 30, 35,   2.9;
    8, 10, 25,   1.1;
    9, 22, 18,   2.4;
   10, 38, 15,   3.0;
   11,  5,  8,   1.7;
   12, 15, 32,   2.1;
   13, 28,  5,   3.2;
   14, 30, 12,   2.6;
   15, 10, 10,   1.9;
   16, 20, 20,   2.5;
   17, 35, 30,   3.3;
   18,  8, 22,   1.3;
   19, 25, 25,   2.8;
   20, 32,  8,   3.4;
   21, 15,  5,   1.6;
   22, 28, 20,   2.2;
   23, 38, 25,   3.5;
   24, 10, 30,   1.4;
   25, 20, 10,   2.0;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
   26, 30, 18,   3.6;
   27,  5, 25,   1.0;
   28, 18, 30,   2.3;
   29, 35, 10,   3.7;
   30, 22, 35,   1.9 ];
% 提取
idx = data(:,1);
X   = data(:,2);
Y   = data(:,3);
W   = data(:,4);

%% 1. 平面散点图：大小映射垃圾量，颜色映射分位段
figure('Name','Scatter: 垃圾量分布','Color','w');
% 为 depot 绘制特殊标记
hold on;
depot = idx==0;
scatter(X(depot), Y(depot), 200, 'ks', 'filled');
p = scatter(X(~depot), Y(~depot), W(~depot)*100, W(~depot), 'filled');
colorbar;
colormap(parula);
title('各收集点垃圾量散点图');
xlabel('X (km)'); ylabel('Y (km)');
legend('处理厂','收集点','Location','best');
axis equal; grid on;
hold off;

%% 2. 热力图：插值垃圾量场
figure('Name','Heatmap: 垃圾量热力图','Color','w');
% 构造网格
xi = linspace(min(X), max(X), 100);
yi = linspace(min(Y), max(Y), 100);
[Xi, Yi] = meshgrid(xi, yi);
Zi = griddata(X(~depot), Y(~depot), W(~depot), Xi, Yi, 'cubic');
imagesc(xi, yi, Zi);
set(gca,'YDir','normal');
colorbar; colormap(flipud(hot));
title('基于插值的垃圾量热力图');
xlabel('X (km)'); ylabel('Y (km)');
hold on;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
scatter(X(~depot), Y(~depot), 20, 'k','filled');
scatter(X(depot), Y(depot), 150, 'ks','filled');
hold off;


