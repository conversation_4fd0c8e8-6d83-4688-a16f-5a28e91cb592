%% PROBLEM3_LNS_TWO_STAGE.M
% 问题三：P-中心选址 + LNS路径优化 + 可视化（MATLAB）
clear; clc; close all;
%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
%% 1. 基本数据
coords = [
    0,0;
    12,8;5,15;20,30;25,10;35,22;18,5;30,35;10,25;22,18;
    38,15;5,8;15,32;28,5;30,12;10,10;20,20;35,30;8,22;25,25;
    32,8;15,5;28,20;38,25;10,30;20,10;30,18;5,25;18,30;35,10;22,35
];
n = size(coords,1)-1;  % 客户数量 30
w_all = [zeros(1,4); ...  % 31×4 需求
    0.72,0.12,0.06,0.30;1.38,0.23,0.05,0.64;1.08,0.18,0.04,0.50;...
    1.55,0.31,0.06,1.18;1.62,0.27,0.05,0.76;1.76,0.384,0.096,0.96;...
    0.77,0.168,0.042,0.42;1.02,0.238,0.068,0.374;1.32,0.176,0.044,0.66;...
    1.45,0.30,0.075,0.675;1.35,0.27,0.108,0.972;1.87,0.51,0.068,0.952;...
    2.58,0.516,0.129,1.075;1.134,0.21,0.063,0.693;0.78,0.13,0.065,0.325;...
    0.768,0.192,0.080,0.56;0.72,0.27,0.090,0.72;1.595,0.348,0.087,0.87;...
    1.50,0.36,0.090,1.05;1.08,0.18,0.090,0.45;0.912,0.19,0.038,0.76;...
    0.90,0.195,0.075,0.33;0.99,0.27,0.072,0.468;1.44,0.24,0.048,0.672;...
    1.74,0.319,0.116,0.725;1.17,0.39,0.13,0.91;1.70,0.34,0.17,1.19;...
    2.64,0.66,0.044,1.056;0.864,0.216,0.072,0.648;0.986,0.204,0.085,0.425
];
st_coords = [12,5;7,28;20,8;30,15;25,10];  % 31–35%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
m = size(st_coords,1);
Tj = 10000;  % 建站成本
Q = [8,6,3,10]; C = [2.5,2.0,5.0,1.8];
v = 40;

%% 2. 距离矩阵
all_coords = [coords; st_coords];
D = squareform(pdist(all_coords));

%% 3. 第一阶段：P-中心选址 + 分配（同前）
bestCost = inf; bestY=false(m,1); bestAssign=[];
for p=1:m
  combs = nchoosek(1:m,p);
  for i=1:size(combs,1)
    y = false(m,1); y(combs(i,:))=true;
    assignTemp = zeros(n,4); assignCost=0;
    for ii=1:n
      dmin=inf; sel=0;
      for ss=find(y)'
        d= D(ii+1,n+ss+1);
        if d<dmin, dmin=d; sel=ss; end
      end
      assignCost = assignCost + dmin;
      assignTemp(ii,:) = sel;
    end
    total = p*Tj + assignCost;
    if total<bestCost
      bestCost=total; bestY=y; bestAssign=assignTemp;
    end
  end
end

%% 4. 第二阶段：LNS路径优化
% 参数
maxIter = 200;         % 迭代次数
removeRatio = 0.25;     % 破坏比率

% 初始化：最近邻解
[baseRoutes, baseCost] = solveCVRP(bestY, bestAssign, n, m, D, Q, C, w_all);
bestGlobalRoutes = baseRoutes;
bestGlobalCost = baseCost;

% LNS 主循环
for iter = 1:maxIter
    % 深拷贝当前最优路径
    currRoutes = bestGlobalRoutes;
    % 4.1 破坏：随机移除部分客户分配
    assignPerturb = bestAssign;
    for kk = 1:4
        pts = find(assignPerturb(:,kk) > 0);
        kRemove = max(1, floor(removeRatio * numel(pts)));
        remIdx = randsample(pts, kRemove);
        % 标记为未服务
        for ii = remIdx'
            % 找到其归属站s0
            s0 = assignPerturb(ii, kk);
            % 在该站对应的回路中移除该点
            routes_k = currRoutes{s0, kk};
            for rIdx = 1:numel(routes_k)
                route = routes_k{rIdx};
                globalIdx = ii + 1;  % 客户的all_coords索引
                route(route == globalIdx) = [];
                routes_k{rIdx} = route;
            end
            currRoutes{s0, kk} = routes_k;
            assignPerturb(ii, kk) = 0;
        end
    end
    % 4.2 修复：对每个站每类，插入未服务客户
    for kk = 1:4
        for s0 = find(bestY)'
            routes_k = currRoutes{s0, kk};
            unserved = find((bestAssign(:,kk) == s0) & (assignPerturb(:,kk)==0))';
            while ~isempty(unserved)
                bestInc = inf; selRoute=1; selPos=0; selPt=0;
                for ii = unserved
                    ptIdx = ii + 1;
                    for rIdx = 1:numel(routes_k)
                        route = routes_k{rIdx};
                        for pos = 2:length(route)
                            before = route(pos-1);
                            after  = route(pos);
                            inc = D(before, ptIdx) + D(ptIdx, after) - D(before, after);
                            costInc = C(kk) * inc;
                            if costInc < bestInc
                                bestInc = costInc;
                                selRoute = rIdx;
                                selPos   = pos;
                                selPt    = ptIdx;
                            end
                        end
                    end
                end
                % 插入
                route = routes_k{selRoute};
                route = [route(1:selPos-1), selPt, route(selPos:end)];
                routes_k{selRoute} = route;
                assignPerturb(selPt-1, kk) = s0;
                unserved(unserved == selPt-1) = [];
            end
            currRoutes{s0, kk} = routes_k;
        end
    end%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    % 4.3 评估新解
    newCost = 0;
    for s0 = find(bestY)'
        for kk = 1:4
            for r = currRoutes{s0, kk}
                route = r{1};
                for t = 1:length(route)-1
                    newCost = newCost + C(kk)*D(route(t), route(t+1));
                end
            end
        end
    end
    % 接受准则
    if newCost < bestGlobalCost
        bestGlobalCost   = newCost;
        bestGlobalRoutes = currRoutes;
    end
end
bestCost = bestGlobalCost + sum(bestY)*Tj;
%% 5. 输出结果
fprintf('选址中转站：'); for ss=1:m, if bestY(ss), fprintf('%d ',n+ss); end; end; fprintf('\n');
fprintf('总成本：%.2f 元\n', bestCost);
for s0=1:m
  if ~bestY(s0), continue; end; j=n+s0;
  fprintf('\n-- 站点 %d 路线 --\n',j);
  for kk=1:4
    for vv=1:numel(bestGlobalRoutes{s0,kk})
      fprintf('类%d 车%d: %s\n',kk,vv,mat2str(bestGlobalRoutes{s0,kk}{vv}));
    end
  end
end

%% 6. 可视化（同前）
fprintf('选址中转站：'); for ss=1:m, if bestY(ss), fprintf('%d ',n+ss); end; end; fprintf('\n');
fprintf('总成本：%.2f 元\n', bestCost);
for s0=1:m
  if ~bestY(s0), continue; end; j=n+s0;
  fprintf('\n-- 站点 %d 路线 --\n',j);
  for kk=1:4
    for vv=1:numel(bestGlobalRoutes{s0,kk})
      fprintf('类%d 车%d: %s\n',kk,vv,mat2str(bestGlobalRoutes{s0,kk}{vv}));
    end
  end
end

%% 6. 精美可视化
% 全局运输路线总览
figure('Name','全局运输路线总览','NumberTitle','off','Position',[100,100,800,600]);
hold on; grid on;
% 画厂区 & 客户
scatter(all_coords(1,1),all_coords(1,2),120,'kp','filled'); text(all_coords(1,1),all_coords(1,2),'厂区','FontSize',12);
scatter(all_coords(2:31,1),all_coords(2:31,2),60,'bo');
for i=1:30; text(all_coords(i+1,1)+0.5, all_coords(i+1,2)+0.5, ['点',num2str(i)],'FontSize',9); end
% 画中转站
scatter(all_coords(32:36,1),all_coords(32:36,2),120,'rs','filled');
for s=1:m
    if bestY(s)
        text(all_coords(n+s+1,1)+0.5, all_coords(n+s+1,2)+0.5, ['站',num2str(n+s)],'FontSize',11,'Color','r');
    end
end
% 路线绘制，不同站点颜色，不同类型线型
colors = lines(m); lstyles = {'-','--',':','-.'};
for s=1:m
    if ~bestY(s), continue; end
    for kk=1:4
        for idx=1:numel(bestGlobalRoutes{s,kk})
            route = bestGlobalRoutes{s,kk}{idx};
            plot(all_coords(route,1),all_coords(route,2),...
                'Color',colors(s,:),'LineStyle',lstyles{kk},'LineWidth',1.5);
        end
    end
end
title('全局运输路线总览','FontSize',14);
xlabel('X (km)'); ylabel('Y (km)');
legend({'厂区','客户','中转站','路线'},'Location','eastoutside');

% 各站分类型子图
figure('Name','各站分类型运输路径','NumberTitle','off','Position',[150,150,1000,800]);
plotIdx = 1;
for s=1:m
    if ~bestY(s), continue; end
    jidx = n + s + 1;
    for kk=1:4
        subplot(sum(bestY)*2,2,plotIdx); hold on; grid on;
        % 站点与客户
        scatter(all_coords(jidx,1),all_coords(jidx,2),100,'rs','filled');
        text(all_coords(jidx,1),all_coords(jidx,2),'站','FontSize',11);
        pts = find(bestAssign(:,kk)==s);
        scatter(all_coords(pts+1,1),all_coords(pts+1,2),50,'bo');
        for ii=pts'
            text(all_coords(ii+1,1)+0.3, all_coords(ii+1,2)+0.3, ['点',num2str(ii)],'FontSize',8);
        end
        % 绘制路径
        for idx=1:numel(bestGlobalRoutes{s,kk})
            route = bestGlobalRoutes{s,kk}{idx};
            plot(all_coords(route,1),all_coords(route,2),'LineWidth',1.5);
        end
        title(sprintf('站%d - 类型%d',n+s,kk));
        xlabel('X'); ylabel('Y'); axis equal;
        plotIdx = plotIdx + 1;
    end
end

% 性能统计图（距离 & 站点覆盖）
totalDist = zeros(m,4);
for s=1:m
    if ~bestY(s), continue; end
    for kk=1:4
        for route=bestGlobalRoutes{s,kk}
            rt=route{1};
            for t=1:length(rt)-1
                totalDist(s,kk) = totalDist(s,kk) + D(rt(t),rt(t+1));
            end
        end
    end
end

figure('Name','距离统计','NumberTitle','off','Position',[200,200,800,400]);
b = bar(totalDist(bestY,:), 'stacked');
title('各站各类型总行驶距离'); xlabel('选址站点索引'); ylabel('距离 (km)');
xticklabels = arrayfun(@(s) sprintf('站%d',n+s), find(bestY),'UniformOutput',false);
set(gca,'XTickLabel',xticklabels);
legend(arrayfun(@(k) sprintf('类型%d',k),1:4,'UniformOutput',false),'Location','northoutside','Orientation','horizontal');

