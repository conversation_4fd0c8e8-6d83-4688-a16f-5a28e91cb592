"""
多车辆协同与碳排放控制优化

解决第二问问题二：多车辆协同与载重约束下的优化
现实中，垃圾分类运输需区分不同垃圾类型（本题中仅考虑4类垃圾，即厨余垃圾、可回收物、有害垃圾、其他垃圾），
每类垃圾需由专用车辆运输（车辆类型1,2,3,4k = 分别对应上述4类垃圾）。
每类车辆的载重限制kQ 、容积限制kV 、单位距离运输成本kC 不同（参数见附件2），
且每个收集点可能产生多种类型的垃圾（各类型垃圾量, 0i k w ，满足i k i k w w = = ）。
车辆从处理厂出发，完成同类型垃圾收集后返回处理厂，不同类型车辆可独立调度。

本解决方案同时兼顾运输成本和碳排放控制。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import time
import matplotlib

# 设置中文字体，解决中文显示为白框的问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 解决保存图像时负号'-'显示为方块的问题

# 1. 数据读取与预处理
# 读取收集点数据
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
points_data = pd.read_excel("D:\电工杯\diangongbei\B题\附件1.xlsx", skiprows=2, names=col_names)

# 读取附件3中的4类垃圾数据
garbage_data = pd.read_excel("D:\电工杯\diangongbei\B题\附件3.xlsx", skiprows=1)
garbage_data.columns = ['收集点编号', '厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']

# 读取附件2中的车辆参数
vehicle_params = pd.read_excel('B题/附件2.xlsx', skiprows=1)
vehicle_params.columns = ['车辆类型k', '垃圾类型', '载重', '容积', '距离成本', '碳排放系数1', '碳排放系数2']

# 提取坐标
points = points_data[['x', 'y']].values
n = len(points)

# 提取各类垃圾重量和体积
garbage_weights = {}
garbage_volumes = {}
# 垃圾类型映射
garbage_types = {1: '厨余垃圾', 2: '可回收物', 3: '有害垃圾', 4: '其他垃圾'}

for k in range(1, 5):  # 4类垃圾
    garbage_weights[k] = garbage_data[garbage_types[k]].values
    # 假设体积与重量有一定比例关系，可根据实际情况调整
    garbage_volumes[k] = garbage_weights[k] * 1.2  # 假设体积是重量的1.2倍

# 提取车辆参数
Q = {}  # 载重限制
V = {}  # 容积限制
C = {}  # 单位距离成本
E1 = {}  # 碳排放系数1
E2 = {}  # 碳排放系数2
carbon_price = 0.5  # 碳排放单价，可以根据实际情况调整

for k in range(1, 5):
    Q[k] = vehicle_params.loc[k-1, '载重']
    V[k] = vehicle_params.loc[k-1, '容积']
    C[k] = vehicle_params.loc[k-1, '距离成本']
    E1[k] = vehicle_params.loc[k-1, '碳排放系数1']
    E2[k] = vehicle_params.loc[k-1, '碳排放系数2']

# 垃圾处理厂坐标
depot = np.array([0, 0])

# 计算距离矩阵
all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 2. 数学模型
"""
多车辆协同与碳排放控制优化模型

决策变量:
x_{i,j,k,v} = 1 表示车辆v(类型k)从点i到点j行驶，否则为0
y_{i,k,v} = 1 表示点i由车辆v(类型k)服务，否则为0

目标函数:
min Z = α * Z_cost + (1-α) * Z_carbon

其中:
Z_cost = ∑_{k=1}^4 ∑_{v=1}^{V_k} ∑_{i=0}^n ∑_{j=0,j≠i}^n C_k * d_{i,j} * x_{i,j,k,v}
Z_carbon = ∑_{k=1}^4 ∑_{v=1}^{V_k} ∑_{i=0}^n ∑_{j=0,j≠i}^n (E1_k * d_{i,j} + E2_k * w_{i,k}) * x_{i,j,k,v}

α为权重系数，表示运输成本在目标函数中的重要性

约束条件:
1. 每个收集点的每类垃圾只能由一辆对应类型的车辆服务:
   ∑_{v=1}^{V_k} y_{i,k,v} = 1, ∀i∈{1,2,...,n}, ∀k∈{1,2,3,4} 且 w_{i,k} > 0

2. 车辆载重约束:
   ∑_{i=1}^n w_{i,k} * y_{i,k,v} ≤ Q_k, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

3. 车辆容积约束:
   ∑_{i=1}^n v_{i,k} * y_{i,k,v} ≤ V_k, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

4. 流量守恒约束:
   ∑_{j=0,j≠i}^n x_{i,j,k,v} = ∑_{j=0,j≠i}^n x_{j,i,k,v} = y_{i,k,v},
   ∀i∈{1,2,...,n}, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

5. 子回路消除约束:
   ∑_{i,j∈S} x_{i,j,k,v} ≤ |S| - 1, ∀S⊂{1,2,...,n}, |S|≥2, ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}

6. 车辆类型约束:
   每类垃圾只能由对应类型的车辆运输

7. 时间约束(扩展):
   ∑_{i=0}^n ∑_{j=0,j≠i}^n (d_{i,j}/speed + service_time * y_{i,k,v}) * x_{i,j,k,v} ≤ max_time,
   ∀k∈{1,2,3,4}, ∀v∈{1,2,...,V_k}
"""

# 3. 节约算法求解每类垃圾的路径
def savings_algorithm(garbage_type, alpha=0.5):
    weights = garbage_weights[garbage_type]
    volumes = garbage_volumes[garbage_type]
    max_weight = Q[garbage_type]
    max_volume = V[garbage_type]

    # 初始化：每个点一个单独路径
    valid_points = [i+1 for i in range(n) if weights[i] > 0]  # 只考虑有该类垃圾的点
    routes = [[i] for i in valid_points]
    route_weights = [weights[i-1] for i in valid_points]
    route_volumes = [volumes[i-1] for i in valid_points]

    # 计算节约值（考虑运输成本和碳排放）
    savings = []
    for i in valid_points:
        for j in valid_points:
            if i != j:
                # 运输成本节约
                cost_saving = C[garbage_type] * (dist_matrix[i, 0] + dist_matrix[j, 0] - dist_matrix[i, j])

                # 碳排放节约
                carbon_saving = (E1[garbage_type] * (dist_matrix[i, 0] + dist_matrix[j, 0] - dist_matrix[i, j]) +
                                E2[garbage_type] * (weights[i-1] + weights[j-1])) * carbon_price

                # 综合节约值
                total_saving = alpha * cost_saving + (1 - alpha) * carbon_saving

                savings.append((i, j, total_saving))

    # 按节约值降序排序
    savings.sort(key=lambda x: x[2], reverse=True)

    # 合并路径
    def find_route(routes, node):
        for idx, route in enumerate(routes):
            if node in route:
                return idx
        return None

    for i, j, _ in savings:  # 使用_忽略未使用的变量
        ri = find_route(routes, i)
        rj = find_route(routes, j)

        if ri is not None and rj is not None and ri != rj:
            route_i = routes[ri]
            route_j = routes[rj]

            # 检查是否可以合并（端点相连且不超过载重和容积限制）
            merged_weight = route_weights[ri] + route_weights[rj]
            merged_volume = route_volumes[ri] + route_volumes[rj]

            if merged_weight <= max_weight and merged_volume <= max_volume:
                # 检查端点连接情况
                if route_i[-1] == i and route_j[0] == j:
                    routes[ri] = route_i + route_j
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[0] == i and route_j[-1] == j:
                    routes[ri] = route_j + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[0] == i and route_j[0] == j:
                    routes[ri] = route_j[::-1] + route_i
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]
                elif route_i[-1] == i and route_j[-1] == j:
                    routes[ri] = route_i + route_j[::-1]
                    route_weights[ri] = merged_weight
                    route_volumes[ri] = merged_volume
                    del routes[rj], route_weights[rj], route_volumes[rj]

    return routes, route_weights, route_volumes

    # 计算路径成本（包括运输成本和碳排放成本）
def calculate_route_cost(route, garbage_type, alpha=0.5):
    # 运输成本
    transport_cost = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        transport_cost += dist_matrix[prev, node] * C[garbage_type]
        prev = node
    # 返回垃圾处理厂
    transport_cost += dist_matrix[prev, 0] * C[garbage_type]

    # 碳排放成本
    carbon_cost = 0
    prev = 0  # 从垃圾处理厂出发
    for node in route:
        # 距离相关的碳排放
        carbon_cost += dist_matrix[prev, node] * E1[garbage_type] * carbon_price
        # 载重相关的碳排放
        carbon_cost += garbage_weights[garbage_type][node-1] * E2[garbage_type] * carbon_price
        prev = node
    # 返回垃圾处理厂
    carbon_cost += dist_matrix[prev, 0] * E1[garbage_type] * carbon_price

    # 综合成本
    total_cost = alpha * transport_cost + (1 - alpha) * carbon_cost

    return total_cost, transport_cost, carbon_cost

# 计算路径时间
def calculate_route_time(route):
    time = 0
    prev = 0  # 从垃圾处理厂出发
    speed = 40  # 车辆平均速度 km/h
    service_time = 0.2  # 每个点的服务时间 h

    for node in route:
        # 行驶时间
        time += dist_matrix[prev, node] / speed
        # 服务时间
        time += service_time
        prev = node

    # 返回垃圾处理厂
    time += dist_matrix[prev, 0] / speed

    return time

# 考虑时间约束的路径拆分
def split_routes_by_time(routes, route_weights, route_volumes, garbage_type, max_time=8):
    new_routes = []
    new_weights = []
    new_volumes = []

    for route, weight, volume in zip(routes, route_weights, route_volumes):
        route_time = calculate_route_time(route)

        if route_time <= max_time:
            # 如果路径时间不超过限制，直接添加
            new_routes.append(route)
            new_weights.append(weight)
            new_volumes.append(volume)
        else:
            # 如果超过时间限制，需要拆分路径
            current_route = []
            current_weight = 0
            current_volume = 0

            for node in route:
                # 计算添加当前节点后的时间
                temp_route = current_route + [node]
                temp_time = calculate_route_time(temp_route)

                if temp_time <= max_time:
                    # 可以添加当前节点
                    current_route.append(node)
                    current_weight += garbage_weights[garbage_type][node-1]
                    current_volume += garbage_volumes[garbage_type][node-1]
                else:
                    # 需要开始新的路径
                    if current_route:
                        new_routes.append(current_route)
                        new_weights.append(current_weight)
                        new_volumes.append(current_volume)

                    # 开始新路径
                    current_route = [node]
                    current_weight = garbage_weights[garbage_type][node-1]
                    current_volume = garbage_volumes[garbage_type][node-1]

            # 添加最后一条路径
            if current_route:
                new_routes.append(current_route)
                new_weights.append(current_weight)
                new_volumes.append(current_volume)

    return new_routes, new_weights, new_volumes

# 可视化路径
def visualize_routes(all_routes, title, alpha=0.5):
    plt.figure(figsize=(12, 10))
    plt.scatter(points[:, 0], points[:, 1], c='blue', s=50, label='收集点')
    plt.scatter([0], [0], c='red', s=200, marker='*', label='垃圾处理厂')

    colors = ['g', 'm', 'c', 'y']
    markers = ['o', 's', '^', 'd']

    for k in range(1, 5):
        routes = all_routes[k]
        for i, route in enumerate(routes):
            route_with_depot = [0] + route + [0]  # 添加处理厂作为起点和终点
            route_x = [all_points[j, 0] for j in route_with_depot]
            route_y = [all_points[j, 1] for j in route_with_depot]

            plt.plot(route_x, route_y, c=colors[k-1], marker=markers[k-1],
                     label=f'{garbage_types[k]}路径{i+1}' if i == 0 else "")

            # 添加路径箭头
            for j in range(len(route_with_depot)-1):
                dx = route_x[j+1] - route_x[j]
                dy = route_y[j+1] - route_y[j]
                plt.arrow(route_x[j], route_y[j], dx*0.9, dy*0.9,
                          head_width=0.3, head_length=0.5, fc=colors[k-1], ec=colors[k-1])

    plt.title(f'{title} (α={alpha})', fontsize=16)
    plt.xlabel('X坐标', fontsize=14)
    plt.ylabel('Y坐标', fontsize=14)
    plt.legend(loc='best', fontsize=12)
    plt.grid(True)
    plt.savefig(f'{title}_alpha_{alpha}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_route_time_analysis(all_routes, time_constrained_routes):
    """绘制路径时间分析图"""
    fig, ax = plt.subplots(figsize=(14, 8))

    # 计算每条路径的时间
    route_times = []
    route_labels = []
    route_colors = []

    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f']

    for k in range(1, 5):
        for i, route in enumerate(all_routes[k]):
            time = calculate_route_time(route)
            route_times.append(time)
            route_labels.append(f'{garbage_types[k]}-{i+1}')
            route_colors.append(colors[k-1])

    # 按时间排序
    sorted_indices = np.argsort(route_times)
    route_times = [route_times[i] for i in sorted_indices]
    route_labels = [route_labels[i] for i in sorted_indices]
    route_colors = [route_colors[i] for i in sorted_indices]

    # 绘制水平条形图
    bars = ax.barh(route_labels, route_times, color=route_colors, alpha=0.7)

    # 添加时间约束线
    ax.axvline(x=8, color='red', linestyle='--', linewidth=2, label='时间约束(8小时)')

    # 添加数据标签
    for bar in bars:
        width = bar.get_width()
        ax.text(width + 0.1, bar.get_y() + bar.get_height()/2, f'{width:.2f}h',
                va='center', fontsize=9)

    # 标记超过时间约束的路径
    for i, time in enumerate(route_times):
        if time > 8:
            ax.text(8.1, i, '需拆分', color='red', va='center', fontsize=10,
                   bbox=dict(facecolor='white', alpha=0.7, edgecolor='red'))

    # 设置图表标题和标签
    ax.set_title('各路径时间分析', fontsize=16, pad=20)
    ax.set_xlabel('时间(小时)', fontsize=14)
    ax.set_ylabel('路径', fontsize=14)

    # 添加图例
    handles, labels = ax.get_legend_handles_labels()
    for k in range(1, 5):
        handles.append(plt.Rectangle((0,0), 1, 1, color=colors[k-1]))
        labels.append(garbage_types[k])
    ax.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=5, fontsize=12)

    # 设置网格线
    ax.grid(axis='x', linestyle='--', alpha=0.7)

    # 调整布局
    plt.tight_layout()
    plt.savefig("路径时间分析.png", dpi=300, bbox_inches='tight')
    plt.show()

def plot_cost_comparison(alphas, transport_costs, carbon_costs, total_costs, with_time_constraint=False):
    """绘制不同权重系数下的成本对比柱状图"""
    fig, ax = plt.subplots(figsize=(12, 8))

    x = np.arange(len(alphas))
    width = 0.25

    # 绘制三种成本的柱状图
    bars1 = ax.bar(x - width, transport_costs, width, label='运输成本', color='#3498db')
    bars2 = ax.bar(x, carbon_costs, width, label='碳排放成本', color='#2ecc71')
    bars3 = ax.bar(x + width, total_costs, width, label='综合成本', color='#e74c3c')

    # 添加数据标签
    def add_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.2f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),  # 3点垂直偏移
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=10)

    add_labels(bars1)
    add_labels(bars2)
    add_labels(bars3)

    # 设置图表标题和标签
    title = "考虑时间约束下的成本对比" if with_time_constraint else "不考虑时间约束的成本对比"
    ax.set_title(title, fontsize=16, pad=20)
    ax.set_xlabel('权重系数 α', fontsize=14)
    ax.set_ylabel('成本', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels([f'α = {alpha}' for alpha in alphas])

    # 添加图例
    ax.legend(loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=3, fontsize=12)

    # 设置网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    save_path = "成本对比_有时间约束.png" if with_time_constraint else "成本对比_无时间约束.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')

    plt.show()

def plot_time_constraint_impact(all_routes, time_constrained_routes):
    """绘制时间约束对路径规划的影响分析图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))

    # 准备数据
    garbage_types_list = list(garbage_types.values())
    original_counts = [len(all_routes[k]) for k in range(1, 5)]
    constrained_counts = [len(time_constrained_routes[k]) for k in range(1, 5)]

    # 左侧：车辆数量对比柱状图
    x = np.arange(len(garbage_types_list))
    width = 0.35

    ax1.bar(x - width/2, original_counts, width, label='无时间约束', color='#3498db')
    ax1.bar(x + width/2, constrained_counts, width, label='有时间约束', color='#e74c3c')

    # 添加数据标签
    for i, v in enumerate(original_counts):
        ax1.text(i - width/2, v + 0.1, str(v), ha='center', fontsize=10)
    for i, v in enumerate(constrained_counts):
        ax1.text(i + width/2, v + 0.1, str(v), ha='center', fontsize=10)

    ax1.set_title('时间约束对车辆数量的影响', fontsize=14)
    ax1.set_xlabel('垃圾类型', fontsize=12)
    ax1.set_ylabel('车辆数量', fontsize=12)
    ax1.set_xticks(x)
    ax1.set_xticklabels(garbage_types_list)
    ax1.legend()
    ax1.grid(axis='y', linestyle='--', alpha=0.7)

    # 右侧：成本增加百分比饼图
    original_total_cost = sum(calculate_route_cost(route, k)[0] for k in range(1, 5) for route in all_routes[k])
    constrained_total_cost = sum(calculate_route_cost(route, k)[0] for k in range(1, 5) for route in time_constrained_routes[k])

    cost_increase = constrained_total_cost - original_total_cost
    cost_increase_percent = (cost_increase / original_total_cost) * 100

    # 计算每种垃圾类型的成本增加
    cost_increases = []
    labels = []

    for k in range(1, 5):
        original_cost = sum(calculate_route_cost(route, k)[0] for route in all_routes[k])
        constrained_cost = sum(calculate_route_cost(route, k)[0] for route in time_constrained_routes[k])
        increase = constrained_cost - original_cost

        if increase > 0:
            cost_increases.append(increase)
            labels.append(f'{garbage_types[k]}\n(+{increase:.2f})')

    colors = ['#3498db', '#2ecc71', '#e74c3c', '#f1c40f']

    if cost_increases:
        ax2.pie(cost_increases, labels=labels, autopct='%1.1f%%', startangle=90, colors=colors[:len(cost_increases)])
        ax2.set_title(f'时间约束导致的成本增加分布\n(总增加: {cost_increase:.2f}, +{cost_increase_percent:.2f}%)', fontsize=14)
    else:
        ax2.text(0.5, 0.5, '无成本增加', ha='center', va='center', fontsize=14)
        ax2.set_title('时间约束导致的成本增加分布', fontsize=14)

    # 调整布局
    plt.tight_layout()
    plt.savefig("时间约束影响分析.png", dpi=300, bbox_inches='tight')
    plt.show()

# 使用示例
alphas = [0.7, 0.5, 0.3]
transport_costs = [2935.90, 2935.90, 2935.90]  # 不考虑时间约束
carbon_costs = [455.40, 455.40, 455.40]
total_costs = [2191.75, 1695.65, 1199.55]

plot_cost_comparison(alphas, transport_costs, carbon_costs, total_costs)

# 考虑时间约束的成本
transport_costs_time = [3308.07, 3308.07, 3308.07]
carbon_costs_time = [502.74, 502.74, 502.74]
total_costs_time = [2466.47, 1905.41, 1344.34]

plot_cost_comparison(alphas, transport_costs_time, carbon_costs_time, total_costs_time, True)

# 子问题1：建立以最小化每日总运输成本为目标的多车辆协同运输模型
def solve_subproblem1():
    """解决子问题1：建立以最小化每日总运输成本为目标的多车辆协同运输模型"""
    print("\n解决子问题1：建立以最小化每日总运输成本为目标的多车辆协同运输模型")
    start_time = time.time()

    # 求解所有垃圾类型的路径（α=1表示只考虑运输成本）
    all_routes = {}
    all_costs = {}
    total_cost = 0
    total_distance = 0
    total_vehicles = 0

    for k in range(1, 5):
        print(f"\n处理第{k}类垃圾 ({garbage_types[k]})...")
        routes, weights, volumes = savings_algorithm(k, alpha=1.0)

        # 计算该类垃圾的总成本和总距离
        type_cost = 0
        type_distance = 0

        for route in routes:
            # 计算成本
            cost, _, _ = calculate_route_cost(route, k, alpha=1.0)
            type_cost += cost

            # 计算距离
            route_distance = 0
            prev = 0  # 从垃圾处理厂出发
            for node in route:
                route_distance += dist_matrix[prev, node]
                prev = node
            # 返回垃圾处理厂
            route_distance += dist_matrix[prev, 0]
            type_distance += route_distance

        all_routes[k] = routes
        all_costs[k] = type_cost
        total_cost += type_cost
        total_distance += type_distance
        total_vehicles += len(routes)

        print(f"第{k}类垃圾共需{len(routes)}辆车")
        print(f"  运输成本: {type_cost:.2f}")
        print(f"  总距离: {type_distance:.2f}")

    print(f"\n总运输成本: {total_cost:.2f}")
    print(f"总距离: {total_distance:.2f}")
    print(f"总车辆数: {total_vehicles}")
    print(f"求解时间: {time.time() - start_time:.2f}秒")

    # 可视化路径
    visualize_routes_with_info(all_routes, "最小化每日总运输成本的多车辆协同路径",
                              total_cost, total_distance, total_vehicles)

    return all_routes, total_cost, total_distance, total_vehicles

# 带有总成本、总距离和车辆数量信息的路径可视化
def visualize_routes_with_info(all_routes, title, total_cost, total_distance, total_vehicles):
    """带有总成本、总距离和车辆数量信息的路径可视化"""
    plt.figure(figsize=(15, 12))

    # 设置背景色和网格
    plt.gca().set_facecolor('#f8f9fa')
    plt.grid(color='white', linestyle='-', linewidth=1, alpha=0.7)

    # 绘制收集点
    plt.scatter(points[:, 0], points[:, 1], c='#3498db', s=100, edgecolor='white', linewidth=1.5, label='收集点')

    # 绘制垃圾处理厂
    plt.scatter([0], [0], c='#e74c3c', s=300, marker='*', edgecolor='white', linewidth=1.5, label='垃圾处理厂')

    # 为每个收集点添加编号
    for i in range(n):
        plt.text(points[i, 0]+0.2, points[i, 1]+0.2, f'{i+1}', fontsize=10, color='black')

    # 垃圾类型和对应颜色
    colors = ['#2ecc71', '#9b59b6', '#f1c40f', '#1abc9c']
    markers = ['o', 's', '^', 'd']

    # 为每种垃圾类型创建一个图例条目
    for k in range(1, 5):
        plt.scatter([], [], c=colors[k-1], marker=markers[k-1], s=100,
                   label=f'{garbage_types[k]}路径')

    # 绘制路径
    for k in range(1, 5):
        routes = all_routes[k]
        for i, route in enumerate(routes):
            route_with_depot = [0] + route + [0]  # 添加处理厂作为起点和终点
            route_x = [all_points[j, 0] for j in route_with_depot]
            route_y = [all_points[j, 1] for j in route_with_depot]

            # 绘制路径线条
            plt.plot(route_x, route_y, c=colors[k-1], linewidth=2.5, alpha=0.8)

            # 添加路径箭头
            for j in range(len(route_with_depot)-1):
                dx = route_x[j+1] - route_x[j]
                dy = route_y[j+1] - route_y[j]
                plt.arrow(route_x[j], route_y[j], dx*0.9, dy*0.9,
                          head_width=0.3, head_length=0.5, fc=colors[k-1], ec=colors[k-1], alpha=0.8)

            # 在路径中间位置添加路径编号
            mid_idx = len(route) // 2
            if len(route) > 0:
                mid_point = route[mid_idx]
                plt.text(all_points[mid_point, 0], all_points[mid_point, 1]+0.5,
                         f'路径{k}-{i+1}', fontsize=12, color=colors[k-1],
                         bbox=dict(facecolor='white', alpha=0.7, edgecolor=colors[k-1]))

    # 设置标题和轴标签
    plt.title(title, fontsize=18, pad=20)
    plt.xlabel('X坐标', fontsize=14)
    plt.ylabel('Y坐标', fontsize=14)

    # 添加总成本、总距离和车辆数量信息
    info_text = f"总运输成本: {total_cost:.2f}\n总距离: {total_distance:.2f}\n总车辆数: {total_vehicles}"
    plt.figtext(0.5, 0.01, info_text, ha="center", fontsize=14,
               bbox=dict(facecolor='#f8f9fa', edgecolor='gray', boxstyle='round,pad=0.5'))

    # 添加图例
    plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=3, fontsize=12,
               frameon=True, facecolor='white', edgecolor='gray')

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(f"{title}.png", dpi=300, bbox_inches='tight')
    plt.show()

# 主函数
if __name__ == "__main__":
    print("解决第二问问题二：多车辆协同与碳排放控制优化")

    # 首先解决子问题1
    subproblem1_routes, subproblem1_cost, subproblem1_distance, subproblem1_vehicles = solve_subproblem1()

    # 设置权重系数
    alphas = [0.7, 0.5, 0.3]  # 分别代表偏重运输成本、平衡两者、偏重碳排放控制

    for alpha in alphas:
        print(f"\n权重系数 α = {alpha} (α越大越偏重运输成本，α越小越偏重碳排放控制)")
        start_time = time.time()

        # 求解所有垃圾类型的路径
        all_routes = {}
        all_costs = {}
        all_transport_costs = {}
        all_carbon_costs = {}
        total_cost = 0
        total_transport_cost = 0
        total_carbon_cost = 0

        for k in range(1, 5):
            print(f"\n处理第{k}类垃圾 ({garbage_types[k]})...")
            routes, weights, volumes = savings_algorithm(k, alpha)

            # 计算该类垃圾的总成本
            type_cost = 0
            type_transport_cost = 0
            type_carbon_cost = 0

            for route in routes:
                cost, transport_cost, carbon_cost = calculate_route_cost(route, k, alpha)
                type_cost += cost
                type_transport_cost += transport_cost
                type_carbon_cost += carbon_cost

            all_routes[k] = routes
            all_costs[k] = type_cost
            all_transport_costs[k] = type_transport_cost
            all_carbon_costs[k] = type_carbon_cost

            total_cost += type_cost
            total_transport_cost += type_transport_cost
            total_carbon_cost += type_carbon_cost

            print(f"第{k}类垃圾共需{len(routes)}辆车")
            print(f"  运输成本: {type_transport_cost:.2f}")
            print(f"  碳排放成本: {type_carbon_cost:.2f}")
            print(f"  综合成本: {type_cost:.2f}")

        print(f"\n总运输成本: {total_transport_cost:.2f}")
        print(f"总碳排放成本: {total_carbon_cost:.2f}")
        print(f"总综合成本: {total_cost:.2f}")
        print(f"求解时间: {time.time() - start_time:.2f}秒")

        # 可视化路径
        visualize_routes(all_routes, f"多车辆协同与碳排放控制优化路径", alpha)

        # 考虑时间约束
        print("\n考虑时间约束(8小时)后的结果:")
        time_constrained_routes = {}
        time_constrained_costs = {}
        time_constrained_transport_costs = {}
        time_constrained_carbon_costs = {}
        time_constrained_total_cost = 0
        time_constrained_total_transport_cost = 0
        time_constrained_total_carbon_cost = 0

        for k in range(1, 5):
            routes, weights, volumes = savings_algorithm(k, alpha)
            new_routes, new_weights, new_volumes = split_routes_by_time(routes, weights, volumes, k, max_time=8)

            # 计算该类垃圾的总成本
            type_cost = 0
            type_transport_cost = 0
            type_carbon_cost = 0

            for route in new_routes:
                cost, transport_cost, carbon_cost = calculate_route_cost(route, k, alpha)
                type_cost += cost
                type_transport_cost += transport_cost
                type_carbon_cost += carbon_cost

            time_constrained_routes[k] = new_routes
            time_constrained_costs[k] = type_cost
            time_constrained_transport_costs[k] = type_transport_cost
            time_constrained_carbon_costs[k] = type_carbon_cost

            time_constrained_total_cost += type_cost
            time_constrained_total_transport_cost += type_transport_cost
            time_constrained_total_carbon_cost += type_carbon_cost

            print(f"\n第{k}类垃圾共需{len(new_routes)}辆车")
            print(f"  运输成本: {type_transport_cost:.2f}")
            print(f"  碳排放成本: {type_carbon_cost:.2f}")
            print(f"  综合成本: {type_cost:.2f}")

        print(f"\n考虑时间约束后的总运输成本: {time_constrained_total_transport_cost:.2f}")
        print(f"考虑时间约束后的总碳排放成本: {time_constrained_total_carbon_cost:.2f}")
        print(f"考虑时间约束后的总综合成本: {time_constrained_total_cost:.2f}")

        # 可视化考虑时间约束的路径
        visualize_routes(time_constrained_routes, f"考虑时间约束的多车辆协同路径", alpha)

        # 分析时间约束对路径规划的影响
        print("\n时间约束对路径规划的影响分析:")
        for k in range(1, 5):
            original_routes = len(all_routes[k])
            constrained_routes = len(time_constrained_routes[k])

            if constrained_routes > original_routes:
                print(f"第{k}类垃圾 ({garbage_types[k]}): 由于时间约束，路径数量从{original_routes}增加到{constrained_routes}")

                # 找出被拆分的路径示例
                for i, route in enumerate(all_routes[k]):
                    route_time = calculate_route_time(route)
                    if route_time > 8:  # 超过8小时的路径
                        route_str = ' -> '.join(str(r) for r in route)
                        print(f"  示例: 路径 0 -> {route_str} -> 0 (时间: {route_time:.2f}小时) 需要拆分")

                        # 找出拆分后的路径
                        split_routes = []
                        for new_route in time_constrained_routes[k]:
                            if set(new_route).issubset(set(route)):
                                split_routes.append(new_route)

                        if split_routes:
                            print("  拆分为:")
                            for j, split_route in enumerate(split_routes):
                                split_str = ' -> '.join(str(r) for r in split_route)
                                split_time = calculate_route_time(split_route)
                                print(f"    路径 0 -> {split_str} -> 0 (时间: {split_time:.2f}小时)")

                        break  # 只显示一个示例
            else:
                print(f"第{k}类垃圾 ({garbage_types[k]}): 时间约束未导致路径拆分，路径数量保持为{original_routes}")

        # 绘制路径时间分析图
        plot_route_time_analysis(all_routes, time_constrained_routes)

        # 绘制时间约束对路径规划的影响分析图
        plot_time_constraint_impact(all_routes, time_constrained_routes)



