clear; clc;

% ===== 数据读取 =====
fileName = '附件1.xlsx';
opts = detectImportOptions(fileName,'VariableNamingRule','preserve');
tbl = readtable(fileName, opts);
tbl.Properties.VariableNames = {'id','x','y','w'};
tbl.w(1) = 0;  % 处理厂权重 = 0

coords = [tbl.x, tbl.y];
w = tbl.w;
n = height(tbl) - 1;  % 客户数量（不含处理厂）
Q = 5;               % 最大载重

% ===== 距离矩阵 =====
D = squareform(pdist(coords,'euclidean'));

% ===== Clarke-Wright 初始解 + 2-opt 微调 =====
base_routes = savingsCW(D, w, Q);
for k = 1:numel(base_routes)
    base_routes{k} = twoOpt(base_routes{k}, D);
end
route_chrom = convertRoutesToChromosome(base_routes, n);

% ===== 遗传算法参数 =====
pop_size = 100;
max_gen = 200;
pc = 0.7;
pm = 0.2;
elite_num = 5;

% ===== 初始化种群（含CW初始化）=====
population = zeros(pop_size, n);
population(1,:) = route_chrom;
for i = 2:pop_size
    population(i,:) = randperm(n) + 1;  % 编码为2~31
end

% ===== 遗传算法主循环 =====
best_distance = Inf;
best_solution = [];

best_dist_history = zeros(max_gen, 1);  

for gen = 1:max_gen
    fitness = zeros(pop_size, 1);
    routes_all = cell(pop_size, 1);

    for i = 1:pop_size
        [dist, routes] = decode_chromosome(population(i,:), w, Q, D);
        for k = 1:numel(routes)
            routes{k} = twoOpt(routes{k}, D);
        end
        dist = sum(cellfun(@(r) routeDistance(r, D), routes));
        fitness(i) = 1 / dist;
        routes_all{i} = routes;

        if dist < best_distance
            best_distance = dist;
            best_solution = population(i,:);
            best_routes = routes;
        end
    end

    best_dist_history(gen) = best_distance; 

    [~, idx] = sort(fitness, 'descend');
    new_pop = population(idx(1:elite_num), :);
    selected = roulette_wheel_selection(fitness);
    new_pop = [new_pop; population(selected(1:pop_size - elite_num), :)];

    for i = elite_num+1:2:pop_size-1
        if rand() < pc
            [new_pop(i,:), new_pop(i+1,:)] = ox_crossover(new_pop(i,:), new_pop(i+1,:));
        end
    end

    for i = elite_num+1:pop_size
        if rand() < pm
            new_pop(i,:) = swap_mutation(new_pop(i,:));
        end
    end

    population = new_pop;
    fprintf('Generation %d: Best Distance = %.2f km\n', gen, best_distance);
end

fprintf('\n—— 最优运输方案 ——\n');
totalDist = 0;
for k = 1:numel(best_routes)
    r = best_routes{k};
    d = routeDistance(r, D);
    load = sum(w(r(2:end-1)));
    totalDist = totalDist + d;
    fprintf('车辆%02d: 载重 = %.1f 吨  距离 = %.2f km  路线: ',k,load,d);
    fprintf('%d-',r(1:end-1)-1); fprintf('%d\n',r(end)-1);
end
fprintf('车辆数 = %d\n总行驶距离 = %.2f km\n', numel(best_routes), totalDist);

%% 绘图
figure; hold on; box on;axis equal;

title('垃圾运输路径（Hybrid GA + CW）');
xlabel('X 坐标'); ylabel('Y 坐标');

scatter(coords(2:end,1), coords(2:end,2), 50, 'b', 'filled','HandleVisibility', 'off');
scatter(coords(1,1), coords(1,2), 200, 'r*', 'filled','HandleVisibility', 'off');
text(coords(1,1), coords(1,2)+1, '处理厂 0', 'HorizontalAlignment', 'center');
for i = 2:size(coords,1)
    text(coords(i,1), coords(i,2)+0.5, sprintf('%d', i-1), 'FontSize', 15);
end

colors = slanCM(186,numel(best_routes));
for k = 1:numel(best_routes)
    r = best_routes{k};
    plot(coords(r,1), coords(r,2), '-o', 'Color', colors(k,:), 'LineWidth', 2, 'MarkerFaceColor', colors(k,:), 'DisplayName', sprintf('车辆 #%d',k));
end
legend('Location','eastoutside');

 xlim([-1 43]);
 ylim([-1 38]);
% 
% % 自动生成主刻度和次刻度
 xticks(0:5:40);
 yticks(0:5:35);

set(gcf, 'Position', [100 100 1000 850]);
set(gca, 'FontSize', 20);


% ===== 收敛曲线绘图 =====
figure;
plot(1:max_gen, best_dist_history, 'b-', 'LineWidth', 2);
xlabel('迭代代数');
ylabel('最优总行驶距离 (km)');
title('遗传算法收敛曲线');
grid on;

%%
function chrom = convertRoutesToChromosome(routes, n)
    all_nodes = [];
    for i = 1:length(routes)
        route = routes{i};
        all_nodes = [all_nodes, route(2:end-1)];
    end
    assert(length(all_nodes) == n);
    chrom = all_nodes;
end

function [dist, routes] = decode_chromosome(chrom, w, Q, D)
    depot = 1;
    routes = {};
    current = [depot];
    load = 0;
    for i = 1:length(chrom)
        node = chrom(i);  % 已经是2~31
        demand = w(node);
        if load + demand > Q
            current = [current, depot];
            routes{end+1} = current;
            current = [depot, node];
            load = demand;
        else
            current = [current, node];
            load = load + demand;
        end
    end
    current = [current, depot];
    routes{end+1} = current;
    dist = sum(cellfun(@(r) routeDistance(r, D), routes));
end

function selected = roulette_wheel_selection(fitness)
    if all(fitness == 0)
        selected = randi(length(fitness), size(fitness));
        return;
    end
    cum_prob = cumsum(fitness) / sum(fitness);
    selected = arrayfun(@(~) find(rand() <= cum_prob, 1), 1:length(fitness));
end

function [child1, child2] = ox_crossover(parent1, parent2)
    n = length(parent1);
    cut_points = sort(randperm(n, 2));
    segment = parent1(cut_points(1):cut_points(2));
    remaining1 = parent2(~ismember(parent2, segment));
    remaining2 = parent1(~ismember(parent1, segment));
    child1 = [remaining1(1:cut_points(1)-1), segment, remaining1(cut_points(1):end)];
    child2 = [remaining2(1:cut_points(1)-1), segment, remaining2(cut_points(1):end)];
end

function mutated = swap_mutation(chrom)
    idx = randperm(length(chrom), 2);
    mutated = chrom;
    mutated(idx) = mutated(fliplr(idx));
end



function routes = savingsCW(D, w, Q)
n = size(D,1)-1;
depot = 1;
routes = arrayfun(@(i){[depot, i+1, depot]},1:n);
S = [];
for i = 1:n-1
    for j = i+1:n
        S = [S; i, j, D(depot,i+1)+D(depot,j+1)-D(i+1,j+1)];
    end
end
S = sortrows(S,-3);
for s = 1:size(S,1)
    i = S(s,1); j = S(s,2);
    idx_i = find(cellfun(@(r) any(r==i+1), routes),1);
    idx_j = find(cellfun(@(r) any(r==j+1), routes),1);
    if isempty(idx_i) || isempty(idx_j) || idx_i==idx_j
        continue;
    end
    ri = routes{idx_i};
    rj = routes{idx_j};
    canMerge1 = (ri(end-1)==i+1)&&(rj(2)==j+1);
    canMerge2 = (rj(end-1)==j+1)&&(ri(2)==i+1);
    if ~(canMerge1 || canMerge2)
        continue;
    end
    newLoad = sum(w([ri(2:end-1), rj(2:end-1)]));
    if newLoad > Q
        continue;
    end

    if canMerge1
        newRoute = [ri(1:end-1), rj(2:end)];
    else
        newRoute = [rj(1:end-1), ri(2:end)];
    end

    routes([idx_i, idx_j]) = [];
    routes{end+1} = newRoute;
end
end


function route = twoOpt(route, D)
improved = true;
while improved
    improved = false;
    for i = 2:length(route)-2
        for k = i+1:length(route)-1
            delta = D(route(i-1),route(k)) + D(route(i),route(k+1)) ...
                  - D(route(i-1),route(i)) - D(route(k),route(k+1));
            if delta < -1e-6
                route(i:k) = route(k:-1:i);
                improved = true;
            end
        end
    end
end
end


function d = routeDistance(route,D)
d = sum(D(sub2ind(size(D), route(1:end-1), route(2:end))));
end

