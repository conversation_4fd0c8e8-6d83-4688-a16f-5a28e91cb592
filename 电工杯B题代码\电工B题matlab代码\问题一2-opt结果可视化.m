%% 可视化精美展示 for 16 辆车 2-opt 结果

% 客户坐标（与之前 data 中的 coords 对应）
coords = [ ...
    12,  8;
    5,   15;
    20,  30;
    25,  10;%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    35,  22;
    18,   5;
    30,  35;
    10,  25;
    22,  18;
    38,  15;
    5,    8;
    15,  32;
    28,   5;
    30,  12;
    10,  10;
    20,  20;
    35,  30;
    8,   22;
    25,  25;
    32,   8;
    15,   5;
    28,  20;
    38,  25;
    10,  30;
    20,  10;
    30,  18;
    5,   25;
    18,  30;
    35,  10;
    22,  35 ];
depot = [0,0];

% 各车路线（以客户编号为索引；0 表示厂区）
routes = {
    [0 1 23 0]
    [0 2 18 27 0]
    [0 3 28 0]
    [0 4 0]
    [0 5 22 0]
    [0 6 20 0]
    [0 7 30 0]
    [0 8 12 24 0]
    [0 9 16 0]
    [0 10 25 0]
    [0 11 17 0]
    [0 13 0]%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
    [0 14 21 0]
    [0 15 19 0]
    [0 26 0]
    [0 29 0]
};

% 各车载重与里程
loads = [4.70,4.60,4.10,3.10,4.90,4.90,4.80,4.60,4.90,5.00,5.00,3.20,4.20,4.70,3.60,3.70];
dists = [90.97,53.16,73.04,53.85,83.03,65.98,95.44,72.54,59.54,81.90,92.73,56.89,64.68,70.71,69.97,72.80];

%% 1) 路线全景图：彩色渐变 + 标注
figure('Name','Route Panorama','NumberTitle','off','Color','w');
hold on; grid on; axis equal;
cmap = autumn(length(routes));
for k = 1:length(routes)
    rt = routes{k};
    % 构造轨迹点坐标
    pts = zeros(length(rt), 2);
    for t = 1:length(rt)
        if rt(t) == 0
            pts(t, :) = depot;
        else
            pts(t, :) = coords(rt(t), :);
        end
    end
    plot(pts(:,1), pts(:,2), '-o', 'LineWidth',1.8, ...
         'Color',cmap(k,:), 'MarkerFaceColor','w', 'MarkerEdgeColor',cmap(k,:));
    mid = round(size(pts,1)/2);
    text(pts(mid,1), pts(mid,2), sprintf('V%d',k), ...
         'FontSize',11, 'FontWeight','bold', 'Color',cmap(k,:));
end
plot(depot(1), depot(2), 'ks', 'MarkerSize',12, 'MarkerFaceColor','k');
text(depot(1), depot(2), '  Depot','FontSize',12,'FontWeight','bold');
title('All Vehicle Routes'); xlabel('X (km)'); ylabel('Y (km)');

%% 2) 载重 vs 距离 甘特式条形图
figure('Name','Load vs Distance','NumberTitle','off','Color','w');
yyaxis left
barh(1:16, loads, 0.4, 'FaceColor',[0.2 0.6 1], 'EdgeColor','none');
xlabel('Load (t)'); xlim([0, max(loads)*1.1]);
ylabel('Vehicle');
set(gca,'YDir','reverse');
yyaxis right
barh((1:16)+0.4, dists, 0.4, 'FaceColor',[1 0.5 0.2], 'EdgeColor','none');
xlabel('Distance (km)'); xlim([0, max(dists)*1.1]);
legend({'Load','Distance'}, 'Location','southoutside','Orientation','horizontal');
title('Vehicle Load and Distance Comparison');
yticks(1:16); yticklabels(arrayfun(@(k)sprintf('V%d',k),1:16,'Uni',false));

%% 3) 载重—里程 散点矩阵，大小/颜色编码
figure('Name','Load-Distance Scatter','NumberTitle','off','Color','w');
scatter(loads, dists, loads*50, dists, 'filled');
colormap(flipud(parula)); colorbar;
xlabel('Load (t)'); ylabel('Distance (km)');
title('Load vs. Distance');
p = polyfit(loads, dists,1);
xv = linspace(min(loads),max(loads),100);
hold on; plot(xv, polyval(p,xv), '--k','LineWidth',1.2);
text(mean(loads), mean(dists), sprintf('y=%.2fx+%.1f',p), ...
     'FontSize',11,'BackgroundColor','w');

%% 4) 按距离排序的瀑布图
[sd, idx] = sort(dists,'descend');
labels = arrayfun(@(k)sprintf('V%d',k), idx,'Uni',false);
figure('Name','Distance Waterfall','NumberTitle','off','Color','w');
bar(sd, 'FaceColor',[0.8 0.2 0.2]);
set(gca,'XTick',1:16,'XTickLabel',labels);
xtickangle(45);
ylabel('Distance (km)');
title('Vehicle Distance Waterfall');

%% 5) 累计距离曲线
cumd = cumsum(sd);
figure('Name','Cumulative Distance','NumberTitle','off','Color','w');
plot(1:16, cumd, '-s','LineWidth',1.6,'MarkerSize',8,'MarkerFaceColor','w');
xticks(1:16); xticklabels(labels);
xtickangle(45);
xlabel('Vehicle (sorted by distance)'); ylabel('Cumulative Distance (km)');
title('Cumulative Distance Covered');

% 统一美化
set(findall(gcf,'-property','FontName'),'FontName','Helvetica');%论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/ecc0f17daef74923cddd5473a2ebffc8ebecfd16
set(findall(gcf,'-property','FontSize'),'FontSize',11);

