%% 核心求解函数：单车型CVRP
function [routes, total_distance, vehicle_count] = solve_single_type_cvrp(...
    coords, demands, capacity, dist_matrix, k, type_name)
    
    n = size(coords, 1) - 1;
    
    % 过滤出有需求的收集点
    valid_points = find(demands(2:end) > 0); % 排除处理厂，找有需求的点
    
    if isempty(valid_points)
        routes = {};
        total_distance = 0;
        vehicle_count = 0;
        return;
    end
    
    fprintf('  有效收集点数: %d个\n', length(valid_points));
    
    % 提取有效点的数据
    valid_coords = [coords(1,:); coords(valid_points+1,:)]; % +1因为索引偏移
    valid_demands = [0; demands(valid_points+1)];
    
    % 重新计算距离矩阵（仅针对有效点）
    m = length(valid_points);
    valid_dist_matrix = zeros(m+1, m+1);
    
    for i = 1:m+1
        for j = 1:m+1
            if i ~= j
                if i == 1
                    orig_i = 1;
                else
                    orig_i = valid_points(i-1) + 1;
                end
                
                if j == 1
                    orig_j = 1;
                else
                    orig_j = valid_points(j-1) + 1;
                end
                
                valid_dist_matrix(i,j) = dist_matrix(orig_i, orig_j);
            end
        end
    end
    
    % 第一阶段：扫描算法聚类
    fprintf('  第一阶段：扫描算法聚类...\n');
    clusters = sweep_algorithm_modified(valid_coords, valid_demands, capacity);
    
    % 第二阶段：路径优化
    fprintf('  第二阶段：路径优化...\n');
    routes = cell(length(clusters), 1);
    total_distance = 0;
    
    for i = 1:length(clusters)
        cluster = clusters{i};
        optimized_route = optimize_route_modified(cluster, valid_dist_matrix);
        
        % 将局部索引转换回原始索引
        original_route = convert_to_original_indices(optimized_route, valid_points);
        routes{i} = original_route;
        
        route_distance = calculate_route_distance(original_route, dist_matrix);
        total_distance = total_distance + route_distance;
    end
    
    vehicle_count = length(clusters);
end
