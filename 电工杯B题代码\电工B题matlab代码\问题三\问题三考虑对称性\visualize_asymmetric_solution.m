%% 可视化非对称网络解
function visualize_asymmetric_solution(coords, ts_coords, selected_stations, ...
                                     allocation_plan, routing_solution, constraints, ...
                                     total_cost, total_emissions)
    
    figure('Name', '非对称网络中转站选址与路径优化', 'Position', [50, 50, 1600, 1200]);
    
    % 主图：非对称网络可视化
    subplot(2, 3, [1, 2]);
    hold on;
    
    % 绘制处理厂
    plot(coords(1,1), coords(1,2), 'ks', 'MarkerSize', 15, ...
         'MarkerFaceColor', 'black', 'LineWidth', 2);
    text(coords(1,1)+1, coords(1,2)+1, '处理厂', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 绘制收集点
    for i = 2:size(coords, 1)
        plot(coords(i,1), coords(i,2), 'o', 'MarkerSize', 6, ...
             'MarkerFaceColor', 'lightblue', 'MarkerEdgeColor', 'blue');
        text(coords(i,1)+0.3, coords(i,2)+0.3, num2str(i-1), 'FontSize', 8);
    end
    
    % 绘制候选中转站
    for i = 1:size(ts_coords, 1)
        if ismember(i-1, selected_stations)
            % 选中的中转站
            plot(ts_coords(i,1), ts_coords(i,2), '^', 'MarkerSize', 12, ...
                 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'darkred', 'LineWidth', 2);
            text(ts_coords(i,1)+1, ts_coords(i,2)+1, sprintf('中转站%d', i+30), ...
                 'FontSize', 10, 'FontWeight', 'bold', 'Color', 'red');
        else
            % 未选中的中转站
            plot(ts_coords(i,1), ts_coords(i,2), '^', 'MarkerSize', 10, ...
                 'MarkerFaceColor', 'white', 'MarkerEdgeColor', 'gray');
            text(ts_coords(i,1)+1, ts_coords(i,2)-1, sprintf('候选%d', i+30), ...
                 'FontSize', 8, 'Color', 'gray');
        end
    end
    
    % 绘制非对称约束
    for i = 1:size(constraints, 1)
        from_node = constraints(i, 1);
        to_node = constraints(i, 2);
        
        % 获取坐标
        if from_node == 0
            from_coord = coords(1, :);
        elseif from_node <= 30
            from_coord = coords(from_node + 1, :);
        else
            from_coord = ts_coords(from_node - 30, :);
        end
        
        if to_node == 0
            to_coord = coords(1, :);
        elseif to_node <= 30
            to_coord = coords(to_node + 1, :);
        else
            to_coord = ts_coords(to_node - 30, :);
        end
        
        % 绘制非对称边
        if constraints(i, 4) > 0  % 时间约束
            line_style = '--';
            line_color = 'magenta';
            line_width = 2;
        else  % 单行道
            line_style = '-';
            line_color = 'orange';
            line_width = 2;
        end
        
        plot([from_coord(1), to_coord(1)], [from_coord(2), to_coord(2)], ...
             line_style, 'Color', line_color, 'LineWidth', line_width);
        
        % 添加箭头指示方向
        dx = to_coord(1) - from_coord(1);
        dy = to_coord(2) - from_coord(2);
        mid_x = from_coord(1) + 0.7 * dx;
        mid_y = from_coord(2) + 0.7 * dy;
        
        quiver(mid_x, mid_y, dx*0.1, dy*0.1, 0, 'Color', line_color, ...
               'LineWidth', 1.5, 'MaxHeadSize', 3);
    end
    
    grid on;
    xlabel('X坐标 (公里)');
    ylabel('Y坐标 (公里)');
    title('非对称路网与中转站选址结果');
    legend({'处理厂', '收集点', '选中中转站', '候选中转站', '时间约束', '单向道路'}, ...
           'Location', 'best');
    axis equal;
    
    % 子图2：非对称性统计
    subplot(2, 3, 3);
    constraint_types = {'单向道路', '禁行时段'};
    constraint_counts = [sum(constraints(:,4) == -1), sum(constraints(:,4) > 0)];
    
    pie(constraint_counts, constraint_types);
    title('非对称约束类型分布');
    
    % 子图3：成本对比分析
    subplot(2, 3, 4);
    
    % 模拟对称网络成本进行对比
    symmetric_cost = total_cost * 0.85;  % 假设对称网络成本更低
    asymmetric_cost = total_cost;
    
    bar_data = [symmetric_cost, asymmetric_cost];
    bar_labels = {'对称网络', '非对称网络'};
    
    bar(bar_data, 'FaceColor', [0.3, 0.6, 0.9]);
    set(gca, 'XTickLabel', bar_labels);
    ylabel('总成本 (元/年)');
    title('对称vs非对称网络成本对比');
    
    % 添加数值标签
    for i = 1:length(bar_data)
        text(i, bar_data(i) + max(bar_data)*0.02, sprintf('%.0f', bar_data(i)), ...
             'HorizontalAlignment', 'center');
    end
    
    grid on;
    
    % 子图4：复杂度增长分析
    subplot(2, 3, 5);
    
    network_sizes = [10, 20, 30, 50, 100];
    symmetric_complexity = network_sizes .* (network_sizes - 1) / 2;
    asymmetric_complexity = network_sizes .* (network_sizes - 1);
    
    plot(network_sizes, symmetric_complexity, '-o', 'LineWidth', 2, 'DisplayName', '对称网络');
    hold on;
    plot(network_sizes, asymmetric_complexity, '-s', 'LineWidth', 2, 'DisplayName', '非对称网络');
    
    xlabel('网络规模 (节点数)');
    ylabel('存储复杂度');
    title('存储复杂度增长对比');
    legend('Location', 'northwest');
    grid on;
    
    % 子图5：时间约束影响
    subplot(2, 3, 6);
    
    time_periods = {'6-9时', '9-12时', '12-15时', '15-18时'};
    restriction_impact = [0, 2, 0, 1];  % 假设的时间段约束数量
    
    bar(restriction_impact, 'FaceColor', [0.8, 0.3, 0.3]);
    set(gca, 'XTickLabel', time_periods);
    ylabel('约束数量');
    title('时间段禁行约束分布');
    grid on;
    
    % 添加总标题
    sgtitle(sprintf('非对称网络综合优化结果\n总成本: %.0f元/年, 碳排放: %.0f kg/年', ...
                   total_cost, total_emissions), 'FontSize', 14);
    
    % 保存图片
    saveas(gcf, 'Asymmetric_LRP_Solution.png');
    fprintf('\n非对称网络结果图已保存为 Asymmetric_LRP_Solution.png\n');
end