import pandas as pd
import numpy as np
from scipy.spatial.distance import cdist
import ast
import matplotlib.pyplot as plt

# === 数据读取 ===
df_location = pd.read_excel("附件1.xlsx", sheet_name="附件130个垃圾分类收集点坐标及总垃圾量", skiprows=2, usecols="A:D")
df_location.columns = ["ID", "X", "Y", "TotalWeight"]

df_garbage = pd.read_excel("附件3.xlsx", sheet_name="附件330个收集点的4类垃圾量分布", skiprows=1)
df_garbage.columns = ["ID", "厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"]

df_transfer = pd.read_excel("附件4.xlsx", sheet_name="附件4中转站候选位置及参数", skiprows=1)
df_transfer.columns = ["中转站编号", "X", "Y", "建设成本", "时间窗口", "存储容量"]
df_transfer["存储容量"] = df_transfer["存储容量"].apply(ast.literal_eval)
df_transfer["时间窗口"] = df_transfer["时间窗口"].apply(ast.literal_eval)

# === 参数定义 ===
garbage_types = ["厨余垃圾", "可回收物", "有害垃圾", "其他垃圾"]
vehicle_params = {
    "厨余垃圾": {"Q": 8, "C": 2.5, "α": 0.8, "β": 0.3, "v": 30},
    "可回收物": {"Q": 6, "C": 2.0, "α": 0.6, "β": 0.2, "v": 40},
    "有害垃圾": {"Q": 3, "C": 5.0, "α": 1.2, "β": 0.5, "v": 25},
    "其他垃圾": {"Q": 10, "C": 1.8, "α": 0.7, "β": 0.25, "v": 35}
}
T_MAX = 24  # 每辆车最多服务8小时

# === 合并点信息并计算距离矩阵 ===
df_points = pd.merge(df_location, df_garbage, on="ID")
point_coords = df_points[["X", "Y"]].to_numpy()
transfer_coords = df_transfer[["X", "Y"]].to_numpy()
all_coords = np.vstack([transfer_coords, point_coords])
dist_matrix = cdist(all_coords, all_coords)

# === 函数定义 ===
def nearest_neighbor_path(nodes, depot):
    unvisited = set(nodes)
    tour = [depot]
    current = depot
    while unvisited:
        next_node = min(unvisited, key=lambda x: dist_matrix[current][x])
        tour.append(next_node)
        unvisited.remove(next_node)
        current = next_node
    tour.append(depot)
    return tour

def split_by_capacity_and_time(tour, demand_map, Q, max_distance):
    trips = []
    trip = [tour[0]]
    load = 0
    dist = 0
    for i in range(1, len(tour)-1):
        last = trip[-1]
        node = tour[i]
        delta = dist_matrix[last][node]
        return_home = dist_matrix[node][tour[0]]
        if load + demand_map[node] <= Q and dist + delta + return_home <= max_distance:
            trip.append(node)
            load += demand_map[node]
            dist += delta
        else:
            trip.append(tour[0])
            trips.append(trip)
            trip = [tour[0], node]
            load = demand_map[node]
            dist = dist_matrix[tour[0]][node]
    trip.append(tour[0])
    trips.append(trip)
    return trips

def route_distance(route):
    return sum(dist_matrix[route[i]][route[i+1]] for i in range(len(route)-1))

def route_weight(route, demand_map):
    return sum(demand_map.get(i, 0) for i in route if i != route[0])

def two_opt(route):
    best = route
    improved = True
    while improved:
        improved = False
        for i in range(1, len(best)-2):
            for j in range(i+1, len(best)-1):
                if j - i == 1: continue
                new = best[:i] + best[i:j][::-1] + best[j:]
                if route_distance(new) < route_distance(best):
                    best = new
                    improved = True
    return best

# === 主调度循环 ===
all_results = []
for gtype in garbage_types:
    w_k = df_points[gtype].to_numpy()
    capacity_k = [c[garbage_types.index(gtype)] for c in df_transfer["存储容量"]]
    assignments = -np.ones(len(df_points), dtype=int)
    used_capacity = np.zeros(len(df_transfer))
    
    # 收集点分配至中转站
    d_point_to_station = cdist(df_points[["X", "Y"]], df_transfer[["X", "Y"]])
    for i in range(len(df_points)):
        for j in np.argsort(d_point_to_station[i]):
            if used_capacity[j] + w_k[i] <= capacity_k[j]:
                assignments[i] = j
                used_capacity[j] += w_k[i]
                break

    df_points[f"{gtype}_分配中转站"] = assignments

    # 路径规划
    for j in range(len(df_transfer)):
        assigned_indices = df_points[df_points[f"{gtype}_分配中转站"] == j].index
        if len(assigned_indices) == 0:
            continue

        # 索引转换
        nodes = [i + len(df_transfer) for i in assigned_indices]
        depot = j
        param = vehicle_params[gtype]
        max_dist = param["v"] * T_MAX

        demands = {n: w_k[n - len(df_transfer)] for n in nodes}
        path = nearest_neighbor_path(nodes, depot)
        split_trips = split_by_capacity_and_time(path, demands, param["Q"], max_dist)

        for t, trip in enumerate(split_trips):
            opt_trip = two_opt(trip)
            d = route_distance(opt_trip)
            w = route_weight(opt_trip, demands)
            cost = round(d * param["C"], 2)
            emission = round(param["α"] * d + param["β"] * w, 2)
            all_results.append({
                "垃圾类型": gtype,
                "中转站编号": df_transfer.loc[j, "中转站编号"],
                "车辆编号": f"{gtype}-车{t+1}",
                "运输路径": " -> ".join(map(str, opt_trip)),
                "运输距离(km)": round(d, 2),
                "垃圾重量(t)": round(w, 2),
                "运输成本(元)": cost,
                "碳排放(kg)": emission
            })

# === 汇总与输出 ===
df_result = pd.DataFrame(all_results)
total_cost = df_result["运输成本(元)"].sum()
total_emission = df_result["碳排放(kg)"].sum()

print(df_result.to_string(index=False))
print(f"\n✅ 总运输成本：{round(total_cost, 2)} 元")
print(f"✅ 总碳排放：{round(total_emission, 2)} 公斤")
