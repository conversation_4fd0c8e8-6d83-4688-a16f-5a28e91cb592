%% 非对称网络设施选址
function [selected_stations, allocation_plan, facility_cost] = ...
    solve_asymmetric_facility_location(coords, ts_coords, ts_costs, ...
    ts_capacities, demands, dist_matrix, C)
    
    n = size(coords, 1) - 1;
    m = size(ts_coords, 1);
    total_demands = sum(demands(2:end, :), 1);
    
    fprintf('  使用非对称距离计算运输成本...\n');
    
    % 计算非对称运输成本矩阵
    transport_costs = zeros(n, m);
    for i = 1:n
        for j = 1:m
            % 收集点i到中转站j再到处理厂的非对称距离
            dist_to_ts = dist_matrix(i+1, n+1+j);
            dist_ts_to_depot = dist_matrix(n+1+j, 1);
            transport_costs(i, j) = dist_to_ts + dist_ts_to_depot;
        end
    end
    
    % 贪心选址算法（考虑非对称性）
    selected_stations = [];
    remaining_demand = total_demands;
    facility_cost = 0;
    allocation_plan = containers.Map('KeyType', 'int32', 'ValueType', 'any');
    
    while any(remaining_demand > 0) && length(selected_stations) < m
        best_station = -1;
        best_benefit = -inf;
        best_allocation = [];
        
        for j = 1:m
            if ismember(j, selected_stations)
                continue;
            end
            
            [benefit, temp_allocation] = evaluate_asymmetric_station_benefit(...
                j, ts_capacities(j, :), remaining_demand, ...
                transport_costs(:, j), ts_costs(j), demands);
            
            if benefit > best_benefit
                best_benefit = benefit;
                best_station = j;
                best_allocation = temp_allocation;
            end
        end
        
        if best_station > 0
            selected_stations = [selected_stations, best_station - 1]; % 转为0-based
            allocation_plan(best_station - 1) = best_allocation;
            facility_cost = facility_cost + ts_costs(best_station);
            
            % 更新剩余需求
            allocated_demand = zeros(1, 4);
            for point = best_allocation
                allocated_demand = allocated_demand + demands(point+1, :);
            end
            remaining_demand = max(0, remaining_demand - allocated_demand);
            
            fprintf('    选择中转站 %d, 服务收集点 %d 个\n', ...
                    best_station + 30, length(best_allocation));
        else
            break;
        end
    end
    
    % 处理未分配的收集点
    handle_unallocated_points_asymmetric(selected_stations, allocation_plan, ...
                                       transport_costs, n);
end

