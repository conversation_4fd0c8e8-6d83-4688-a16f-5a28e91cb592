%% PROBLEM3_INITIAL_VISUALS.M
% 问题三初始数据精美可视化

clear; clc; close all;

%% 1. 输入数据
% 编号 1–30 为收集点，31 为中转站
pts = [ ...
     0,    0;    % 0: 处理厂
    12,    8;
     5,   15;
    20,   30;
    25,   10;
    35,   22;
    18,    5;
    30,   35;
    10,   25;
    22,   18;
    38,   15;
     5,    8;
    15,   32;
    28,    5;
    30,   12;
    10,   10;
    20,   20;
    35,   30;
     8,   22;
    25,   25;
    32,    8;
    15,    5;
    28,   20;
    38,   25;
    10,   30;
    20,   10;
    30,   18;
     5,   25;
    18,   30;
    35,   10;
    22,   35;
    % 转运站 31
    25,   15
];
w = [ ...
    0,    0,    0,    0;    % 0: 处理厂
    0.72, 0.12, 0.06, 0.30;
    1.38, 0.23, 0.05, 0.64;
    1.08, 0.18, 0.04, 0.50;
    1.55, 0.31, 0.06, 1.18;
    1.62, 0.27, 0.05, 0.76;
    1.76, 0.384,0.096,0.96;
    0.77, 0.168,0.042,0.42;
    1.02, 0.238,0.068,0.374;
    1.32, 0.176,0.044,0.66;
    1.45, 0.30, 0.075,0.675;
    1.35, 0.27, 0.108,0.972;
    1.87, 0.51, 0.068,0.952;
    2.58, 0.516,0.129,1.075;
    1.134,0.21, 0.063,0.693;
    0.78, 0.13, 0.065,0.325;
    0.768,0.192,0.080,0.56;
    0.72, 0.27, 0.090,0.72;
    1.595,0.348,0.087,0.87;
    1.50, 0.36, 0.090,1.05;
    1.08, 0.18, 0.090,0.45;
    0.912,0.19, 0.038,0.76;
    0.90, 0.195,0.075,0.33;
    0.99, 0.27, 0.072,0.468;
    1.44, 0.24, 0.048,0.672;
    1.74, 0.319,0.116,0.725;
    1.17, 0.39, 0.130,0.91;
    1.70, 0.34, 0.170,1.19;
    2.64, 0.66, 0.044,1.056;
    0.864,0.216,0.072,0.648;
    0.986,0.204,0.085,0.425;
    0,    0,    0,    0      % 31: 中转站
];

types = {'厨余','可回收','有害','其他'};
colors = lines(4);

%% 2. 子图1：四类垃圾量气泡分布
figure('Name','垃圾产量气泡图','NumberTitle','off');
hold on; grid on;
scatter(pts(1,1),pts(1,2),120,'ks','filled'); text(pts(1,1),pts(1,2),' 厂区');
for k = 1:4
    sz = 50 + 200*(w(2:end,k)/max(w(2:end,k)));
    scatter(pts(2:end,1),pts(2:end,2),sz,colors(k,:),'filled','MarkerFaceAlpha',0.6);
end
legend(['厂区', types], 'Location','bestoutside');
title('各收集点四类垃圾产量分布');
xlabel('X (km)'); ylabel('Y (km)');

%% 3. 子图2：四类垃圾累计产量柱状图
figure('Name','累计产量柱状图','NumberTitle','off');
total = sum(w(2:end,:),1);
bar(total,'FaceColor',[.2 .6 .5]);
set(gca,'XTickLabel',types);
title('四类垃圾累计产量对比');
ylabel('总产量 (吨)');
grid on;

%% 4. 子图3：车辆参数对比雷达图
Qk = [8,6,3,10];
Vk = [20,25,10,18];
Ck = [2.5,2.0,5.0,1.8];
P = [Qk; Vk; Ck]';
P = P./max(P);    % 归一化
theta = linspace(0,2*pi,3+1);
labels = {'载重 Q_k','容积 V_k','距离成本 C_k'};
figure('Name','车辆参数雷达图','NumberTitle','off');
ax = polaraxes; hold on;
for k = 1:4
    vals = [P(k,:), P(k,1)];
    polarplot(theta, vals,'-o','Color',colors(k,:),'LineWidth',1.5);
end
ax.ThetaTick = rad2deg(theta(1:3));
ax.ThetaTickLabel = labels;
legend(types,'Location','eastoutside');
title('四类型车辆参数（归一化）');

%% 5. 子图4：特殊单行道示意与时间窗注记
figure('Name','路网与时间窗示意','NumberTitle','off'); hold on; grid on;
% 绘厂区及收集点
scatter(pts(1,1),pts(1,2),120,'ks','filled'); text(pts(1,1),pts(1,2),' 处理厂');
scatter(pts(2:31,1),pts(2:31,2),60,'ko');
for i=2:31, text(pts(i,1),pts(i,2),num2str(i-1)); end
% 标注特殊单行道
plot([pts(5,1),pts(32,1)],[pts(5,2),pts(32,2)],'r-','LineWidth',2);
text(mean([pts(5,1),pts(32,1)]),mean([pts(5,2),pts(32,2)]), {'4→31:18km','31→4:15km'},'Color','r');
plot([pts(27,1),pts(28,1)],[pts(27,2),pts(28,2)],'r--','LineWidth',2);
text(mean([pts(27,1),pts(28,1)]),mean([pts(27,2),pts(28,2)]),{'27→28:14km','28→27:18km'},'Color','r');
% 标注禁行时段（示意文字）
text(pts(23,1),pts(23,2)+1,'9:00–12:00 禁行','Color','m');
text(pts(10,1),pts(10,2)-1,'9:00–11:00 禁行','Color','m');
title('特殊单行道与禁行时段示意');
xlabel('X (km)'); ylabel('Y (km)');
legend({'厂区','收集点','单行道','时间窗'},'Location','southoutside');

