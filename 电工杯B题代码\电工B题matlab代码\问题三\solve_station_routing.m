%% 单中转站路径优化
function [routes, cost, emissions] = solve_station_routing(...
    station_id, assigned_points, coords, ts_coords, demands, ...
    dist_matrix, Q, C, alpha, beta, time_window, depot_hours, vehicle_speed)
    
    routes = cell(4, 1);
    cost = 0;
    emissions = 0;
    
    % 调试信息
    fprintf('    调试：station_id = %d, ts_coords大小 = [%d, %d]\n', ...
            station_id, size(ts_coords, 1), size(ts_coords, 2));
    
    % station_id 现在是0-4的索引，转换为MATLAB的1-based索引
    station_local_idx = station_id + 1;
    
    % 检查索引有效性
    if station_local_idx < 1 || station_local_idx > size(ts_coords, 1)
        fprintf('    错误：计算的索引 %d 超出ts_coords范围 [1, %d]\n', ...
                station_local_idx, size(ts_coords, 1));
        
        % 尝试直接使用station_id作为1-based索引
        if station_id >= 1 && station_id <= size(ts_coords, 1)
            station_local_idx = station_id;
            fprintf('    使用直接索引 %d\n', station_local_idx);
        else
            fprintf('    无法修复索引，返回空结果\n');
            return;
        end
    end
    
    % 中转站在全局坐标系中的索引
    station_global_idx = size(coords, 1) + station_local_idx;
    
    % 为每种垃圾类型优化路径
    for k = 1:4
        if isempty(assigned_points)
            routes{k} = [];
            continue;
        end
        
        % 提取k类垃圾的需求
        k_demands = zeros(1, length(assigned_points));
        for j = 1:length(assigned_points)
            point_idx = assigned_points(j);
            k_demands(j) = demands(point_idx + 1, k);
        end
        
        % 过滤有需求的点
        valid_points = assigned_points(k_demands > 0);
        if isempty(valid_points)
            routes{k} = [];
            continue;
        end
        
        % 构造两段式路径：收集点 -> 中转站 -> 处理厂
        [k_routes, k_cost, k_emissions] = optimize_two_stage_routes(...
            valid_points, station_global_idx, dist_matrix, ...
            k_demands(k_demands > 0), Q(k), C(k), alpha(k), beta(k), ...
            time_window, depot_hours, vehicle_speed);
        
        routes{k} = k_routes;
        cost = cost + k_cost;
        emissions = emissions + k_emissions;
    end
end