import numpy as np
import itertools
import matplotlib.pyplot as plt

# Input data: 0 is depot, 1..n are customers
# Columns: x, y, demand
data = np.array([
    [0,   0,  0],    # 0: depot
    [12,  8,  1.2],
    [5,   15, 2.3],
    [20,  30, 1.8],
    [25,  10, 3.1],
    [35,  22, 2.7],
    [18,   5, 1.5],
    [30,  35, 2.9],
])

coords = data[:, :2]
demand = data[:, 2]
Q = 5.0  # vehicle capacity
n = len(coords) - 1  # number of customers


def route_distance(route, coords):
    """
    Compute Euclidean distance of a route represented as a sequence of node indices.
    Includes return to depot.
    """
    dist = 0.0
    for i in range(len(route) - 1):
        p, q = route[i], route[i+1]
        dist += np.linalg.norm(coords[p] - coords[q])
    return dist


def vrp_bruteforce(coords, demand, Q):
    """
    Solve the Vehicle Routing Problem by brute-force enumeration of feasible subsets.
    Returns list of best routes and total distance.
    """
    n = coords.shape[0] - 1
    subsets = []

    # 1) Precompute all feasible customer subsets and their best route
    customers = list(range(1, n+1))
    for r in range(1, n+1):
        for combo in itertools.combinations(customers, r):
            total_demand = sum(demand[list(combo)])
            if total_demand > Q:
                continue
            best_dist = np.inf
            best_route = None
            for perm in itertools.permutations(combo):
                route = (0,) + perm + (0,)
                d = route_distance(route, coords)
                if d < best_dist:
                    best_dist = d
                    best_route = route
            subsets.append({'set': set(combo), 'route': best_route, 'dist': best_dist})

    full_set = set(customers)
    best_total_dist = np.inf
    best_solution = None

    # 2) DFS to cover all customers with subsets
    def dfs(chosen_indices, covered, cur_dist):
        nonlocal best_total_dist, best_solution
        if covered == full_set:
            if cur_dist < best_total_dist:
                best_total_dist = cur_dist
                best_solution = chosen_indices.copy()
            return
        if cur_dist >= best_total_dist:
            return
        # find first uncovered customer
        first_uncovered = next(c for c in customers if c not in covered)
        for idx, subset in enumerate(subsets):
            if first_uncovered not in subset['set']:
                continue
            if subset['set'] & covered:
                continue
            dfs(chosen_indices + [idx], covered | subset['set'], cur_dist + subset['dist'])

    dfs([], set(), 0.0)

    # 3) Build routes from best solution indices
    best_routes = [subsets[i]['route'] for i in best_solution]
    return best_routes, best_total_dist


if __name__ == '__main__':
    routes, best_distance = vrp_bruteforce(coords, demand, Q)
    print(f"Optimal total distance: {best_distance:.2f} km")

    # Visualization
    # 1) Route map
    plt.figure(figsize=(8, 6))
    # Plot depot
    plt.scatter(*coords[0], s=120, marker='s', c='black', label='Depot')
    plt.text(coords[0,0], coords[0,1], ' 0', fontsize=12, fontweight='bold')
    # Plot customers
    plt.scatter(coords[1:,0], coords[1:,1], s=80, c='blue', label='Customer')
    for i in range(1, n+1):
        plt.text(coords[i,0], coords[i,1], f' {i}', fontsize=10)
    # Plot routes
    colors = plt.cm.get_cmap('tab10')
    for k, route in enumerate(routes):
        xs, ys = zip(*(coords[list(route)]))
        plt.plot(xs, ys, '-o', linewidth=1.5, label=f'Vehicle {k+1}', color=colors(k))
    plt.title('Vehicle Routes')
    plt.xlabel('X (km)')
    plt.ylabel('Y (km)')
    plt.legend(loc='best')
    plt.grid(True)
    plt.show()

    # 2) Distance per vehicle bar chart
    distances = [route_distance(r, coords) for r in routes]
    plt.figure(figsize=(6,4))
    plt.bar(range(1, len(distances)+1), distances)
    plt.xlabel('Vehicle')
    plt.ylabel('Distance (km)')
    plt.title('Distance per Vehicle')
    plt.grid(axis='y')
    plt.show()

    # 3) Distance share pie chart
    plt.figure(figsize=(6,6))
    labels = [f'V{k+1}' for k in range(len(distances))]
    plt.pie(distances, labels=labels, autopct='%1.1f%%')
    plt.title('Distance Share by Vehicle')
    plt.show()
