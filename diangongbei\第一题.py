import pandas as pd
import numpy as np
import random

# 读取数据，路径请按实际修改
col_names = ['收集点编号', 'x', 'y', 'w', '其他列']
data = pd.read_excel(r'B题\\附件1.xlsx', skiprows=2, names=col_names)

points = data[['x', 'y']].values
weights = data['w'].values
n = len(points)

Q = 5.0  # 车辆最大载重
depot = np.array([0, 0])

all_points = np.vstack([depot, points])
dist_matrix = np.linalg.norm(all_points[:, None, :] - all_points[None, :, :], axis=2)

# 路径分割函数
def split_routes(chromosome, weights, Q):
    routes = []
    route = []
    load = 0
    for node in chromosome:
        w = weights[node - 1]
        if load + w > Q:
            routes.append(route)
            route = [node]
            load = w
        else:
            route.append(node)
            load += w
    if route:
        routes.append(route)
    return routes

# 距离计算函数
def calc_distance(routes, dist_matrix):
    total_dist = 0
    for route in routes:
        prev = 0
        for node in route:
            total_dist += dist_matrix[prev][node]
            prev = node
        total_dist += dist_matrix[prev][0]
    return total_dist

POP_SIZE = 100
GENS = 300
W = 0.8  # 惯性权重
C1 = 1.5  # 个体学习因子
C2 = 1.5  # 群体学习因子

# 适应度函数（距离越小越好）
def fitness(chromosome):
    routes = split_routes(chromosome, weights, Q)
    dist = calc_distance(routes, dist_matrix)
    return dist

# 生成一个随机排列
def random_permutation():
    base = list(range(1, n+1))
    random.shuffle(base)
    return base

# 交换操作：将perm1变为perm2所需的交换序列
def get_swap_sequence(perm1, perm2):
    perm1 = perm1.copy()
    swaps = []
    for i in range(len(perm1)):
        if perm1[i] != perm2[i]:
            idx = perm1.index(perm2[i], i)
            swaps.append((i, idx))
            perm1[i], perm1[idx] = perm1[idx], perm1[i]
    return swaps

# 应用交换序列到排列
def apply_swaps(perm, swaps):
    perm = perm.copy()
    for i, j in swaps:
        perm[i], perm[j] = perm[j], perm[i]
    return perm

class Particle:
    def __init__(self, position):
        self.position = position  # 当前解（排列）
        self.best_position = position.copy()
        self.best_fitness = fitness(position)
        self.velocity = []  # 交换操作序列

    def update_personal_best(self):
        fit = fitness(self.position)
        if fit < self.best_fitness:
            self.best_fitness = fit
            self.best_position = self.position.copy()

    def update_velocity(self, gbest_position):
        # 生成交换序列
        swaps_pbest = get_swap_sequence(self.position, self.best_position)
        swaps_gbest = get_swap_sequence(self.position, gbest_position)
        # 按比例随机选取部分交换操作
        v_new = []
        # 惯性部分
        v_new.extend(random.sample(self.velocity, int(W * len(self.velocity))) if self.velocity else [])
        # 个体部分
        v_new.extend(random.sample(swaps_pbest, int(C1 * random.random() * len(swaps_pbest))) if swaps_pbest else [])
        # 群体部分
        v_new.extend(random.sample(swaps_gbest, int(C2 * random.random() * len(swaps_gbest))) if swaps_gbest else [])
        self.velocity = v_new

    def move(self):
        self.position = apply_swaps(self.position, self.velocity)


def pso():
    particles = [Particle(random_permutation()) for _ in range(POP_SIZE)]
    gbest_position = min(particles, key=lambda p: p.best_fitness).best_position.copy()
    gbest_fitness = fitness(gbest_position)
    for gen in range(GENS):
        for p in particles:
            p.update_personal_best()
        # 更新全局最优
        current_gbest = min(particles, key=lambda p: p.best_fitness)
        if current_gbest.best_fitness < gbest_fitness:
            gbest_fitness = current_gbest.best_fitness
            gbest_position = current_gbest.best_position.copy()
        # 粒子更新
        for p in particles:
            p.update_velocity(gbest_position)
            p.move()
        if gen % 50 == 0:
            print(f"第{gen}代，最佳距离: {gbest_fitness:.2f}")
    return gbest_position

if __name__ == '__main__':
    best_solution = pso()
    best_routes = split_routes(best_solution, weights, Q)
    print("\n最优路径和车辆分配:")
    for i, route in enumerate(best_routes):
        route_str = ' -> '.join(str(r) for r in route)
        print(f"车辆{i+1}: 0 -> {route_str} -> 0")
    total_dist = calc_distance(best_routes, dist_matrix)
    print(f"总行驶距离: {total_dist:.2f}")